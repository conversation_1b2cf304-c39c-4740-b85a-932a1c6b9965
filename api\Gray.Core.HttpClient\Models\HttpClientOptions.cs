namespace Gray.Core.GrayHttpClient.Models
{
    /// <summary>
    /// HTTP客户端配置选项
    /// </summary>
    public class HttpClientOptions
    {
        /// <summary>
        /// 是否启用HTTP客户端
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 默认超时时间（秒）
        /// </summary>
        public int DefaultTimeout { get; set; } = 30;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// 重试延迟时间（秒）
        /// </summary>
        public int RetryDelaySeconds { get; set; } = 2;

        /// <summary>
        /// 是否启用日志
        /// </summary>
        public bool EnableLogging { get; set; } = true;

        /// <summary>
        /// 是否启用指标收集
        /// </summary>
        public bool EnableMetrics { get; set; } = true;

        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; } = "Information";

        /// <summary>
        /// 认证配置
        /// </summary>
        public AuthenticationOptions Authentication { get; set; } = new();

        /// <summary>
        /// API端点配置
        /// </summary>
        public Dictionary<string, ApiEndpointOptions> ApiEndpoints { get; set; } = new();

        /// <summary>
        /// Polly策略配置
        /// </summary>
        public PollyOptions Polly { get; set; } = new();

        /// <summary>
        /// 日志配置
        /// </summary>
        public LoggingOptions Logging { get; set; } = new();

        /// <summary>
        /// 缓存配置
        /// </summary>
        public CacheOptions Cache { get; set; } = new();

        /// <summary>
        /// 多系统认证配置
        /// </summary>
        public Dictionary<string, AuthenticationSystemOptions> AuthenticationSystems { get; set; } = new();
    }

    /// <summary>
    /// 认证配置选项
    /// </summary>
    public class AuthenticationOptions
    {
        /// <summary>
        /// 是否启用认证
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 认证类型（JWT、Basic等）
        /// </summary>
        public string Type { get; set; } = "JWT";

        /// <summary>
        /// Token刷新阈值（分钟）
        /// </summary>
        public int TokenRefreshThresholdMinutes { get; set; } = 5;

        /// <summary>
        /// 是否自动刷新Token
        /// </summary>
        public bool AutoRefreshToken { get; set; } = true;

        /// <summary>
        /// 登录端点
        /// </summary>
        public string LoginEndpoint { get; set; } = "/api/Login/GetJwtToken3";

        /// <summary>
        /// 刷新Token端点
        /// </summary>
        public string RefreshEndpoint { get; set; } = "/api/Login/RefreshToken";
    }

    /// <summary>
    /// API端点配置选项
    /// </summary>
    public class ApiEndpointOptions
    {
        /// <summary>
        /// 端点名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 基础URL
        /// </summary>
        public string BaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int Timeout { get; set; } = 30;

        /// <summary>
        /// 是否启用重试
        /// </summary>
        public bool EnableRetry { get; set; } = true;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// 是否启用熔断器
        /// </summary>
        public bool EnableCircuitBreaker { get; set; } = true;

        /// <summary>
        /// 熔断器失败阈值
        /// </summary>
        public int CircuitBreakerFailureThreshold { get; set; } = 5;

        /// <summary>
        /// 熔断器恢复时间（秒）
        /// </summary>
        public int CircuitBreakerRecoveryTimeSeconds { get; set; } = 30;

        /// <summary>
        /// 默认请求头
        /// </summary>
        public Dictionary<string, string> Headers { get; set; } = new();

        /// <summary>
        /// 关联的认证系统名称
        /// </summary>
        public string? AuthenticationSystem { get; set; }
    }

    /// <summary>
    /// Polly策略配置选项
    /// </summary>
    public class PollyOptions
    {
        /// <summary>
        /// 重试策略配置
        /// </summary>
        public RetryOptions Retry { get; set; } = new();

        /// <summary>
        /// 熔断器策略配置
        /// </summary>
        public CircuitBreakerOptions CircuitBreaker { get; set; } = new();

        /// <summary>
        /// 超时策略配置
        /// </summary>
        public TimeoutOptions Timeout { get; set; } = new();
    }

    /// <summary>
    /// 重试策略配置
    /// </summary>
    public class RetryOptions
    {
        /// <summary>
        /// 是否启用重试
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxAttempts { get; set; } = 3;

        /// <summary>
        /// 延迟时间（秒）
        /// </summary>
        public int DelaySeconds { get; set; } = 2;

        /// <summary>
        /// 退避倍数
        /// </summary>
        public double BackoffMultiplier { get; set; } = 2.0;

        /// <summary>
        /// 最大延迟时间（秒）
        /// </summary>
        public int MaxDelaySeconds { get; set; } = 30;

        /// <summary>
        /// 需要重试的HTTP状态码
        /// </summary>
        public List<int> RetryOnHttpStatusCodes { get; set; } = new() { 408, 429, 500, 502, 503, 504 };
    }

    /// <summary>
    /// 熔断器策略配置
    /// </summary>
    public class CircuitBreakerOptions
    {
        /// <summary>
        /// 是否启用熔断器
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 失败阈值
        /// </summary>
        public int FailureThreshold { get; set; } = 5;

        /// <summary>
        /// 采样持续时间（秒）
        /// </summary>
        public int SamplingDurationSeconds { get; set; } = 60;

        /// <summary>
        /// 最小吞吐量
        /// </summary>
        public int MinimumThroughput { get; set; } = 10;

        /// <summary>
        /// 熔断持续时间（秒）
        /// </summary>
        public int DurationOfBreakSeconds { get; set; } = 30;
    }

    /// <summary>
    /// 超时策略配置
    /// </summary>
    public class TimeoutOptions
    {
        /// <summary>
        /// 是否启用超时
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;
    }

    /// <summary>
    /// 日志配置选项
    /// </summary>
    public class LoggingOptions
    {
        /// <summary>
        /// 是否记录请求
        /// </summary>
        public bool LogRequests { get; set; } = true;

        /// <summary>
        /// 是否记录响应
        /// </summary>
        public bool LogResponses { get; set; } = true;

        /// <summary>
        /// 是否记录请求头
        /// </summary>
        public bool LogHeaders { get; set; } = false;

        /// <summary>
        /// 是否记录请求体
        /// </summary>
        public bool LogBody { get; set; } = false;

        /// <summary>
        /// 是否记录敏感数据
        /// </summary>
        public bool LogSensitiveData { get; set; } = false;

        /// <summary>
        /// 最大请求体日志长度
        /// </summary>
        public int MaxBodyLogLength { get; set; } = 1000;

        /// <summary>
        /// 敏感请求头列表
        /// </summary>
        public List<string> SensitiveHeaders { get; set; } = new() { "Authorization", "Cookie", "Set-Cookie" };
    }

    /// <summary>
    /// 缓存配置选项
    /// </summary>
    public class CacheOptions
    {
        /// <summary>
        /// 是否启用缓存
        /// </summary>
        public bool Enabled { get; set; } = false;

        /// <summary>
        /// 默认过期时间（分钟）
        /// </summary>
        public int DefaultExpirationMinutes { get; set; } = 5;

        /// <summary>
        /// 缓存键前缀
        /// </summary>
        public string CacheKeyPrefix { get; set; } = "GrayHttpClient:";

        /// <summary>
        /// 可缓存的HTTP方法
        /// </summary>
        public List<string> CacheableHttpMethods { get; set; } = new() { "GET" };

        /// <summary>
        /// 可缓存的状态码
        /// </summary>
        public List<int> CacheableStatusCodes { get; set; } = new() { 200, 201, 202 };
    }

    /// <summary>
    /// 认证系统配置选项
    /// </summary>
    public class AuthenticationSystemOptions
    {
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 系统名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 登录端点
        /// </summary>
        public string LoginEndpoint { get; set; } = "/api/auth/login";

        /// <summary>
        /// 刷新Token端点
        /// </summary>
        public string RefreshEndpoint { get; set; } = "/api/auth/refresh";

        /// <summary>
        /// 认证凭据
        /// </summary>
        public AuthenticationCredentials Credentials { get; set; } = new();

        /// <summary>
        /// Token字段映射
        /// </summary>
        public TokenMapping TokenMapping { get; set; } = new();

        /// <summary>
        /// 请求格式配置
        /// </summary>
        public RequestFormat RequestFormat { get; set; } = new();

        /// <summary>
        /// 是否自动刷新Token
        /// </summary>
        public bool AutoRefreshToken { get; set; } = true;

        /// <summary>
        /// Token刷新阈值（分钟）
        /// </summary>
        public int TokenRefreshThresholdMinutes { get; set; } = 5;
    }

    /// <summary>
    /// 认证凭据
    /// </summary>
    public class AuthenticationCredentials
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// 客户端密钥
        /// </summary>
        public string? ClientSecret { get; set; }

        /// <summary>
        /// API密钥
        /// </summary>
        public string? ApiKey { get; set; }

        /// <summary>
        /// 其他自定义参数
        /// </summary>
        public Dictionary<string, string> CustomParameters { get; set; } = new();
    }

    /// <summary>
    /// Token字段映射
    /// </summary>
    public class TokenMapping
    {
        /// <summary>
        /// 访问Token字段名
        /// </summary>
        public string AccessTokenField { get; set; } = "access_token";

        /// <summary>
        /// 刷新Token字段名
        /// </summary>
        public string RefreshTokenField { get; set; } = "refresh_token";

        /// <summary>
        /// 过期时间字段名
        /// </summary>
        public string ExpiresInField { get; set; } = "expires_in";

        /// <summary>
        /// Token类型字段名
        /// </summary>
        public string TokenTypeField { get; set; } = "token_type";
    }

    /// <summary>
    /// 请求格式配置
    /// </summary>
    public class RequestFormat
    {
        /// <summary>
        /// HTTP方法
        /// </summary>
        public string Method { get; set; } = "POST";

        /// <summary>
        /// 内容类型
        /// </summary>
        public string ContentType { get; set; } = "application/json";

        /// <summary>
        /// 请求体模板
        /// </summary>
        public Dictionary<string, object> BodyTemplate { get; set; } = new();

        /// <summary>
        /// 请求头模板
        /// </summary>
        public Dictionary<string, string> HeaderTemplate { get; set; } = new();
    }
}
