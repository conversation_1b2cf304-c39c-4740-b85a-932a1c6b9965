/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "动平衡扫描记录",TableName = "DY_BarCodeScanLog",DBServer = "JL_DYDbContext")]
    public partial class DY_BarCodeScanLog:JL_DYEntity
    {
        /// <summary>
       ///机台名
       /// </summary>
       [Display(Name ="机台名")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string DBMachine { get; set; }

       /// <summary>
       ///机台Id
       /// </summary>
       [Display(Name ="机台Id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid DBMachineId { get; set; }

       /// <summary>
       ///扫描胎号
       /// </summary>
       [Display(Name ="扫描胎号")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string Tyreid { get; set; }

       /// <summary>
       ///扫描时间
       /// </summary>
       [Display(Name ="扫描时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public DateTime ScanTime { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Key]
       [Display(Name ="BarcodeScanId")]
       [Column(TypeName="bigint")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public long BarcodeScanId { get; set; }

       
    }
}