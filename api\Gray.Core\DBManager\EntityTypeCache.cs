using Gray.Entity.SystemModels;
using Microsoft.Extensions.DependencyModel;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;

namespace Gray.Core.DBManager
{
    /// <summary>
    /// 提供实体类型的缓存和数据库类型查询功能
    /// </summary>
    public static class EntityTypeCache
    {
        private static readonly ConcurrentDictionary<string, Type> _entityTypes =
            new ConcurrentDictionary<string, Type>(StringComparer.OrdinalIgnoreCase);
        private static readonly Dictionary<string, Type> DbEntityTypes = new Dictionary<string, Type>();
        private static readonly ConcurrentDictionary<string, FileSystemWatcher> _watchers =
            new ConcurrentDictionary<string, FileSystemWatcher>();

        private static readonly ConcurrentDictionary<string, List<Type>> _entityTypesByAssembly
          = new ConcurrentDictionary<string, List<Type>>();

        /// <summary>
        /// 获取所有已缓存的实体类型
        /// </summary>
        public static IReadOnlyDictionary<string, Type> EntityTypes => _entityTypes;

        static EntityTypeCache()
        {
            InitializeEntityTypes();
            //SetupFileWatchers();
        }

        /// <summary>
        /// 初始化实体类型缓存
        /// </summary>
        private static void InitializeEntityTypes()
        {
            try
            {
                _entityTypes.Clear();
                _entityTypesByAssembly.Clear();
                DbEntityTypes.Clear();
                LoadEntityTypesFromAssemblies();
                InitDbEntityType();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化实体类型缓存失败: {ex.Message}");
                throw new InvalidOperationException("初始化实体类型缓存失败", ex);
            }
        }
        /// <summary>
        /// 缓存分库model基类
        /// </summary>
        private static void InitDbEntityType()
        {
            var compilationLibrary = DependencyContext
                 .Default
                 .CompileLibraries
                 .Where(x => x.Name.EndsWith(".Entity") && !x.Serviceable && x.Type != "package" && x.Type == "project");

            foreach (var _compilation in compilationLibrary)
            {
                foreach (var item in AssemblyLoadContext.Default
                    .LoadFromAssemblyName(new AssemblyName(_compilation.Name))
                    .GetTypes()
                    .Where(x => x.GetTypeInfo().BaseType != null && x.BaseType == (typeof(BaseEntity))))
                {
                    DbEntityTypes[item.Name] = item;
                }
            }
        }
        private static void LoadEntityTypesFromAssemblies()
        {
            // 获取所有实体相关的程序集
            var compilationLibraries = DependencyContext
                .Default
                .CompileLibraries
                .Where(x => (x.Name.EndsWith(".Entity") || x.Name.Contains(".Entity.")) &&
                           !x.Serviceable &&
                           x.Type == "project")
                .ToList();

            if (compilationLibraries.Count == 0)
            {
                Console.WriteLine("警告: 未找到任何实体程序集");
            }

            foreach (var compilation in compilationLibraries)
            {
                try
                {
                    LoadTypesFromAssembly(compilation.Name);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"加载程序集 {compilation.Name} 失败: {ex.Message}");
                }
            }
        }
        /// <summary>
        /// 根据分库名称获取分库model基类
        /// </summary>
        public static Type GetDbEntityType(string dbService)
        {
            string name = dbService?.Replace("DbContext", "");
            return DbEntityTypes.TryGetValue($"{name}Entity", out var entityType)
                ? entityType
                : null;
        }
        public static void LoadTypesFromAssembly(string assemblyName)
        {
            if (string.IsNullOrEmpty(assemblyName))
            {
                throw new ArgumentNullException(nameof(assemblyName));
            }

            try
            {
                var assembly = Assembly.Load(assemblyName);
                var entityTypes = assembly.GetTypes()
                    .Where(t => t.IsClass
                           && !t.IsAbstract
                           && t.BaseType != null
                           && (t.BaseType.Name.EndsWith("Entity") || t.BaseType.Name == "BaseEntity"))
                    .ToList();

                if (entityTypes.Count > 0)
                {
                    _entityTypesByAssembly.AddOrUpdate(assemblyName, entityTypes, (_, __) => entityTypes);

                    // 同时更新主缓存
                    foreach (var type in entityTypes)
                    {
                        _entityTypes.TryAdd(type.Name, type);
                    }

                    Console.WriteLine($"已加载程序集 {assemblyName} 中的 {entityTypes.Count} 个实体类型");
                }
                else
                {
                    Console.WriteLine($"程序集 {assemblyName} 中未找到实体类型");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载程序集 {assemblyName} 中的实体类型失败: {ex.Message}");
                throw;
            }
        }

        public static IEnumerable<Type> GetEntityTypes(Type baseType)
        {
            if (baseType == null)
            {
                throw new ArgumentNullException(nameof(baseType));
            }

            return _entityTypesByAssembly.Values
                .SelectMany(types => types)
                .Where(t => t.BaseType == baseType)
                .ToList(); // 返回列表副本，避免并发修改问题
        }

        /// <summary>
        /// 设置文件监视器以实现热重载
        /// </summary>
        private static void SetupFileWatchers()
        {
            try
            {
                var basePath = AppDomain.CurrentDomain.BaseDirectory;
                var entityAssemblies = Directory.GetFiles(basePath, "*.Entity.dll");

                foreach (var assembly in entityAssemblies)
                {
                    var directoryPath = Path.GetDirectoryName(assembly);
                    var fileName = Path.GetFileName(assembly);

                    var watcher = new FileSystemWatcher(directoryPath, fileName)
                    {
                        NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.CreationTime,
                        EnableRaisingEvents = true
                    };

                    watcher.Changed += (s, e) => OnAssemblyChanged(e.FullPath);
                    _watchers.TryAdd(assembly, watcher);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置文件监视器失败: {ex.Message}");
            }
        }

        private static void OnAssemblyChanged(string assemblyPath)
        {
            try
            {
                // 等待文件解锁
                System.Threading.Thread.Sleep(1000);

                var assemblyName = Path.GetFileNameWithoutExtension(assemblyPath);
                Console.WriteLine($"检测到程序集变更: {assemblyName}");

                // 重新加载特定程序集的类型
                LoadTypesFromAssembly(assemblyName);

                Console.WriteLine($"已重新加载实体类型，当前缓存数量: {_entityTypes.Count}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"重新加载程序集失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 手动触发重新加载所有实体类型
        /// </summary>
        public static void ReloadEntityTypes()
        {
            InitializeEntityTypes();
        }

        /// <summary>
        /// 更新或添加单个实体类型
        /// </summary>
        /// <param name="entityName">实体名称</param>
        /// <param name="entityType">实体类型</param>
        public static bool UpdateEntityType(string entityName, Type entityType)
        {
            //if (entityType.GetTypeInfo().BaseType != typeof(BaseEntity))
            //{
            //    throw new ArgumentException("提供的类型必须继承自BaseEntity");
            //}
            return _entityTypes.AddOrUpdate(entityName, entityType, (_, __) => entityType) != null;
        }

        /// <summary>
        /// 根据数据库上下文名称获取对应的实体类型
        /// </summary>
        /// <param name="dbContextName">数据库上下文名称,例如"UserDbContext"</param>
        /// <returns>实体类型,如果未找到则返回null</returns>
        public static Type GetEntityType(string dbContextName)
        {
            if (string.IsNullOrEmpty(dbContextName))
            {
                return null;
            }

            string entityName = $"{dbContextName.Replace("DbContext", "")}Entity";
            return _entityTypes.TryGetValue(entityName, out var entityType)
                ? entityType
                : null;
        }

        /// <summary>
        /// 从指定程序集加载满足自定义条件的实体类型
        /// </summary>
        /// <param name="assemblyName">程序集名称</param>
        /// <param name="predicate">用于筛选实体类型的条件</param>
        public static void LoadCustomTypesFromAssembly(string assemblyName, Func<Type, bool> predicate)
        {
            if (string.IsNullOrEmpty(assemblyName))
            {
                throw new ArgumentNullException(nameof(assemblyName));
            }

            if (predicate == null)
            {
                throw new ArgumentNullException(nameof(predicate));
            }

            try
            {
                var assembly = Assembly.Load(assemblyName);
                var entityTypes = assembly.GetTypes()
                    .Where(t => t.IsClass && !t.IsAbstract && predicate(t))
                    .ToList();

                if (entityTypes.Count > 0)
                {
                    // 获取现有的类型列表或创建新列表
                    var existingTypes = _entityTypesByAssembly.GetOrAdd(assemblyName, new List<Type>());
                    
                    // 添加新的类型到列表中
                    foreach (var type in entityTypes)
                    {
                        if (!existingTypes.Contains(type))
                        {
                            existingTypes.Add(type);
                            _entityTypes.TryAdd(type.Name, type);
                        }
                    }

                    Console.WriteLine($"已加载程序集 {assemblyName} 中的 {entityTypes.Count} 个自定义实体类型");
                }
                else
                {
                  //  Console.WriteLine($"程序集 {assemblyName} 中未找到符合条件的自定义实体类型");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载程序集 {assemblyName} 中的自定义实体类型失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 批量从指定程序集加载满足自定义条件的实体类型
        /// </summary>
        /// <param name="assemblyNames">程序集名称集合</param>
        /// <param name="predicate">用于筛选实体类型的条件</param>
        public static void LoadCustomTypesFromAssemblies(IEnumerable<string> assemblyNames, Func<Type, bool> predicate)
        {
            if (assemblyNames == null)
            {
                throw new ArgumentNullException(nameof(assemblyNames));
            }

            foreach (var assemblyName in assemblyNames)
            {
                try
                {
                    LoadCustomTypesFromAssembly(assemblyName, predicate);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"加载程序集 {assemblyName} 失败: {ex.Message}");
                    // 继续处理其他程序集
                }
            }
        }

        /// <summary>
        /// 示例：在执行LoadTypesFromAssembly后继续添加实体
        /// 此方法可以在应用程序启动时调用，以加载额外的实体类型
        /// </summary>
        /// <param name="assemblyName">程序集名称</param>
        /// <param name="baseEntityType">基础实体类型</param>
        public static void LoadAdditionalEntities(string assemblyName, Type baseEntityType)
        {
            if (string.IsNullOrEmpty(assemblyName))
            {
                throw new ArgumentNullException(nameof(assemblyName));
            }

            if (baseEntityType == null)
            {
                throw new ArgumentNullException(nameof(baseEntityType));
            }

            try
            {
                // 首先确保已经加载了基本实体
                if (!_entityTypesByAssembly.ContainsKey(assemblyName))
                {
                    LoadTypesFromAssembly(assemblyName);
                }

                // 然后加载额外的实体类型，例如那些可能不是直接继承自BaseEntity的类型
                // 但仍然需要被注册到DbContext中
                LoadCustomTypesFromAssembly(assemblyName, type => 
                    type.IsClass && 
                    !type.IsAbstract && 
                    type.BaseType != null && 
                    type.BaseType != baseEntityType && // 排除已经加载的基本实体
                    type.GetInterfaces().Any(i => i.Name == "IEntity") // 假设有一个IEntity接口标记所有实体
                );

                Console.WriteLine($"已完成程序集 {assemblyName} 中额外实体类型的加载");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载程序集 {assemblyName} 中的额外实体类型失败: {ex.Message}");
                throw;
            }
        }

    }
}