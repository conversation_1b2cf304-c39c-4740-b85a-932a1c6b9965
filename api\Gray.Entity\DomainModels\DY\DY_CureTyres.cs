/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "动平衡硫化记录",TableName = "DY_CureTyres",DBServer = "JL_DYDbContext")]
    public partial class DY_CureTyres:JL_DYEntity
    {
        /// <summary>
       ///胎号
       /// </summary>
       [Display(Name ="胎号")]
       [MaxLength(22)]
       [Column(TypeName="nvarchar(22)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string TyreBarcode { get; set; }

       /// <summary>
       ///胎号id
       /// </summary>
       [Display(Name ="胎号id")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int TyreBarcodeid { get; set; }

       /// <summary>
       ///代码
       /// </summary>
       [Display(Name ="代码")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string TyreCode { get; set; }

       /// <summary>
       ///规格
       /// </summary>
       [Display(Name ="规格")]
       [MaxLength(500)]
       [Column(TypeName="nvarchar(500)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string TyreSPEC { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Key]
       [Display(Name ="CureTyreId")]
       [Column(TypeName="bigint")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public long CureTyreId { get; set; }

       /// <summary>
       ///同步时间
       /// </summary>
       [Display(Name ="同步时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public DateTime SyncTime { get; set; }

       /// <summary>
       ///硫化时间
       /// </summary>
       [Display(Name ="硫化时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public DateTime CureTime { get; set; }

       /// <summary>
       ///硫化操作员
       /// </summary>
       [Display(Name ="硫化操作员")]
       [MaxLength(255)]
       [Column(TypeName="nvarchar(255)")]
       [Editable(true)]
       public string CureOP { get; set; }

       
    }
}