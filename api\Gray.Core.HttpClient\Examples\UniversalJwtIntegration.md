# 通用JWT认证集成指南

现在您只需要通过配置就能快速对接任何基于JWT认证的外部系统！

## 🚀 快速开始

### 1. 服务注册（一次性配置）

在 `Program.cs` 中注册服务：

```csharp
using Gray.Core.HttpClient.Extensions;

// 注册HTTP客户端服务（包含通用JWT认证支持）
builder.Services.AddHttpClientServices(builder.Configuration);
```

### 2. 配置新系统（只需配置，无需写代码）

在 `gray.core.httpclient.json` 中添加新系统配置：

```json
{
  "HttpClient": {
    "Enabled": true,
    
    // 认证系统配置 - 这里配置您要对接的系统
    "AuthenticationSystems": {
      "YourNewSystem": {
        "Enabled": true,
        "Name": "您的新系统",
        "LoginEndpoint": "/api/auth/login",
        "RefreshEndpoint": "/api/auth/refresh",
        "Credentials": {
          "Username": "your_username",
          "Password": "your_password"
        },
        "TokenMapping": {
          "AccessTokenField": "access_token",
          "RefreshTokenField": "refresh_token",
          "ExpiresInField": "expires_in"
        },
        "RequestFormat": {
          "Method": "POST",
          "ContentType": "application/json",
          "BodyTemplate": {
            "username": "{Username}",
            "password": "{Password}",
            "grant_type": "password"
          }
        },
        "AutoRefreshToken": true,
        "TokenRefreshThresholdMinutes": 5
      }
    },

    // API端点配置 - 关联到认证系统
    "ApiEndpoints": {
      "YourNewSystem": {
        "Name": "您的新系统API",
        "BaseUrl": "https://your-new-system.com",
        "Timeout": 30,
        "AuthenticationSystem": "YourNewSystem",
        "EnableRetry": true,
        "MaxRetryAttempts": 3
      }
    }
  }
}
```

### 3. 使用（自动认证）

```csharp
public class YourBusinessService
{
    private readonly IHttpClientManager _clientManager;

    public YourBusinessService(IHttpClientManager clientManager)
    {
        _clientManager = clientManager;
    }

    public async Task CallNewSystemApi()
    {
        // 获取HttpClient，JWT认证会自动处理
        var httpClient = _clientManager.GetHttpClient("YourNewSystem");
        
        // 直接调用API，Token会自动添加到请求头
        var response = await httpClient.GetAsync("/api/data");
        
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            // 处理响应...
        }
    }
}
```

## 🎯 支持的认证方式

### 1. 用户名密码认证

```json
{
  "YourSystem": {
    "Credentials": {
      "Username": "admin",
      "Password": "password123"
    },
    "RequestFormat": {
      "BodyTemplate": {
        "username": "{Username}",
        "password": "{Password}",
        "grant_type": "password"
      }
    }
  }
}
```

### 2. 客户端凭据认证

```json
{
  "YourSystem": {
    "Credentials": {
      "ClientId": "your_client_id",
      "ClientSecret": "your_client_secret"
    },
    "RequestFormat": {
      "ContentType": "application/x-www-form-urlencoded",
      "BodyTemplate": {
        "grant_type": "client_credentials",
        "client_id": "{ClientId}",
        "client_secret": "{ClientSecret}"
      }
    }
  }
}
```

### 3. API密钥认证

```json
{
  "YourSystem": {
    "Credentials": {
      "ApiKey": "your_api_key"
    },
    "RequestFormat": {
      "HeaderTemplate": {
        "X-API-Key": "{ApiKey}"
      },
      "BodyTemplate": {
        "api_key": "{ApiKey}"
      }
    }
  }
}
```

### 4. 自定义认证

```json
{
  "YourSystem": {
    "Credentials": {
      "CustomParameters": {
        "TenantId": "your_tenant_id",
        "AppId": "your_app_id"
      }
    },
    "RequestFormat": {
      "BodyTemplate": {
        "tenant_id": "{TenantId}",
        "app_id": "{AppId}",
        "grant_type": "custom"
      }
    }
  }
}
```

## 🔧 配置说明

### TokenMapping（Token字段映射）

不同系统返回的Token字段名可能不同，通过映射配置适配：

```json
{
  "TokenMapping": {
    "AccessTokenField": "token",        // 有些系统叫 "token"
    "RefreshTokenField": "refreshToken", // 有些系统叫 "refreshToken"
    "ExpiresInField": "expiresIn",      // 有些系统叫 "expiresIn"
    "TokenTypeField": "tokenType"       // 有些系统叫 "tokenType"
  }
}
```

### RequestFormat（请求格式）

支持不同的请求格式：

```json
{
  "RequestFormat": {
    "Method": "POST",                           // GET, POST, PUT等
    "ContentType": "application/json",          // 或 "application/x-www-form-urlencoded"
    "BodyTemplate": {                          // 请求体模板
      "username": "{Username}",
      "password": "{Password}"
    },
    "HeaderTemplate": {                        // 请求头模板
      "X-Custom-Header": "custom_value"
    }
  }
}
```

## 💡 实际使用示例

### 示例1：对接钉钉API

```json
{
  "AuthenticationSystems": {
    "DingTalk": {
      "Enabled": true,
      "Name": "钉钉开放平台",
      "LoginEndpoint": "/gettoken",
      "Credentials": {
        "ClientId": "your_app_key",
        "ClientSecret": "your_app_secret"
      },
      "TokenMapping": {
        "AccessTokenField": "access_token"
      },
      "RequestFormat": {
        "Method": "GET",
        "BodyTemplate": {
          "appkey": "{ClientId}",
          "appsecret": "{ClientSecret}"
        }
      }
    }
  },
  "ApiEndpoints": {
    "DingTalk": {
      "BaseUrl": "https://oapi.dingtalk.com",
      "AuthenticationSystem": "DingTalk"
    }
  }
}
```

### 示例2：对接企业微信API

```json
{
  "AuthenticationSystems": {
    "WeWork": {
      "Enabled": true,
      "Name": "企业微信",
      "LoginEndpoint": "/cgi-bin/gettoken",
      "Credentials": {
        "ClientId": "your_corpid",
        "ClientSecret": "your_corpsecret"
      },
      "TokenMapping": {
        "AccessTokenField": "access_token"
      },
      "RequestFormat": {
        "Method": "GET",
        "BodyTemplate": {
          "corpid": "{ClientId}",
          "corpsecret": "{ClientSecret}"
        }
      }
    }
  }
}
```

## ✨ 自动功能

- ✅ **自动获取Token**：首次调用时自动登录获取Token
- ✅ **自动刷新Token**：Token即将过期时自动刷新
- ✅ **自动重试**：401错误时自动刷新Token并重试
- ✅ **自动缓存**：Token缓存管理，避免重复获取
- ✅ **多系统隔离**：不同系统的Token互不影响
- ✅ **错误处理**：完善的异常处理和日志记录

## 🎉 总结

现在您只需要：

1. **一次性注册服务**（在Program.cs中）
2. **配置认证信息**（在配置文件中）
3. **直接使用HttpClient**（系统自动处理认证）

无需为每个新系统编写认证代码，真正做到了配置驱动的JWT认证集成！
