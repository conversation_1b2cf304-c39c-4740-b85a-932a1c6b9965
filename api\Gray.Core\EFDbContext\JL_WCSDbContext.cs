﻿using Gray.Core.DBManager;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.SystemModels;
using Microsoft.EntityFrameworkCore;
using System;

namespace Gray.Core.EFDbContext
{
    public class JL_WCSDbContext : BaseDbContext<JL_WCSDbContext>, IDependency
    {
        protected override string ConnectionString
        {
            get
            {
                return DBServerProvider.GetConnection(this.GetType().Name);
            }
        }

        public JL_WCSDbContext() : base()
        {
        }

        public JL_WCSDbContext(DbContextOptions<JL_WCSDbContext> options) : base(options)
        {
        }

       

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.UseDbTypeFromConfig(optionsBuilder, this.GetType().Name);
            //默认禁用实体跟踪
            optionsBuilder = optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            base.OnConfiguring(optionsBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
        }

        protected override Type GetBaseEntityType()
        {
            // 默认返回null，由派生类实现
            return typeof(JL_WCSEntity);
        }
    }
}