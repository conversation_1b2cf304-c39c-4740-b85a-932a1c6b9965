using System;

namespace Gray.Core.Logging
{
    /// <summary>
    /// SQL日志记录配置选项
    /// </summary>
    public class SqlLoggingOptions
    {
        /// <summary>
        /// 是否启用SQL日志记录（默认：false）
        /// </summary>
        public bool Enabled { get; set; } = false;

        /// <summary>
        /// 是否记录SQL执行前的日志（默认：true）
        /// </summary>
        public bool LogExecuting { get; set; } = true;

        /// <summary>
        /// 是否记录SQL执行完成的日志（默认：true）
        /// </summary>
        public bool LogExecuted { get; set; } = true;

        /// <summary>
        /// 是否记录SQL执行错误的日志（默认：true）
        /// </summary>
        public bool LogErrors { get; set; } = true;

        /// <summary>
        /// 是否包含SQL命令文本（默认：true）
        /// 注意：生产环境可能包含敏感信息
        /// </summary>
        public bool IncludeCommandText { get; set; } = true;

        /// <summary>
        /// 是否包含SQL参数（默认：false）
        /// 注意：生产环境可能包含敏感信息
        /// </summary>
        public bool IncludeParameters { get; set; } = false;

        /// <summary>
        /// 是否包含执行结果（默认：false）
        /// 注意：可能包含大量数据
        /// </summary>
        public bool IncludeResult { get; set; } = false;

        /// <summary>
        /// 是否使用结构化日志（默认：true）
        /// true: 使用Microsoft.Extensions.Logging的结构化日志
        /// false: 使用JSON格式的字符串日志
        /// </summary>
        public bool UseStructuredLogging { get; set; } = true;

        /// <summary>
        /// 是否写入文件日志（默认：false）
        /// </summary>
        public bool WriteToFile { get; set; } = false;

        /// <summary>
        /// 文件日志路径（默认：Log/Sql/）
        /// </summary>
        public string LogFilePath { get; set; } = "Log/Sql/";

        /// <summary>
        /// 慢查询阈值（毫秒，默认：1000ms）
        /// 超过此时间的查询会被标记为慢查询
        /// </summary>
        public int SlowQueryThresholdMs { get; set; } = 1000;

        /// <summary>
        /// 是否只记录慢查询（默认：false）
        /// </summary>
        public bool OnlyLogSlowQueries { get; set; } = false;

        /// <summary>
        /// 最大SQL文本长度（默认：4000字符）
        /// 超过此长度的SQL会被截断
        /// </summary>
        public int MaxSqlTextLength { get; set; } = 4000;

        /// <summary>
        /// 是否记录连接信息（默认：false）
        /// </summary>
        public bool IncludeConnectionInfo { get; set; } = false;

        /// <summary>
        /// 日志级别过滤器
        /// </summary>
        public SqlLogLevel LogLevel { get; set; } = SqlLogLevel.Information;

        /// <summary>
        /// 创建开发环境配置
        /// </summary>
        public static SqlLoggingOptions Development => new SqlLoggingOptions
        {
            Enabled = true,
            LogExecuting = true,
            LogExecuted = true,
            LogErrors = true,
            IncludeCommandText = true,
            IncludeParameters = true,
            IncludeResult = false,
            UseStructuredLogging = true,
            WriteToFile = true,
            OnlyLogSlowQueries = false,
            SlowQueryThresholdMs = 500,
            IncludeConnectionInfo = true,
            LogLevel = SqlLogLevel.Debug
        };

        /// <summary>
        /// 创建生产环境配置
        /// </summary>
        public static SqlLoggingOptions Production => new SqlLoggingOptions
        {
            Enabled = true,
            LogExecuting = false,
            LogExecuted = true,
            LogErrors = true,
            IncludeCommandText = false,
            IncludeParameters = false,
            IncludeResult = false,
            UseStructuredLogging = true,
            WriteToFile = true,
            OnlyLogSlowQueries = true,
            SlowQueryThresholdMs = 2000,
            IncludeConnectionInfo = false,
            LogLevel = SqlLogLevel.Warning
        };

        /// <summary>
        /// 创建性能监控配置
        /// </summary>
        public static SqlLoggingOptions Performance => new SqlLoggingOptions
        {
            Enabled = true,
            LogExecuting = false,
            LogExecuted = true,
            LogErrors = true,
            IncludeCommandText = true,
            IncludeParameters = false,
            IncludeResult = false,
            UseStructuredLogging = true,
            WriteToFile = true,
            OnlyLogSlowQueries = true,
            SlowQueryThresholdMs = 1000,
            IncludeConnectionInfo = false,
            LogLevel = SqlLogLevel.Information
        };

        /// <summary>
        /// 创建调试配置
        /// </summary>
        public static SqlLoggingOptions Debug => new SqlLoggingOptions
        {
            Enabled = true,
            LogExecuting = true,
            LogExecuted = true,
            LogErrors = true,
            IncludeCommandText = true,
            IncludeParameters = true,
            IncludeResult = true,
            UseStructuredLogging = false,
            WriteToFile = true,
            OnlyLogSlowQueries = false,
            SlowQueryThresholdMs = 100,
            IncludeConnectionInfo = true,
            LogLevel = SqlLogLevel.Debug
        };
    }

    /// <summary>
    /// SQL日志级别
    /// </summary>
    public enum SqlLogLevel
    {
        Debug = 0,
        Information = 1,
        Warning = 2,
        Error = 3
    }
}
