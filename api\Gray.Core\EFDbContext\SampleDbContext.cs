using Gray.Core.DBManager;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Reflection;

namespace Gray.Core.EFDbContext
{
    /// <summary>
    /// 示例数据库上下文，展示如何使用扩展的实体加载功能
    /// </summary>
    public class SampleDbContext : BaseDbContext<SampleDbContext>
    {
        // 假设这是我们的基础实体类型
        private static readonly Type _baseEntityType = typeof(SampleEntity);
        
        // 示例接口，用于标记需要加载的额外实体
        private interface IAdditionalEntity { }
        
        public SampleDbContext(DbContextOptions<SampleDbContext> options) : base(options)
        {
            // 在构造函数中，我们可以确保额外的实体类型已经被加载
            EnsureAdditionalEntitiesLoaded();
        }
        
        /// <summary>
        /// 确保额外的实体类型已经被加载到缓存中
        /// </summary>
        private void EnsureAdditionalEntitiesLoaded()
        {
            // 获取包含实体的程序集
            var entityAssemblies = AppDomain.CurrentDomain.GetAssemblies()
                .Where(a => a.GetName().Name.EndsWith(".Entity") || a.GetName().Name.Contains(".Entity."))
                .Select(a => a.GetName().Name)
                .ToList();
                
            // 对每个程序集，加载额外的实体
            foreach (var assemblyName in entityAssemblies)
            {
                try
                {
                    // 加载实现了IAdditionalEntity接口的类型
                    EntityTypeCache.LoadCustomTypesFromAssembly(
                        assemblyName, 
                        type => type.IsClass && 
                               !type.IsAbstract && 
                               typeof(IAdditionalEntity).IsAssignableFrom(type)
                    );
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"加载额外实体失败: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// 覆盖基类方法，提供特定的基础实体类型
        /// </summary>
        protected override Type GetBaseEntityType()
        {
            return _baseEntityType;
        }
        
        /// <summary>
        /// 覆盖基类方法，加载额外的实体类型
        /// </summary>
        protected override void LoadAdditionalEntitiesFromCache(ModelBuilder modelBuilder)
        {
            // 获取所有实现了IAdditionalEntity接口的类型
            var additionalEntityTypes = AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(a => a.GetTypes())
                .Where(t => t.IsClass && 
                           !t.IsAbstract && 
                           typeof(IAdditionalEntity).IsAssignableFrom(t))
                .ToList();
                
            // 将这些类型注册到模型构建器
            foreach (var entityType in additionalEntityTypes)
            {
                modelBuilder.Entity(entityType);
                Console.WriteLine($"已注册额外实体: {entityType.Name}");
            }
        }
    }
    
    /// <summary>
    /// 示例基础实体类
    /// </summary>
    public class SampleEntity
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
} 