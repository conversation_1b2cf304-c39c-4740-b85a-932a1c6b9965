namespace Gray.Core.GrayHttpClient.Services
{
    /// <summary>
    /// Token管理器接口
    /// </summary>
    public interface ITokenManager
    {
        /// <summary>
        /// 获取当前访问Token
        /// </summary>
        /// <returns>访问Token</returns>
        Task<string?> GetAccessTokenAsync();

        /// <summary>
        /// 设置Token
        /// </summary>
        /// <param name="accessToken">访问Token</param>
        /// <param name="refreshToken">刷新Token</param>
        /// <param name="expiresIn">过期时间（秒）</param>
        Task SetTokenAsync(string accessToken, string? refreshToken = null, int? expiresIn = null);

        /// <summary>
        /// 刷新Token
        /// </summary>
        /// <returns>是否刷新成功</returns>
        Task<bool> RefreshTokenAsync();

        /// <summary>
        /// 清除Token
        /// </summary>
        Task ClearTokenAsync();

        /// <summary>
        /// 检查Token是否即将过期
        /// </summary>
        /// <returns>是否即将过期</returns>
        Task<bool> IsTokenExpiringSoonAsync();

        /// <summary>
        /// 检查Token是否有效
        /// </summary>
        /// <returns>是否有效</returns>
        Task<bool> IsTokenValidAsync();
    }
}
