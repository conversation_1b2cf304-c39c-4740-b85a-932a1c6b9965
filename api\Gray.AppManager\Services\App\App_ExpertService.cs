/*
Tel:15662337743
 *所有业务编写全部应在Partial文件夹下App_ExpertService与IApp_ExpertService中编写
 */
using Gray.AppManager.IRepositories;
using Gray.AppManager.IServices;
using Gray.Core.BaseProvider;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.DomainModels;

namespace Gray.AppManager.Services
{
    public partial class App_ExpertService : ServiceBase<App_Expert, IApp_ExpertRepository>, IApp_ExpertService, IDependency
    {
        public App_ExpertService(IApp_ExpertRepository repository)
             : base(repository) 
        { 
           Init(repository);
        }
        public static IApp_ExpertService Instance
        {
           get { return AutofacContainerModule.GetService<IApp_ExpertService>(); }
        }
    }
}
