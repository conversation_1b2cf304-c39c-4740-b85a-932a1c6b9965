using Gray.Core.GrayHttpClient.Models;
using Gray.Core.GrayHttpClient.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;

namespace Gray.Core.GrayHttpClient.Handlers
{
    /// <summary>
    /// 通用认证处理器
    /// 根据配置自动为不同系统添加JWT认证
    /// </summary>
    public class UniversalAuthenticationHandler : DelegatingHandler
    {
        private readonly IUniversalTokenManager _tokenManager;
        private readonly ILogger<UniversalAuthenticationHandler> _logger;
        private readonly HttpClientOptions _options;
        private readonly string _systemName;

        public UniversalAuthenticationHandler(
            IUniversalTokenManager tokenManager,
            ILogger<UniversalAuthenticationHandler> logger,
            IOptions<HttpClientOptions> options,
            string systemName)
        {
            _tokenManager = tokenManager;
            _logger = logger;
            _options = options.Value;
            _systemName = systemName;
        }

        /// <summary>
        /// 发送HTTP请求前的处理
        /// </summary>
        protected override async Task<HttpResponseMessage> SendAsync(
            HttpRequestMessage request, 
            CancellationToken cancellationToken)
        {
            // 检查是否需要认证
            if (!_tokenManager.IsSystemConfigured(_systemName))
            {
                _logger.LogDebug("系统 {SystemName} 未配置认证，直接发送请求", _systemName);
                return await base.SendAsync(request, cancellationToken);
            }

            // 添加认证头
            await AddAuthenticationHeaderAsync(request);

            // 发送请求
            var response = await base.SendAsync(request, cancellationToken);

            // 如果返回401未授权，尝试刷新Token并重试
            if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                _logger.LogWarning("系统 {SystemName} 收到401未授权响应，尝试刷新Token并重试", _systemName);

                // 尝试刷新Token
                var refreshSuccess = await _tokenManager.RefreshTokenAsync(_systemName);
                if (refreshSuccess)
                {
                    // 重新添加认证头
                    await AddAuthenticationHeaderAsync(request);

                    // 重试请求
                    response = await base.SendAsync(request, cancellationToken);
                    
                    if (response.IsSuccessStatusCode)
                    {
                        _logger.LogInformation("系统 {SystemName} Token刷新后重试请求成功", _systemName);
                    }
                    else
                    {
                        _logger.LogWarning("系统 {SystemName} Token刷新后重试请求仍然失败，状态码: {StatusCode}", 
                            _systemName, response.StatusCode);
                    }
                }
                else
                {
                    _logger.LogError("系统 {SystemName} Token刷新失败，无法重试请求", _systemName);
                }
            }

            return response;
        }

        /// <summary>
        /// 添加认证头
        /// </summary>
        private async Task AddAuthenticationHeaderAsync(HttpRequestMessage request)
        {
            try
            {
                var token = await _tokenManager.GetAccessTokenAsync(_systemName);
                if (!string.IsNullOrEmpty(token))
                {
                    // 移除现有的Authorization头（如果存在）
                    request.Headers.Authorization = null;

                    // 添加Bearer Token
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                    
                    _logger.LogDebug("已为系统 {SystemName} 添加认证头到请求: {Method} {Uri}", 
                        _systemName, request.Method, request.RequestUri);
                }
                else
                {
                    _logger.LogWarning("无法获取系统 {SystemName} 的访问Token，请求将不包含认证信息", _systemName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "为系统 {SystemName} 添加认证头时发生异常", _systemName);
            }
        }
    }

    /// <summary>
    /// 通用认证处理器工厂
    /// 用于创建特定系统的认证处理器实例
    /// </summary>
    public class UniversalAuthenticationHandlerFactory
    {
        private readonly IUniversalTokenManager _tokenManager;
        private readonly ILogger<UniversalAuthenticationHandler> _logger;
        private readonly IOptions<HttpClientOptions> _options;

        public UniversalAuthenticationHandlerFactory(
            IUniversalTokenManager tokenManager,
            ILogger<UniversalAuthenticationHandler> logger,
            IOptions<HttpClientOptions> options)
        {
            _tokenManager = tokenManager;
            _logger = logger;
            _options = options;
        }

        /// <summary>
        /// 创建指定系统的认证处理器
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <returns>认证处理器实例</returns>
        public UniversalAuthenticationHandler CreateHandler(string systemName)
        {
            return new UniversalAuthenticationHandler(_tokenManager, _logger, _options, systemName);
        }
    }
}
