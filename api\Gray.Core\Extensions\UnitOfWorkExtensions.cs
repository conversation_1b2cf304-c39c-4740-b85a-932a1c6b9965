using Gray.Core.UnitOfWorkMange;
using System;
using System.Threading.Tasks;

namespace Gray.Core.Extensions
{
    public static class UnitOfWorkExtensions 
    {
        public static void ClearCurrentThreadContext(this UnitOfWork unitOfWork)
        {
            if (unitOfWork == null)
                throw new ArgumentNullException(nameof(unitOfWork));

            try
            {
                // 通过反射清理 AsyncLocal 上下文
                var contextField = typeof(UnitOfWork)
                    .GetField("_contextPerThread", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (contextField?.GetValue(unitOfWork) is System.Threading.AsyncLocal<System.Collections.Generic.Dictionary<string, Gray.Core.EFDbContext.BaseDbContext>> asyncLocal)
                {
                    asyncLocal.Value?.Clear();
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to clear thread context", ex);
            }
        }

        public static async Task ClearCurrentThreadContextAsync(this UnitOfWork unitOfWork)
        {
            if (unitOfWork == null)
                throw new ArgumentNullException(nameof(unitOfWork));

            await Task.Run(() => ClearCurrentThreadContext(unitOfWork));
        }

        // 添加其他工作单元扩展方法...
    }
}
