﻿using Gray.Core.DBManager;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.SystemModels;
using Microsoft.EntityFrameworkCore;
using System;

namespace Gray.Core.EFDbContext
{
    public class JL_CRMDbContext : BaseDbContext<JL_CRMDbContext>, IDependency
    {
        protected override string ConnectionString
        {
            get
            {
                return DBServerProvider.GetConnection("JL_CRMConn");
            }
        }

        public JL_CRMDbContext() : base()
        {
        }

        public JL_CRMDbContext(DbContextOptions<JL_CRMDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.UseDbTypeFromConfig(optionsBuilder, this.GetType().Name);
            //默认禁用实体跟踪
            optionsBuilder = optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            optionsBuilder
           .UseSqlServer(o => o.UseCompatibilityLevel(120));
            base.OnConfiguring(optionsBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
        }

        protected override Type GetBaseEntityType()
        {
            return typeof(JL_CRMEntity);
        }
    }
}