using Gray.Core.GrayHttpClient.Models;
using Gray.Core.GrayHttpClient.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;

namespace Gray.Core.GrayHttpClient.Handlers
{
    /// <summary>
    /// 认证处理器
    /// 负责在HTTP请求中添加认证信息
    /// </summary>
    public class AuthenticationHandler : DelegatingHandler
    {
        private readonly ITokenManager _tokenManager;
        private readonly ILogger<AuthenticationHandler> _logger;
        private readonly HttpClientOptions _options;

        public AuthenticationHandler(
            ITokenManager tokenManager,
            ILogger<AuthenticationHandler> logger,
            IOptions<HttpClientOptions> options)
        {
            _tokenManager = tokenManager;
            _logger = logger;
            _options = options.Value;
        }

        /// <summary>
        /// 发送HTTP请求前的处理
        /// </summary>
        protected override async Task<HttpResponseMessage> SendAsync(
            HttpRequestMessage request, 
            CancellationToken cancellationToken)
        {
            // 如果认证未启用，直接发送请求
            if (!_options.Authentication.Enabled)
            {
                return await base.SendAsync(request, cancellationToken);
            }

            // 添加认证头
            await AddAuthenticationHeaderAsync(request);

            // 发送请求
            var response = await base.SendAsync(request, cancellationToken);

            // 如果返回401未授权，尝试刷新Token并重试
            if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                _logger.LogWarning("收到401未授权响应，尝试刷新Token并重试");

                // 尝试刷新Token
                var refreshSuccess = await _tokenManager.RefreshTokenAsync();
                if (refreshSuccess)
                {
                    // 重新添加认证头
                    await AddAuthenticationHeaderAsync(request);

                    // 重试请求
                    response = await base.SendAsync(request, cancellationToken);
                    
                    if (response.IsSuccessStatusCode)
                    {
                        _logger.LogInformation("Token刷新后重试请求成功");
                    }
                    else
                    {
                        _logger.LogWarning("Token刷新后重试请求仍然失败，状态码: {StatusCode}", response.StatusCode);
                    }
                }
                else
                {
                    _logger.LogError("Token刷新失败，无法重试请求");
                }
            }

            return response;
        }

        /// <summary>
        /// 添加认证头
        /// </summary>
        private async Task AddAuthenticationHeaderAsync(HttpRequestMessage request)
        {
            try
            {
                var token = await _tokenManager.GetAccessTokenAsync();
                if (!string.IsNullOrEmpty(token))
                {
                    // 移除现有的Authorization头（如果存在）
                    request.Headers.Authorization = null;

                    // 添加Bearer Token
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
                    
                    _logger.LogDebug("已添加认证头到请求: {Method} {Uri}", request.Method, request.RequestUri);
                }
                else
                {
                    _logger.LogWarning("无法获取访问Token，请求将不包含认证信息");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加认证头时发生异常");
            }
        }
    }
}
