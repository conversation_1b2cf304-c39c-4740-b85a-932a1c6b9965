using Gray.Core.CacheManager;
using Gray.Core.DBManager;
using Gray.Core.Extensions.AutofacManager;
using Gray.Core.Extensions.Exceptions;
using Gray.Core.Monitoring;
using Gray.Entity.DomainModels;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Gray.Core.Infrastructure
{
    public static class StoredSqlManager
    {
        private static List<Sys_StoredSql> _storedSqls { get; set; }
        private static readonly object _lockObj = new object();
        private static string _cacheVersion = "";
        private const string CacheKey = "storedSqlCache";
        private static readonly IMemoryCache _queryPlanCache = new MemoryCache(new MemoryCacheOptions());

        private static readonly Dictionary<string, Expression<Func<Sys_StoredSql, bool>>> _expressionCache
            = new Dictionary<string, Expression<Func<Sys_StoredSql, bool>>>();

        /// <summary>
        /// 获取所有存储的SQL
        /// </summary>
        public static List<Sys_StoredSql> StoredSqls
        {
            get
            {
                return GetAllStoredSql();
            }
        }

        /// <summary>
        /// SQL执行计划缓存Key
        /// </summary>
        private static string GetQueryPlanCacheKey(string sqlName, object parameters)
        {
            return $"QueryPlan_{sqlName}_{JsonConvert.SerializeObject(parameters)}";
        }

        /// <summary>
        /// 优化后的GetStoredSql方法使用表达式树
        /// </summary>
        public static Sys_StoredSql GetStoredSql(string sqlName)
        {
            if (!_expressionCache.ContainsKey(sqlName))
            {
                var parameter = Expression.Parameter(typeof(Sys_StoredSql), "x");
                var nameProperty = Expression.Property(parameter, "SqlName");
                var enabledProperty = Expression.Property(parameter, "IsEnabled");
                var namePredicate = Expression.Equal(nameProperty, Expression.Constant(sqlName));
                var enabledPredicate = Expression.Equal(enabledProperty, Expression.Constant(1));
                var andPredicate = Expression.AndAlso(namePredicate, enabledPredicate);

                _expressionCache[sqlName] = Expression.Lambda<Func<Sys_StoredSql, bool>>(andPredicate, parameter);
            }

            return StoredSqls.AsQueryable().FirstOrDefault(_expressionCache[sqlName]);
        }

        /// <summary>
        /// 执行存储的SQL并返回动态结果
        /// </summary>
        public static List<dynamic> ExecuteStoredSql(string sqlName, object parameters = null)
        {
            var sql = GetStoredSql(sqlName);
            if (sql == null)
                throw new Exception($"未找到名为[{sqlName}]的SQL或SQL未启用");

            // 处理参数
            object sqlParams = null;
            if (!string.IsNullOrEmpty(sql.ParametersDefinition) && parameters != null)
            {
                sqlParams = parameters;
            }

            // 执行SQL
            return DBServerProvider.GetSqlDapper(sql.DBServer)
                .QueryDynamicList(sql.SqlText, sqlParams, null, false);
        }

        /// <summary>
        /// 带监控和告警的执行方法
        /// </summary>
        private static T ExecuteWithMonitoring<T>(string sqlName, Func<T> execution)
        {
            var sw = Stopwatch.StartNew();
            try
            {
                var result = execution();
                var elapsed = sw.ElapsedMilliseconds;

                // 记录性能数据
                RecordPerformance(sqlName, elapsed);

                // 性能告警检查
                CheckPerformanceWarning(sqlName, elapsed);

                return result;
            }
            catch (Exception ex)
            {
                RecordError(sqlName, ex, sw.ElapsedMilliseconds);
                throw new StoredSqlException($"执行SQL[{sqlName}]时发生错误", ex)
                {
                    SqlName = sqlName,
                    ErrorType = StoredSqlErrorType.ExecutionError
                };
            }
        }

        /// <summary>
        /// 性能告警检查
        /// </summary>
        private static void CheckPerformanceWarning(string sqlName, long elapsedMs)
        {
            var options = GetMonitoringOptions();
            if (elapsedMs > options.ExtremelySlowQueryThresholdMs)
            {
                throw new StoredSqlException($"SQL[{sqlName}]执行时间过长: {elapsedMs}ms")
                {
                    SqlName = sqlName,
                    ExecutionTime = elapsedMs.ToString(),
                    ErrorType = StoredSqlErrorType.PerformanceWarning
                };
            }
        }

        /// <summary>
        /// 执行存储的SQL并返回强类型结果(优化版)
        /// </summary>
        public static List<T> ExecuteStoredSql<T>(string sqlName, object parameters = null)
        {
            return ExecuteWithMonitoring(sqlName, () =>
            {
                var sql = GetStoredSql(sqlName);
                if (sql == null)
                    throw new StoredSqlException($"未找到名为[{sqlName}]的SQL或SQL未启用");

                // 检查执行计划缓存
                var cacheKey = GetQueryPlanCacheKey(sqlName, parameters);
                if (!_queryPlanCache.TryGetValue(cacheKey, out object cachedPlan))
                {
                    // 这里可以添加查询计划优化逻辑
                    cachedPlan = new
                    {
                        SqlText = sql.SqlText,
                        Parameters = parameters
                    };

                    var cacheOptions = new MemoryCacheEntryOptions()
                        .SetSlidingExpiration(TimeSpan.FromMinutes(30))
                        .SetAbsoluteExpiration(TimeSpan.FromHours(2));

                    _queryPlanCache.Set(cacheKey, cachedPlan, cacheOptions);
                }

                // 执行SQL
                return DBServerProvider.SqlDapper
                    .QueryList<T>(sql.SqlText, parameters, null, false);
            });
        }

        /// <summary>
        /// 获取所有存储的SQL并缓存
        /// </summary>
        private static List<Sys_StoredSql> GetAllStoredSql()
        {
            ICacheService cacheService = AutofacContainerModule.GetService<ICacheService>();

            // 检查缓存版本
            if (_storedSqls != null && _cacheVersion == cacheService.Get(CacheKey))
            {
                return _storedSqls;
            }

            lock (_lockObj)
            {
                if (_cacheVersion != "" && _storedSqls != null && _cacheVersion == cacheService.Get(CacheKey))
                    return _storedSqls;

                _storedSqls = DBServerProvider.DbContext
                    .Set<Sys_StoredSql>()
                    .Where(x => x.IsEnabled == 1)
                    .ToList();

                string cacheVersion = cacheService.Get(CacheKey);
                if (string.IsNullOrEmpty(cacheVersion))
                {
                    cacheVersion = DateTime.Now.ToString("yyyyMMddHHMMssfff");
                    cacheService.Add(CacheKey, cacheVersion);
                }

                _cacheVersion = cacheVersion;
            }

            return _storedSqls;
        }

        /// <summary>
        /// 手动刷新SQL缓存
        /// </summary>
        public static void RefreshCache()
        {
            _storedSqls = null;
            _cacheVersion = "";
        }

        /// <summary>
        /// 验证SQL参数是否匹配
        /// </summary>
        public static bool ValidateParameters(string sqlName, object parameters)
        {
            if (string.IsNullOrEmpty(sqlName))
                throw new ArgumentNullException(nameof(sqlName));

            var sql = GetStoredSql(sqlName);
            if (sql == null)
                throw new StoredSqlException($"未找到名为[{sqlName}]的SQL");

            if (string.IsNullOrEmpty(sql.ParametersDefinition))
                return parameters == null;

            try
            {
                // 将参数定义反序列化为字典
                var definedParams = JsonConvert.DeserializeObject<Dictionary<string, string>>(sql.ParametersDefinition);
                var inputParams = parameters.GetType().GetProperties();

                // 验证所有必需参数是否存在且类型匹配
                foreach (var param in definedParams)
                {
                    var inputParam = inputParams.FirstOrDefault(x => x.Name.Equals(param.Key, StringComparison.OrdinalIgnoreCase));
                    if (inputParam == null)
                        return false;

                    // TODO: 可以进一步验证参数类型
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 异步执行SQL
        /// </summary>
        public static async Task<List<T>> ExecuteStoredSqlAsync<T>(string sqlName, object parameters = null)
        {
            return await ExecuteWithMonitoringAsync(sqlName, async () =>
            {
                var sql = GetStoredSql(sqlName);
                if (sql == null)
                    throw new StoredSqlException($"未找到名为[{sqlName}]的SQL或SQL未启用")
                    {
                        SqlName = sqlName,
                        ErrorType = StoredSqlErrorType.NotFound
                    };

                if (!ValidateParameters(sqlName, parameters))
                    throw new StoredSqlException($"SQL[{sqlName}]的参数验证失败")
                    {
                        SqlName = sqlName,
                        Parameters = JsonConvert.SerializeObject(parameters),
                        ErrorType = StoredSqlErrorType.ParameterValidationFailed
                    };

                var result = await DBServerProvider.SqlDapper
                    .QueryListAsync<T>(sql.SqlText, parameters, null, false);
                return result.ToList();
            });
        }

        /// <summary>
        /// 记录SQL执行性能数据
        /// </summary>
        private static void RecordPerformance(string sqlName, long elapsedMilliseconds)
        {
            // 建议实现一个性能监控服务来处理这些数据
            var performanceData = new
            {
                SqlName = sqlName,
                ExecutionTime = elapsedMilliseconds,
                Timestamp = DateTime.Now
            };

            // TODO: 将性能数据写入数据库或发送到监控系统
            Debug.WriteLine($"SQL执行性能: {JsonConvert.SerializeObject(performanceData)}");
        }

        /// <summary>
        /// 记录SQL执行错误
        /// </summary>
        private static void RecordError(string sqlName, Exception ex, long elapsedMilliseconds)
        {
            var errorData = new
            {
                SqlName = sqlName,
                Error = ex.Message,
                StackTrace = ex.StackTrace,
                ExecutionTime = elapsedMilliseconds,
                Timestamp = DateTime.Now,
                ErrorType = ex is StoredSqlException ?
                    ((StoredSqlException)ex).ErrorType :
                    StoredSqlErrorType.ExecutionError
            };

            // 使用性能监控服务记录错误
            SqlPerformanceMonitor.RecordError(errorData);
        }

        // 添加缺失的方法
        private static SqlMonitoringOptions GetMonitoringOptions()
        {
            return AutofacContainerModule.GetService<SqlMonitoringOptions>()
                ?? new SqlMonitoringOptions();
        }

        // 添加异步监控方法
        private static async Task<T> ExecuteWithMonitoringAsync<T>(string sqlName, Func<Task<T>> execution)
        {
            var sw = Stopwatch.StartNew();
            try
            {
                var result = await execution();
                var elapsed = sw.ElapsedMilliseconds;

                RecordPerformance(sqlName, elapsed);
                CheckPerformanceWarning(sqlName, elapsed);

                return result;
            }
            catch (Exception ex)
            {
                RecordError(sqlName, ex, sw.ElapsedMilliseconds);
                throw;
            }
        }
    }
}