/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "预计划提报",TableName = "PreProductPlan",DetailTable =  new Type[] { typeof(PreProductPlanList)},DetailTableCnName = "预计划提报明细",DBServer = "JL_CRMDbContext")]
    public partial class PreProductPlan:JL_CRMEntity
    {
        /// <summary>
       ///生产提报单号
       /// </summary>
       [Display(Name ="生产提报单号")]
       [MaxLength(100)]
       [Column(TypeName="varchar(100)")]
       [Editable(true)]
       public string PreProductPlanNo { get; set; }

       /// <summary>
       ///预订单提报种类
       /// </summary>
       [Display(Name ="预订单提报种类")]
       [MaxLength(20)]
       [Column(TypeName="nvarchar(20)")]
       [Editable(true)]
       public string PreOrderType { get; set; }

       /// <summary>
       ///生产计划年
       /// </summary>
       [Display(Name ="生产计划年")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ProductPlanYear { get; set; }

       /// <summary>
       ///生产计划月
       /// </summary>
       [Display(Name ="生产计划月")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ProductPlanMonth { get; set; }

       /// <summary>
       ///提报大区
       /// </summary>
       [Display(Name ="提报大区")]
       [MaxLength(30)]
       [Column(TypeName="nvarchar(30)")]
       [Editable(true)]
       public string CustomerArea { get; set; }

       /// <summary>
       ///生产提报日期
       /// </summary>
       [Display(Name ="生产提报日期")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public DateTime PreProductPlanDate { get; set; }

       /// <summary>
       ///业务提报总数
       /// </summary>
       [Display(Name ="业务提报总数")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ToTalQtyYW { get; set; }

       /// <summary>
       ///生产提报总数
       /// </summary>
       [Display(Name ="生产提报总数")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? TotalQty { get; set; }

       /// <summary>
       ///备注
       /// </summary>
       [Display(Name ="备注")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       public string Remark { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? CreateID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Creator")]
       [MaxLength(30)]
       [Column(TypeName="nvarchar(30)")]
       [Editable(true)]
       public string Creator { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? CreateDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ModifyID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Modifier")]
       [MaxLength(30)]
       [Column(TypeName="nvarchar(30)")]
       [Editable(true)]
       public string Modifier { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? ModifyDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="AuditId")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? AuditId { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Auditor")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       public string Auditor { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="AuditStatus")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? AuditStatus { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="AuditDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? AuditDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="AuditReason")]
       [MaxLength(500)]
       [Column(TypeName="nvarchar(500)")]
       [Editable(true)]
       public string AuditReason { get; set; }

       /// <summary>
       ///Id
       /// </summary>
       [Key]
       [Display(Name ="Id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid PreProductPlanId { get; set; }

       [Display(Name ="预计划提报明细")]
       [ForeignKey("PreProductPlanId")]
       public List<PreProductPlanList> PreProductPlanList { get; set; }


       
    }
}