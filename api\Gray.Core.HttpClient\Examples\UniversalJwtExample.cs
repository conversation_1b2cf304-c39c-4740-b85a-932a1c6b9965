using Gray.Core.GrayHttpClient.Services;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Gray.Core.GrayHttpClient.Examples
{
    /// <summary>
    /// 通用JWT认证使用示例
    /// 展示如何通过配置快速对接新系统
    /// </summary>
    public class UniversalJwtExample
    {
        private readonly IHttpClientManager _clientManager;
        private readonly IUniversalTokenManager _tokenManager;
        private readonly ILogger<UniversalJwtExample> _logger;

        public UniversalJwtExample(
            IHttpClientManager clientManager,
            IUniversalTokenManager tokenManager,
            ILogger<UniversalJwtExample> logger)
        {
            _clientManager = clientManager;
            _tokenManager = tokenManager;
            _logger = logger;
        }

        /// <summary>
        /// 示例1：简单API调用（推荐方式）
        /// 系统会自动处理认证，您只需要调用API
        /// </summary>
        public async Task<string> SimpleApiCallExample()
        {
            try
            {
                // 获取HttpClient，JWT认证会自动处理
                var httpClient = _clientManager.GetHttpClient("ExternalSystem1");
                
                // 直接调用API，Token会自动添加到请求头
                var response = await httpClient.GetAsync("/api/users");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("API调用成功，返回数据长度: {Length}", content.Length);
                    return content;
                }
                else
                {
                    _logger.LogWarning("API调用失败，状态码: {StatusCode}", response.StatusCode);
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "API调用时发生异常");
                return string.Empty;
            }
        }

        /// <summary>
        /// 示例2：POST请求示例
        /// </summary>
        public async Task<bool> PostDataExample()
        {
            try
            {
                var httpClient = _clientManager.GetHttpClient("ExternalSystem1");
                
                var requestData = new
                {
                    Name = "测试数据",
                    Description = "这是一个测试",
                    CreatedAt = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                var response = await httpClient.PostAsync("/api/data", content);
                
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("数据创建成功");
                    return true;
                }
                else
                {
                    _logger.LogWarning("数据创建失败，状态码: {StatusCode}", response.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建数据时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 示例3：多系统调用
        /// 同时调用多个不同的外部系统
        /// </summary>
        public async Task<Dictionary<string, string>> MultiSystemCallExample()
        {
            var results = new Dictionary<string, string>();

            // 获取所有配置的系统
            var systems = _tokenManager.GetConfiguredSystems();

            foreach (var systemName in systems)
            {
                try
                {
                    var httpClient = _clientManager.GetHttpClient(systemName);
                    var response = await httpClient.GetAsync("/api/health");
                    
                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();
                        results[systemName] = content;
                        _logger.LogInformation("系统 {SystemName} 调用成功", systemName);
                    }
                    else
                    {
                        results[systemName] = $"Error: {response.StatusCode}";
                        _logger.LogWarning("系统 {SystemName} 调用失败: {StatusCode}", systemName, response.StatusCode);
                    }
                }
                catch (Exception ex)
                {
                    results[systemName] = $"Exception: {ex.Message}";
                    _logger.LogError(ex, "调用系统 {SystemName} 时发生异常", systemName);
                }
            }

            return results;
        }

        /// <summary>
        /// 示例4：Token管理（高级用法）
        /// 手动管理Token状态
        /// </summary>
        public async Task TokenManagementExample()
        {
            var systemName = "ExternalSystem1";

            try
            {
                // 检查Token是否有效
                var isValid = await _tokenManager.IsTokenValidAsync(systemName);
                _logger.LogInformation("系统 {SystemName} Token有效性: {IsValid}", systemName, isValid);

                // 手动刷新Token
                if (!isValid)
                {
                    var refreshSuccess = await _tokenManager.RefreshTokenAsync(systemName);
                    _logger.LogInformation("系统 {SystemName} Token刷新结果: {Success}", systemName, refreshSuccess);
                }

                // 获取Token（通常不需要手动获取）
                var token = await _tokenManager.GetAccessTokenAsync(systemName);
                if (!string.IsNullOrEmpty(token))
                {
                    _logger.LogInformation("系统 {SystemName} Token已获取，长度: {Length}", systemName, token.Length);
                }

                // 清除Token（登出时使用）
                // await _tokenManager.ClearTokenAsync(systemName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Token管理时发生异常");
            }
        }

        /// <summary>
        /// 示例5：错误处理和重试
        /// </summary>
        public async Task<string> ErrorHandlingExample()
        {
            var maxRetries = 3;
            var retryCount = 0;

            while (retryCount < maxRetries)
            {
                try
                {
                    var httpClient = _clientManager.GetHttpClient("ExternalSystem1");
                    var response = await httpClient.GetAsync("/api/sensitive-data");

                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();
                        _logger.LogInformation("敏感数据获取成功，重试次数: {RetryCount}", retryCount);
                        return content;
                    }
                    else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                    {
                        _logger.LogWarning("认证失败，尝试清除Token并重试");
                        await _tokenManager.ClearTokenAsync("ExternalSystem1");
                        retryCount++;
                        await Task.Delay(1000 * retryCount); // 递增延迟
                        continue;
                    }
                    else
                    {
                        _logger.LogWarning("API调用失败，状态码: {StatusCode}", response.StatusCode);
                        break;
                    }
                }
                catch (HttpRequestException ex)
                {
                    _logger.LogWarning(ex, "网络请求异常，重试次数: {RetryCount}", retryCount);
                    retryCount++;
                    if (retryCount < maxRetries)
                    {
                        await Task.Delay(2000 * retryCount); // 递增延迟
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "未知异常，停止重试");
                    break;
                }
            }

            _logger.LogError("获取敏感数据失败，已达到最大重试次数");
            return string.Empty;
        }

        /// <summary>
        /// 示例6：批量操作
        /// </summary>
        public async Task<List<BatchResult>> BatchOperationExample()
        {
            var results = new List<BatchResult>();
            var httpClient = _clientManager.GetHttpClient("ExternalSystem1");

            var operations = new[]
            {
                new { Id = 1, Action = "GET", Endpoint = "/api/users/1" },
                new { Id = 2, Action = "GET", Endpoint = "/api/users/2" },
                new { Id = 3, Action = "GET", Endpoint = "/api/users/3" }
            };

            // 并发执行多个操作
            var tasks = operations.Select(async op =>
            {
                try
                {
                    var response = await httpClient.GetAsync(op.Endpoint);
                    var content = response.IsSuccessStatusCode 
                        ? await response.Content.ReadAsStringAsync() 
                        : $"Error: {response.StatusCode}";

                    return new BatchResult
                    {
                        Id = op.Id,
                        Success = response.IsSuccessStatusCode,
                        Data = content,
                        StatusCode = (int)response.StatusCode
                    };
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "批量操作 {Id} 失败", op.Id);
                    return new BatchResult
                    {
                        Id = op.Id,
                        Success = false,
                        Data = ex.Message,
                        StatusCode = 0
                    };
                }
            });

            results.AddRange(await Task.WhenAll(tasks));

            var successCount = results.Count(r => r.Success);
            _logger.LogInformation("批量操作完成，成功: {Success}/{Total}", successCount, results.Count);

            return results;
        }

        /// <summary>
        /// 示例7：系统健康检查
        /// </summary>
        public async Task<Dictionary<string, SystemHealth>> HealthCheckExample()
        {
            var healthResults = new Dictionary<string, SystemHealth>();
            var systems = _tokenManager.GetConfiguredSystems();

            foreach (var systemName in systems)
            {
                var health = new SystemHealth { SystemName = systemName };

                try
                {
                    var startTime = DateTime.UtcNow;
                    
                    // 检查Token状态
                    health.TokenValid = await _tokenManager.IsTokenValidAsync(systemName);
                    
                    // 检查API连通性
                    var httpClient = _clientManager.GetHttpClient(systemName);
                    var response = await httpClient.GetAsync("/api/health");
                    
                    health.ApiAccessible = response.IsSuccessStatusCode;
                    health.StatusCode = (int)response.StatusCode;
                    health.ResponseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
                    
                    if (response.IsSuccessStatusCode)
                    {
                        health.ResponseData = await response.Content.ReadAsStringAsync();
                    }
                    
                    health.IsHealthy = health.TokenValid && health.ApiAccessible;
                }
                catch (Exception ex)
                {
                    health.IsHealthy = false;
                    health.ErrorMessage = ex.Message;
                    _logger.LogError(ex, "系统 {SystemName} 健康检查失败", systemName);
                }

                healthResults[systemName] = health;
            }

            return healthResults;
        }
    }

    /// <summary>
    /// 批量操作结果
    /// </summary>
    public class BatchResult
    {
        public int Id { get; set; }
        public bool Success { get; set; }
        public string Data { get; set; } = string.Empty;
        public int StatusCode { get; set; }
    }

    /// <summary>
    /// 系统健康状态
    /// </summary>
    public class SystemHealth
    {
        public string SystemName { get; set; } = string.Empty;
        public bool IsHealthy { get; set; }
        public bool TokenValid { get; set; }
        public bool ApiAccessible { get; set; }
        public int StatusCode { get; set; }
        public double ResponseTime { get; set; }
        public string? ResponseData { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
