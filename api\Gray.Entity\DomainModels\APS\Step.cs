/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "半成品工序配置",TableName = "Step",DBServer = "JL_APSDbContext")]
    public partial class Step:JL_APSEntity
    {
        /// <summary>
       ///
       /// </summary>
       [Key]
       [Display(Name ="Id")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int Id { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="StepName")]
       [MaxLength(50)]
       [Column(TypeName="nvarchar(50)")]
       [Editable(true)]
       public string StepName { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="DutyAddress")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string DutyAddress { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="IFUse")]
       [Column(TypeName="bit")]
       [Editable(true)]
       public bool? IFUse { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Days")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? Days { get; set; }

       
    }
}