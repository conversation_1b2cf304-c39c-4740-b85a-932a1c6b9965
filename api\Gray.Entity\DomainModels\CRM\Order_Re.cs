/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "订单拒绝",TableName = "Order_Re",DBServer = "JL_CRMDbContext")]
    public partial class Order_Re:JL_CRMEntity
    {
        /// <summary>
       ///
       /// </summary>
       [Display(Name ="OrderId")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid OrderId { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="OrderNo")]
       [MaxLength(100)]
       [Column(TypeName="varchar(100)")]
       [Editable(true)]
       public string OrderNo { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="OrderType")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string OrderType { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="TotalPrice")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? TotalPrice { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="TotalQty")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? TotalQty { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="OrderDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public DateTime OrderDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CustomerCode")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string CustomerCode { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CustomerName")]
       [MaxLength(1000)]
       [Column(TypeName="nvarchar(1000)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string CustomerName { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="PhoneNo")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string PhoneNo { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="OrderStatus")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? OrderStatus { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Remark")]
       [MaxLength(400)]
       [Column(TypeName="nvarchar(400)")]
       [Editable(true)]
       public string Remark { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="AuditId")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? AuditId { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Auditor")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       public string Auditor { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="AuditStatus")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? AuditStatus { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="AuditDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? AuditDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="AuditReason")]
       [MaxLength(1000)]
       [Column(TypeName="nvarchar(1000)")]
       [Editable(true)]
       public string AuditReason { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? CreateID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Creator")]
       [MaxLength(60)]
       [Column(TypeName="nvarchar(60)")]
       [Editable(true)]
       public string Creator { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? CreateDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ModifyID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Modifier")]
       [MaxLength(60)]
       [Column(TypeName="nvarchar(60)")]
       [Editable(true)]
       public string Modifier { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? ModifyDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CustomerAddressId")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       public Guid? CustomerAddressId { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CustomerAddress")]
       [MaxLength(1200)]
       [Column(TypeName="nvarchar(1200)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string CustomerAddress { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CustomerArea")]
       [MaxLength(400)]
       [Column(TypeName="nvarchar(400)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string CustomerArea { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CustomerXM")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string CustomerXM { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="PackingRequirment")]
       [MaxLength(400)]
       [Column(TypeName="nvarchar(400)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string PackingRequirment { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="PreOrderId")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       public Guid? PreOrderId { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="TotalWeight")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? TotalWeight { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="TotalRebate")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? TotalRebate { get; set; }

       /// <summary>
       ///发货优先级
       /// </summary>
       [Display(Name ="发货优先级")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? Priority { get; set; }

       /// <summary>
       ///总应收账款
       /// </summary>
       [Display(Name ="总应收账款")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? TotalReceivables { get; set; }

       /// <summary>
       ///总理赔账款
       /// </summary>
       [Display(Name ="总理赔账款")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? TotalClaimAccounts { get; set; }

       /// <summary>
       ///冲返利类型
       /// </summary>
       [Display(Name ="冲返利类型")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string RebateType { get; set; }

       /// <summary>
       ///冲返利上限
       /// </summary>
       [Display(Name ="冲返利上限")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? RebateMax { get; set; }

       /// <summary>
       ///使用月度返利
       /// </summary>
       [Display(Name ="使用月度返利")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? RebateMonth { get; set; }

       /// <summary>
       ///使用季度返利
       /// </summary>
       [Display(Name ="使用季度返利")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? RebateQuarter { get; set; }

       /// <summary>
       ///使用年度返利
       /// </summary>
       [Display(Name ="使用年度返利")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? ReabtYear { get; set; }

       /// <summary>
       ///可用月度返利
       /// </summary>
       [Display(Name ="可用月度返利")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? RebateMonthMax { get; set; }

       /// <summary>
       ///可用季度返利
       /// </summary>
       [Display(Name ="可用季度返利")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? RebateQuarterMax { get; set; }

       /// <summary>
       ///可用年度返利
       /// </summary>
       [Display(Name ="可用年度返利")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? ReabtYearMax { get; set; }

       /// <summary>
       ///理赔账款可用
       /// </summary>
       [Display(Name ="理赔账款可用")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? ClaimAccountsMax { get; set; }

       /// <summary>
       ///拒绝的Id
       /// </summary>
       [Key]
       [Display(Name ="拒绝的Id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid Order_ReId { get; set; }

       
    }
}