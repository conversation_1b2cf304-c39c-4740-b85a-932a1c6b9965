﻿using Microsoft.EntityFrameworkCore;
using Gray.Core.DBManager;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.SystemModels;
using System;

namespace Gray.Core.EFDbContext
{
    public class JL_DYDbContext : BaseDbContext<JL_DYDbContext>, IDependency
    {
        protected override string ConnectionString
        {
            get
            {
                return DBServerProvider.GetConnection(this.GetType().Name);
            }
        }

        public JL_DYDbContext() : base()
        {
        }

        public JL_DYDbContext(DbContextOptions<JL_DYDbContext> options) : base(options)
        {
        }

      

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.UseDbTypeFromConfig(optionsBuilder, this.GetType().Name);
            //默认禁用实体跟踪
            optionsBuilder = optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            base.OnConfiguring(optionsBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
        }

        protected override Type GetBaseEntityType()
        {
           return typeof(JL_DYEntity);
        }
    }
}