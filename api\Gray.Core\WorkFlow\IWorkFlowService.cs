using Gray.Core.EFDbContext;
using Gray.Core.Utilities;
using Gray.Entity.DomainModels;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading.Tasks;

namespace Gray.Core.WorkFlow 
{
    public interface IWorkFlowService
    {
        // 基础查询方法
        bool Exists<T>(string workFlowTableName = null);
        bool Exists<T>(T entity, string workFlowTableName = null) where T : class;
        bool Exists(string table);
        Task<int> GetAuditStatus<T>(string value, string workFlowTableName);
        Task<Sys_WorkFlowTable> GetAuditFlowTable<T>(string workTableKey, string workFlowTableName = null);
        
        // 核心业务方法 
        Task<WebResponseContent> AddProceseAsync<T>(T entity, bool rewrite = false, bool changeTableStatus = true,
            Action<T, List<int>> addWorkFlowExecuted = null, bool checkId = false,
            string workFlowTableName = null) where T : class;

        Task<WebResponseContent> AuditAsync<T>(
            BaseDbContext tableDbContext,
            T entity,
            AuditStatus status,
            string remark,
            PropertyInfo autditProperty = null,
            Func<T, AuditStatus, bool, WebResponseContent> workFlowExecuting = null,
            Func<T, AuditStatus, List<int>, bool, WebResponseContent> workFlowExecuted = null,
            bool init = false,
            Action<T, List<int>> initInvoke = null, 
            FlowWriteState flowWriteState = FlowWriteState.审批,
            string workFlowTableName = null) where T : class;

        Task<WebResponseContent> AntiAuditAsync<T>(AntiData antiData, BaseDbContext dbContext, T entity, string workFlowTableName, bool restart = false) where T : class;

        // 其他查询与业务方法
        Task<object> GetAuditFormDataAsync(string tableKey, string table);
        Task<object> GetFormDataAsync<T, Detail>(string tableKey, string table, WorkFlowFormDetails flowFormDetails) 
            where T : class 
            where Detail : class;
        List<object> GetFormData<T>(List<T> entities, string[] fields) where T : class;
        Task<WebResponseContent> UpdateFlowDataAsync(Sys_WorkFlow workFlow, List<Sys_WorkFlowStep> add);
        Task<List<int>> GetAuditUserIdsAsync(int stepType, string nextId = null);
        
        // 统一事务与异常处理方法（使用TransactionService进行事务管理）
        Task<TResult> ExecuteInTransactionAsync<TResult>(Func<Task<TResult>> action, string operationName);

        // 新增审核日志方法
        Task AddAuditLogAsync<T>(object[] keys, int? auditStatus, string auditReason) where T : class;
    }
}
