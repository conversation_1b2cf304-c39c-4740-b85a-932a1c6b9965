# IOTSharp物联网平台集成指南

## 🎯 已完成配置

IOTSharp系统已经通过通用JWT认证系统完成配置，您可以直接使用！

### 配置信息
- **系统地址**: http://10.0.1.13:5000
- **认证方式**: JWT Token认证
- **用户账号**: <EMAIL>
- **登录接口**: /api/Account/Login
- **Token有效期**: 3小时（10800秒）

## 🚀 直接使用示例

### 1. 获取设备遥测数据

```csharp
public class DeviceService
{
    private readonly IOTSharpRealExample _iotSharpExample;

    public DeviceService(IOTSharpRealExample iotSharpExample)
    {
        _iotSharpExample = iotSharpExample;
    }

    public async Task GetDeviceData()
    {
        // 设备ID（来自您的真实数据）
        var deviceId = "759f2245-7971-48c6-9a12-012a3714333a";
        
        // 获取最新遥测数据 - JWT认证自动处理
        var telemetryData = await _iotSharpExample.GetDeviceTelemetryLatestAsync(deviceId);
        
        if (telemetryData != null)
        {
            foreach (var data in telemetryData)
            {
                Console.WriteLine($"{data.KeyName}: {data.Value} ({data.DataType}) - {data.DateTime}");
            }
        }
    }
}
```

### 2. 获取温湿度数据

```csharp
public async Task GetTemperatureHumidity()
{
    var deviceId = "759f2245-7971-48c6-9a12-012a3714333a";
    
    // 自动提取温度和湿度数据
    var tempHumidity = await _iotSharpExample.GetTemperatureHumidityAsync(deviceId);
    
    if (tempHumidity != null)
    {
        Console.WriteLine($"温度: {tempHumidity.Temperature}°C");
        Console.WriteLine($"湿度: {tempHumidity.Humidity}%");
        Console.WriteLine($"设备时间: {tempHumidity.DeviceTimestamp}");
    }
}
```

### 3. 批量获取多设备数据

```csharp
public async Task GetMultipleDevicesData()
{
    var deviceIds = new List<string>
    {
        "759f2245-7971-48c6-9a12-012a3714333a",
        "另一个设备ID",
        "第三个设备ID"
    };
    
    // 并发获取多个设备数据
    var results = await _iotSharpExample.GetMultipleDevicesTelemetryAsync(deviceIds);
    
    foreach (var device in results)
    {
        Console.WriteLine($"设备 {device.Key} 有 {device.Value.Count} 条数据");
    }
}
```

### 4. 设备监控

```csharp
public async Task StartDeviceMonitoring()
{
    var deviceId = "759f2245-7971-48c6-9a12-012a3714333a";
    var cancellationTokenSource = new CancellationTokenSource();
    
    // 每30秒监控一次设备数据
    await _iotSharpExample.MonitorDeviceAsync(deviceId, 30, cancellationTokenSource.Token);
}
```

## 📊 真实数据示例

根据您提供的真实响应，系统会自动解析以下数据：

```json
{
  "data": [
    {
      "keyName": "humidity",
      "dateTime": "2025-07-07T01:19:42.652265",
      "dataType": "Double",
      "value": 55.53
    },
    {
      "keyName": "temperature", 
      "dateTime": "2025-07-07T01:19:42.6507062",
      "dataType": "Double",
      "value": 24.8
    },
    {
      "keyName": "timestamp",
      "dateTime": "2025-07-07T01:19:42.6537989", 
      "dataType": "String",
      "value": "2025-07-07 09:20:29"
    }
  ],
  "code": 10000,
  "msg": "Ok"
}
```

解析结果：
- **温度**: 24.8°C
- **湿度**: 55.53%
- **设备时间**: 2025-07-07 09:20:29

## 🔧 服务注册

在您的业务项目中注册服务：

```csharp
// Program.cs
builder.Services.AddHttpClientServices(builder.Configuration);
builder.Services.AddScoped<IOTSharpRealExample>();

// 在Controller或Service中使用
public class DeviceController : ControllerBase
{
    private readonly IOTSharpRealExample _iotSharpExample;

    public DeviceController(IOTSharpRealExample iotSharpExample)
    {
        _iotSharpExample = iotSharpExample;
    }

    [HttpGet("device/{deviceId}/telemetry")]
    public async Task<IActionResult> GetDeviceTelemetry(string deviceId)
    {
        var data = await _iotSharpExample.GetDeviceTelemetryLatestAsync(deviceId);
        return Ok(data);
    }

    [HttpGet("device/{deviceId}/temperature-humidity")]
    public async Task<IActionResult> GetTemperatureHumidity(string deviceId)
    {
        var data = await _iotSharpExample.GetTemperatureHumidityAsync(deviceId);
        return Ok(data);
    }
}
```

## 🛡️ 自动功能

系统已自动处理：

- ✅ **JWT认证**: 自动登录获取Token
- ✅ **Token刷新**: Token过期前自动刷新
- ✅ **错误重试**: 401错误时自动重新认证
- ✅ **数据解析**: 自动解析IOTSharp响应格式
- ✅ **异常处理**: 完善的错误处理和日志记录

## 🔍 监控和调试

### 检查连接状态

```csharp
public async Task CheckConnection()
{
    var isConnected = await _iotSharpExample.CheckIOTSharpConnectionAsync();
    Console.WriteLine($"IOTSharp连接状态: {isConnected}");
}
```

### 查看日志

系统会自动记录详细日志：
- Token获取和刷新状态
- API调用成功/失败信息
- 数据解析结果
- 异常详情

## 🎉 总结

现在您可以：

1. **零配置使用** - IOTSharp已完全配置好
2. **直接调用API** - 注入`IOTSharpRealExample`即可使用
3. **自动认证** - JWT Token完全自动管理
4. **实时监控** - 支持设备数据实时监控
5. **批量处理** - 支持多设备并发数据获取

所有认证和网络细节都已封装，您只需要关注业务逻辑！
