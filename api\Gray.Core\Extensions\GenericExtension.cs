﻿using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Dynamic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace Gray.Core.Extensions
{
    public class DataTableWithColumns
    {
        public List<string> columns { get; set; } = new List<string>();
        public DataTable table { get; set; }
    }

    /// <summary>
    /// 泛型扩展
    /// </summary>
    public static class GenericExtension
    {
        public static bool Equal<T>(this T x, T y)
        {
            return ((IComparable)(x)).CompareTo(y) == 0;
        }

        #region ToDictionary

        /// <summary>
        /// 将实体指定的字段写入字典
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <param name="expression"></param>
        /// <returns></returns>

        public static Dictionary<string, object> ToDictionary<T>(this T t, Expression<Func<T, object>> expression) where T : class
        {
            Dictionary<string, object> dic = new Dictionary<string, object>();
            string[] fields = expression.GetExpressionToArray();
            PropertyInfo[] properties = expression == null ? t.GetType().GetProperties() : t.GetType().GetProperties().Where(x => fields.Contains(x.Name)).ToArray();

            foreach (var property in properties)
            {
                var value = property.GetValue(t, null);
                dic.Add(property.Name, value != null ? value.ToString() : "");
            }
            return dic;
        }

        public static Dictionary<string, string> ToDictionary<TInterface, T>(this TInterface t, Dictionary<string, string> dic = null) where T : class, TInterface
        {
            if (dic == null)
                dic = new Dictionary<string, string>();
            var properties = typeof(T).GetProperties();
            foreach (var property in properties)
            {
                var value = property.GetValue(t, null);
                if (value == null) continue;
                dic.Add(property.Name, value != null ? value.ToString() : "");
            }
            return dic;
        }

        #endregion ToDictionary

        /// <summary>
        /// 将DataTable 转成 List<dynamic>,主要用于给dapper批量操作时传参数。
        /// </summary>
        /// <param name="table">要转换的DataTable对象</param>
        /// <param name="filterFields">要筛选的列。默认不传，此时返回全部列</param>
        /// <param name="includeOrExclude">指定上一个参数条件是要保留的列还是要排除的列</param>
        /// <returns></returns>
        public static List<dynamic> ToDynamicList(this DataTable table, string[] filterFields = null, bool includeOrExclude = true)
        {
            var modelList = new List<dynamic>();
            var isFilter = filterFields != null && filterFields.Any();
            IEnumerable reservedColumns = table.Columns;
            if (isFilter)
                reservedColumns = table.Columns.Cast<DataColumn>().Where(c => filterFields.Contains(c.ColumnName) == includeOrExclude);
            foreach (DataRow row in table.Rows)
            {
                dynamic model = new ExpandoObject();
                var dict = (IDictionary<string, object>)model;
                foreach (DataColumn column in reservedColumns)
                {
                    dict[column.ColumnName] = row[column];
                }
                modelList.Add(model);
            }
            return modelList;
        }

        public static List<T> Add合计<T>(this IEnumerable<T> source, string 合计列名, params string[] 计算列名) where T : new()
        {
            List<T> list = source.ToList();

            T a = new T();

            var property2 = a.GetType().GetProperty(合计列名);
            property2.SetValue(a, "合计");

            foreach (var item in 计算列名)
            {
                var property = a.GetType().GetProperty(item);

                int 计算结果 = 0;
                //计算数量
                for (var i = 0; i < list.Count(); i++)
                {
                    Type type = a.GetType();
                    System.Reflection.PropertyInfo propertyInfo = type.GetProperty("item"); //获取指定名称的属性
                    try
                    {
                        object value = (object)propertyInfo.GetValue(list[i], null); //获取属性值

                        计算结果 = 计算结果 + value.GetInt();
                    }
                    catch (Exception exp)
                    {
                    }
                }

                property.SetValue(a, 计算结果);
            }

            list.Add(a);
            return list;
        }

        public static DataTableWithColumns DataTablWithColumn(this DataTable thisValue)
        {
            List<string> columns = new List<string>();
            if (thisValue != null && thisValue.Columns.Count > 0)
            {
                for (int i = 0; i < thisValue.Columns.Count; i++)
                {
                    columns.Add(thisValue.Columns[i].ColumnName.ToString());
                }
                return new DataTableWithColumns() { columns = columns, table = thisValue };
            }
            else
            {
                return null;
            }
        }

        public static List<T> DataTableToDataList<T>(this DataTable dt)
        {
            var list = new List<T>();
            var plist = new List<PropertyInfo>(typeof(T).GetProperties());
            foreach (DataRow item in dt.Rows)
            {
                T s = Activator.CreateInstance<T>();
                for (int i = 0; i < dt.Columns.Count; i++)
                {
                    PropertyInfo info = plist.Find(p => p.Name == dt.Columns[i].ColumnName);
                    if (info != null)
                    {
                        try
                        {
                            if (!Convert.IsDBNull(item[i]))
                            {
                                object v = null;
                                if (info.PropertyType.ToString().Contains("System.Nullable"))
                                {
                                    v = Convert.ChangeType(item[i], Nullable.GetUnderlyingType(info.PropertyType));
                                }
                                else
                                {
                                    v = Convert.ChangeType(item[i], info.PropertyType);
                                }
                                info.SetValue(s, v, null);
                            }
                        }
                        catch (Exception ex)
                        {
                            throw new Exception("字段[" + info.Name + "]转换出错," + ex.Message);
                        }
                    }
                }
                list.Add(s);
            }
            return list;
        }

        public static IList<T> ConvertToModel<T>(DataTable dt) where T : new()
        {
            // 定义集合
            IList<T> ts = new List<T>();

            // 获得此模型的类型
            Type type = typeof(T);
            string tempName = "";

            foreach (DataRow dr in dt.Rows)
            {
                T t = new T();
                // 获得此模型的公共属性
                PropertyInfo[] propertys = t.GetType().GetProperties();
                foreach (PropertyInfo pi in propertys)
                {
                    tempName = pi.Name;  // 检查DataTable是否包含此列

                    if (dt.Columns.Contains(tempName))
                    {
                        // 判断此属性是否有Setter
                        if (!pi.CanWrite) continue;

                        object value = dr[tempName];
                        if (value != DBNull.Value)
                            try
                            {
                                pi.SetValue(t, value, null);
                            }
                            catch (Exception exp)
                            {
                                Console.WriteLine(exp);
                            }
                    }
                }
                ts.Add(t);
            }
            return ts;
        }

        public static DataTable ToDataTable<T>(this IEnumerable<T> source, Expression<Func<T, object>> columns = null, bool contianKey = true)
        {
            DataTable dtReturn = new DataTable();
            if (source == null) return dtReturn;

            PropertyInfo[] oProps = typeof(T).GetProperties()
                .Where(x => x.PropertyType.Name != "List`1").ToArray();
            if (columns != null)
            {
                string[] columnArray = columns.GetExpressionToArray();
                oProps = oProps.Where(x => columnArray.Contains(x.Name)).ToArray();
            }
            //移除自增主键
            PropertyInfo keyType = oProps.GetKeyProperty();// oProps.GetKeyProperty()?.PropertyType;
            if (!contianKey && keyType != null && (keyType.PropertyType == typeof(int) || keyType.PropertyType == typeof(long)))
            {
                oProps = oProps.Where(x => x.Name != keyType.Name).ToArray();
            }

            foreach (var pi in oProps)
            {
                var colType = pi.PropertyType;

                if ((colType.IsGenericType) && (colType.GetGenericTypeDefinition() == typeof(Nullable<>)))
                {
                    colType = colType.GetGenericArguments()[0];
                }

                dtReturn.Columns.Add(new DataColumn(pi.Name, colType));
            }
            foreach (var rec in source)
            {
                var dr = dtReturn.NewRow();
                foreach (var pi in oProps)
                {
                    dr[pi.Name] = pi.GetValue(rec, null) == null
                        ? DBNull.Value
                        : pi.GetValue
                            (rec, null);
                }
                dtReturn.Rows.Add(dr);
            }
            return dtReturn;
        }

        // 将 List<dynamic> 转换为 DataTable
        public static DataTable ConvertToDataTable(this List<dynamic> list)
        {
            DataTable table = new DataTable();

            if (list == null || list.Count == 0)
                return table;

            // 用于存储列名
            HashSet<string> columnNames = new HashSet<string>();

            // 遍历每个动态对象，确定列名
            foreach (var item in list)
            {
                if (item is ExpandoObject) // 处理 ExpandoObject
                {
                    var expandoDict = (IDictionary<string, object>)item;
                    foreach (var kvp in expandoDict)
                    {
                        if (!columnNames.Contains(kvp.Key))
                        {
                            columnNames.Add(kvp.Key);
                            // 设置列类型为 object，允许使用 DBNull
                            table.Columns.Add(kvp.Key, typeof(object));
                        }
                    }
                }
                else // 处理匿名类型
                {
                    var properties = item.GetType().GetProperties();
                    foreach (var prop in properties)
                    {
                        if (!columnNames.Contains(prop.Name))
                        {
                            columnNames.Add(prop.Name);
                            // 设置列类型为 object，允许使用 DBNull
                            table.Columns.Add(prop.Name, typeof(object));
                        }
                    }
                }
            }

            // 填充数据
            foreach (var item in list)
            {
                DataRow row = table.NewRow();

                if (item is ExpandoObject) // 处理 ExpandoObject 数据
                {
                    var expandoDict = (IDictionary<string, object>)item;
                    foreach (var kvp in expandoDict)
                    {
                        row[kvp.Key] = kvp.Value ?? DBNull.Value;
                    }
                }
                else // 处理匿名类型数据
                {
                    var properties = item.GetType().GetProperties();
                    foreach (var prop in properties)
                    {
                        row[prop.Name] = prop.GetValue(item) ?? DBNull.Value;
                    }
                }

                table.Rows.Add(row);
            }

            return table;
        }

        public static DataTable LINQToDataTable<T>(this IEnumerable<T> varlist, string TableName = nameof(T), Dictionary<string, string> tableColNameMap = null)
        {   //定义要返回的DataTable对象
            //DataTable dtReturn = new DataTable("DtInfo");
            DataTable dtReturn = new DataTable(TableName);

            // 保存列集合的属性信息数组
            PropertyInfo[] oProps = null;
            if (varlist == null) return dtReturn;//安全性检查
                                                 //循环遍历集合，使用反射获取类型的属性信息
            foreach (T rec in varlist)
            {
                //使用反射获取T类型的属性信息，返回一个PropertyInfo类型的集合
                if (oProps == null)
                {
                    oProps = ((Type)rec.GetType()).GetProperties();
                    //循环PropertyInfo数组
                    foreach (PropertyInfo pi in oProps)
                    {
                        Type colType = pi.PropertyType;//得到属性的类型
                                                       //如果属性为泛型类型
                        if ((colType.IsGenericType) && (colType.GetGenericTypeDefinition()
                        == typeof(Nullable<>)))
                        {   //获取泛型类型的参数
                            colType = colType.GetGenericArguments()[0];
                        }
                        //将类型的属性名称与属性类型作为DataTable的列数据
                        dtReturn.Columns.Add(new DataColumn(pi.Name, colType));
                    }
                }
                //新建一个用于添加到DataTable中的DataRow对象
                DataRow dr = dtReturn.NewRow();
                //循环遍历属性集合
                foreach (PropertyInfo pi in oProps)
                {   //为DataRow中的指定列赋值
                    dr[pi.Name] = pi.GetValue(rec, null) == null ?
                        DBNull.Value : pi.GetValue(rec, null);
                }
                //将具有结果值的DataRow添加到DataTable集合中
                dtReturn.Rows.Add(dr);
            }
            if (tableColNameMap != null && tableColNameMap.Count > 0)
            {
                foreach (var key in tableColNameMap.Keys)
                {
                    if (dtReturn.Columns.Contains(key))
                    {
                        dtReturn.Columns[key].ColumnName = tableColNameMap.Where(a => a.Key == key).Select(a => a.Value).FirstOrDefault();
                    }
                    else
                    {
                        Console.WriteLine(key + "不存在此列");
                    }
                }
            }
            return dtReturn;//返回DataTable对象
        }

        /// <summary>
        /// 将集合转换为 DataTable
        /// </summary>
        /// <param name="varlist">数据集合</param>
        /// <param name="tableName">DataTable名称</param>
        /// <param name="tableColNameMap">列名映射</param>
        /// <param name="IFJSON">是否对非基元类型进行JSON序列化</param>
        /// <returns>返回转换后的 DataTable</returns>
        public static DataTable ToDataTable2<T>(this IEnumerable<T> varlist, string tableName = nameof(T), Dictionary<string, string> tableColNameMap = null, bool IFJSON = false)
        {
            // 定义要返回的 DataTable
            DataTable dtReturn = new DataTable(tableName);

            // 获取属性信息
            PropertyDescriptorCollection properties = TypeDescriptor.GetProperties(typeof(T));

            // 动态添加列
            foreach (PropertyDescriptor property in properties)
            {
                dtReturn.Columns.Add(property.Name, Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType);
            }

            // 如果 varlist 为空，返回空的 DataTable
            if (varlist == null || !varlist.Any()) return dtReturn;

            // 循环遍历集合，填充 DataTable
            foreach (var item in varlist)
            {
                DataRow row = dtReturn.NewRow();

                // 为每列赋值
                foreach (DataColumn column in dtReturn.Columns)
                {
                    var propertyValue = item.GetType().GetProperty(column.ColumnName).GetValue(item);

                    if (propertyValue != null)
                    {
                        if (IFJSON && !column.DataType.IsPrimitive)
                        {
                            row[column.ColumnName] = JsonConvert.SerializeObject(propertyValue);
                        }
                        else
                        {
                            row[column.ColumnName] = propertyValue;
                        }
                    }
                    else
                    {
                        row[column.ColumnName] = DBNull.Value;
                    }
                }

                dtReturn.Rows.Add(row);
            }

            // 如果列名映射存在，修改列名
            if (tableColNameMap != null && tableColNameMap.Count > 0)
            {
                foreach (var map in tableColNameMap)
                {
                    if (dtReturn.Columns.Contains(map.Key))
                    {
                        dtReturn.Columns[map.Key].ColumnName = map.Value;
                    }
                    else
                    {
                        Console.WriteLine($"{map.Key} 不存在此列");
                    }
                }
            }

            return dtReturn;
        }

        /// <summary>
        /// 将List转换为DataTable
        /// </summary>
        /// <param name="list">请求数据</param>
        /// <returns></returns>
        public static DataTable ListToDataTable<T>(this List<T> list, string tableName = "table", bool IFJSON = false)
        {
            //创建一个名为"tableName"的空表
            DataTable dt = new DataTable(tableName);

            ////创建传入对象名称的列
            //foreach (var item in list.FirstOrDefault().GetType().GetProperties())
            //{
            //    dt.Columns.Add(item.Name);
            //}
            PropertyDescriptorCollection properties = TypeDescriptor.GetProperties(typeof(T));
            for (int i = 0; i < properties.Count; i++)
            {
                PropertyDescriptor property = properties[i];
                dt.Columns.Add(property.Name, Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType);
            }
            //循环存储
            foreach (var item in list)
            {
                //新加行
                DataRow value = dt.NewRow();
                //根据DataTable中的值，进行对应的赋值
                foreach (DataColumn dtColumn in dt.Columns)
                {
                    int i = dt.Columns.IndexOf(dtColumn);
                    //基元元素，直接复制，对象类型等，进行序列化
                    if (value.GetType().IsPrimitive)
                    {
                        value[i] = item.GetType().GetProperty(dtColumn.ColumnName).GetValue(item);
                    }
                    else
                    {
                        if (IFJSON)
                        {
                            value[i] = JsonConvert.SerializeObject(item.GetType().GetProperty(dtColumn.ColumnName).GetValue(item));
                        }
                        else
                        {
                            var tmp = item.GetType().GetProperty(dtColumn.ColumnName).GetValue(item);
                            if (tmp != null)
                            {
                                value[i] = tmp;
                            }
                            else
                            {
                            }
                        }
                    }
                }
                dt.Rows.Add(value);
            }
            return dt;
        }
    }
}