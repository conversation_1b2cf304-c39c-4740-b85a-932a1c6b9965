﻿using Gray.Core.Configuration;
using Gray.Core.Const;
using Gray.Core.EFDbContext;
using Gray.Core.Enums;
using Gray.Core.Extensions;
using Gray.Core.ManageUser;
using Gray.Core.UnitOfWorkMange;
using Gray.Entity;
using Gray.Entity.DomainModels;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using MySqlConnector;
using Npgsql;
using Oracle.ManagedDataAccess.Client;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Gray.Core.DBManager
{
    public partial class DBServerProvider
    {
        private static readonly object _lock = new object();
        private static volatile bool _initialized = false;
        private static IServiceProvider _serviceProvider;
        private static IConfiguration _configuration;
        private static IUnitOfWork _unitOfWork;

        private static readonly ConcurrentDictionary<string, DBConnConfig> ConnectionPool =
            new ConcurrentDictionary<string, DBConnConfig>(StringComparer.OrdinalIgnoreCase);

        private static readonly int MaxRetries = 3;
        private static readonly TimeSpan RetryDelay = TimeSpan.FromSeconds(1);

        public static void Configure(IServiceProvider serviceProvider, IConfiguration configuration)
        {
            if (_initialized) return;

            lock (_lock)
            {
                if (_initialized) return;

                try
                {
                    _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
                    _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
                    _unitOfWork = serviceProvider.GetRequiredService<IUnitOfWork>();

                    InitializeConnectionPool();

                    _initialized = true;

                    Console.WriteLine("DBServerProvider 初始化成功加载连接字符串");
                }
                catch (Exception ex)
                {
                    var message = $"DBServerProvider initialization failed: {ex.Message}";
                    Console.WriteLine(message);
                    // 记录详细错误日志
                    LogError(ex, message);
                    throw new InvalidOperationException(message, ex);
                }
            }
        }

        private static void InitializeConnectionPool()
        {
            try
            {
                if (string.IsNullOrEmpty(AppSetting.MainDBConnecString))
                {
                    throw new InvalidOperationException("主数据库连接字符串未配置");
                }

                // 1. 设置系统默认连接
                SetConnection(MainDB, AppSetting.MainDBConnecString);

                // 2. 加载本地配置
                LoadLocalConfigs();

                // 3. 尝试加载数据库配置
                LoadDatabaseConfigs();
            }
            catch (Exception ex)
            {
                var message = $"初始化数据库连接字符串池失败: {ex.Message}";
                Console.WriteLine(message);
                LogError(ex, message);
                throw;
            }
        }

        // 系统与基础库标识符
        private const string MainDB = "SysDbContext";

        private static string GetConnKeyByProperty(string propertyName)
        {
            // 如果属性名以Conn结尾，移除它
            if (propertyName.EndsWith("Conn"))
            {
                propertyName = propertyName.Substring(0, propertyName.Length - 4);
            }
            // DbContext直接返回
            if (propertyName.EndsWith("DbContext"))
            {
                return propertyName;
            }
            return $"{propertyName}DbContext";
        }

        private static string GetConnectionByProperty(string propertyName)
        {
            return GetDbConnectionString(GetConnKeyByProperty(propertyName));
        }

        static DBServerProvider()
        {
        }

        private static void InitializeConnections()
        {
            try
            {
                // 1. 先加载系统默认连接，用于访问配置表
                SetConnection(MainDB, AppSetting.MainDBConnecString);

                // 2. 尝试从数据库加载配置
                LoadDatabaseConfigs();

                // 3. 从本地配置加载缺失的连接
                LoadLocalConfigs();
            }
            catch (Exception ex)
            {
                // 如果数据库加载失败，至少确保使用本地配置
                SetConnection(MainDB, AppSetting.MainDBConnecString);
                LoadLocalConfigs();
                Console.WriteLine($"从数据库加载配置失败，使用本地配置: {ex.Message}");
            }
        }

        private static string DecryptPassword(string encryptedPassword)
        {
            if (string.IsNullOrEmpty(encryptedPassword))
                return string.Empty;

            try
            {
                return encryptedPassword.DecryptDES(AppSetting.Secret.DB);
            }
            catch
            {
                return encryptedPassword; // 如果解密失败，返回原始值
            }
        }

        private static void LoadLocalConfigs()
        {
            // 从本地配置加载，但不覆盖已存在的连接
            foreach (var conn in AppSetting._connection.DbContexts)
            {
                if (conn.Key.EndsWith("DbContext") && !ConnectionPool.ContainsKey(conn.Key))
                {
                    SetConnection(conn.Key, conn.Value);
                }
            }
        }

        /// <summary>
        /// 刷新所有连接配置
        /// </summary>
        public static void RefreshAllConnections()
        {
            ConnectionPool.Clear();
            InitializeConnections();
        }

        private static string BuildConnectionString(Sys_DatabaseConfigs config)
        {
            var builder = new System.Data.Common.DbConnectionStringBuilder();

            switch (config.DbType.ToLower())
            {
                case "mssql":
                    builder["Data Source"] = $"{config.Server},{config.Port}";
                    builder["Initial Catalog"] = config.DatabaseName;
                    if (config.AuthenticationType == 1) // Windows身份验证
                    {
                        builder["Integrated Security"] = true;
                    }
                    else
                    {
                        builder["User ID"] = config.UserName;
                        builder["Password"] = DecryptPassword(config.EncryptedPassword);
                    }
                    builder["TrustServerCertificate"] = true;
                    break;

                case "mysql":
                    builder["Server"] = config.Server;
                    builder["Port"] = config.Port;
                    builder["Database"] = config.DatabaseName;
                    builder["User Id"] = config.UserName;
                    builder["Password"] = DecryptPassword(config.EncryptedPassword);
                    break;

                    // ... 其他数据库类型的处理 ...
            }

            // 添加自定义设置
            if (!string.IsNullOrEmpty(config.AdditionalSettings))
            {
                foreach (var setting in config.AdditionalSettings.Split(';'))
                {
                    var parts = setting.Split('=');
                    if (parts.Length == 2)
                    {
                        builder[parts[0].Trim()] = parts[1].Trim();
                    }
                }
            }

            return builder.ConnectionString;
        }

        // 简化的连接获取方法
        private static string GetDbConn(string dbKey) => GetDbConnectionString($"{dbKey}DbContext");

        // 常用连接字符串属性
        public static string SysConnectingString => GetDbConn("Sys");

        public static string ServiceConnectingString => AppSetting.UseDynamicShareDB
            ? GetDbConnectionString(UserContext.CurrentServiceId.ToString())
            : GetDbConn("Service");

        // 业务库连接使用动态属性访问器
        public static string GetConnection(string propertyName) => GetConnectionByProperty(propertyName);

        // 所有原有的连接属性可以简化为:
        public static string JL_MESConn => GetConnection(nameof(JL_MESConn));

        public static string JL_WCSConn => GetConnection(nameof(JL_WCSConn));

        // ... 其他属性类似

        public static void SetConnection(string key, string val,string Version = "")
        {
            var config = new DBConnConfig
            {
                Connection = val,
                DbType = DBType.Name,  // 使用默认数据库类型
                Version = Version  // 可以根据需要设置版本
            };

            ConnectionPool.AddOrUpdate(key, config, (k, old) => config);
        }
        public static void SetConnection(string key, DBConnConfig val)
        {
            ConnectionPool.AddOrUpdate(key, val, (k, old) => val);
        }
   
        public static string GetConnectionString(string key)
        {
            key = key ?? MainDB;
            return ConnectionPool.TryGetValue(key, out var config) ? config.Connection : key;
        }

        public static DBConnConfig GetConnConfig(string key)
        {
            key = key ?? MainDB;
            var c =  ConnectionPool.TryGetValue(key, out var config) ? config : ConnectionPool[MainDB];
            return c;
        }


        

        private static T RetryOnFailure<T>(Func<T> operation)
        {
            for (int i = 0; i < MaxRetries; i++)
            {
                try
                {
                    return operation();
                }
                catch (Exception ex) when (i < MaxRetries - 1)
                {
                    LogError(ex, $"连接重试 {i + 1}/{MaxRetries}");
                    Thread.Sleep(RetryDelay);
                }
            }
            return operation(); // 最后一次尝试
        }

        public static ConnectionPoolMetrics GetConnectionPoolMetrics()
        {
            return new ConnectionPoolMetrics
            {
                TotalConnections = ConnectionPool.Count,
                ActiveConnections = GetUnitOfWork().GetActiveContexts(),
                LastRefreshTime = DateTime.Now,
                Configurations = ConnectionPool.Keys.ToList()  // 添加当前配置的键列表
            };
        }

        private static void LogError(Exception ex, string message)
        {
            try
            {
                string logPath = Path.Combine(AppContext.BaseDirectory, "Log/DBProvider");
                string fileName = $"error_{DateTime.Now:yyyyMMdd}.log";

                Directory.CreateDirectory(logPath);

                var logEntry = new
                {
                    Timestamp = DateTime.Now,
                    Message = message,
                    Exception = ex.ToString(),
                    StackTrace = ex.StackTrace,
                    ConnectionPool = ConnectionPool.Keys
                };

                File.AppendAllText(
                    Path.Combine(logPath, fileName),
                    JsonSerializer.Serialize(logEntry, new JsonSerializerOptions { WriteIndented = true })
                );
            }
            catch (Exception logEx)
            {
                Console.WriteLine($"写入错误日志失败: {logEx.Message}");
            }
        }

        /// <summary>
        /// 获取系统 EF
        /// </summary>
        public static BaseDbContext DbContext
        {
            get { return GetEFDbContext(); }
        }

        /// <summary>
        /// 获取系统 EF
        /// </summary>
        public static BaseDbContext GetEFDbContext()
        {
            return GetEFDbContext(null);
        }

    public static void RegisterDbContext<TContext>(
    IServiceCollection services,
    IConfiguration configuration)
    where TContext : BaseDbContext<TContext>
{
    try
    {
        Console.WriteLine($"开始注册数据库上下文: {typeof(TContext).Name}");
        
        // 获取连接配置
        var config = GetConnConfig(typeof(TContext).Name);
        if (config == null)
        {
            throw new InvalidOperationException(
                $"未找到 {typeof(TContext).Name} 的数据库连接配置");
        }

        // 使用标准的 AddDbContext 方法进行注册
        services.AddDbContext<TContext>(options =>
        {
            DbTypeConfigurer.ConfigureDbType(
                options,
                config.Connection,
                config.DbType,
                config.Version,
                enableDetailedLogging: true
            );
        });


      //  Console.WriteLine($"成功注册数据库上下文: {typeof(TContext).Name}");
    }
    catch (Exception ex)
    {
        var message = $"注册数据库上下文 {typeof(TContext).Name} 失败: {ex.Message}";
        Console.WriteLine(message);
        LogError(ex, message);
        throw;
    }
}
        public static BaseDbContext GetEFDbContext(string dbName = null)
        {
            try
            {
                EnsureInitialized();

                if (string.IsNullOrEmpty(dbName))
                {
                    return _unitOfWork.GetDbContext<SysDbContext>();
                }

                // 获取所有已加载的程序集
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                Type contextType = null;

                // 在所有程序集中查找匹配的DbContext类型（支持泛型和非泛型）
                foreach (var assembly in assemblies)
                {
                    contextType = assembly.GetTypes()
                        .FirstOrDefault(t => t.Name.Equals(dbName, StringComparison.OrdinalIgnoreCase)
                                        && (typeof(BaseDbContext).IsAssignableFrom(t) ||
                                            IsGenericBaseDbContext(t)));

                    if (contextType != null)
                        break;
                }

                if (contextType == null)
                {
                    throw new InvalidOperationException($"未找到名为[{dbName}]的数据库上下文类型");
                }

                // 使用反射调用泛型方法
                var method = typeof(IUnitOfWork).GetMethod(nameof(IUnitOfWork.GetDbContext));
                var genericMethod = method.MakeGenericMethod(contextType);
                var parameters = new object[] { false }; // 默认不启用跟踪
                var dbContext = (BaseDbContext)genericMethod.Invoke(_unitOfWork, parameters);

                return dbContext;
            }
            catch (Exception ex)
            {
                var message = $"获取数据库上下文失败: {ex.Message}";
                LogError(ex, message);
                throw new InvalidOperationException(message, ex);
            }
        }

        private static void EnsureInitialized()
        {
            if (!_initialized || _serviceProvider == null)
            {
                throw new InvalidOperationException(
                    "DBServerProvider not initialized. Ensure Configure method is called during application startup.");
            }
        }

        /// <summary>
        /// 检查类型是否为 DbContext 类型（支持泛型和非泛型 BaseDbContext）
        /// </summary>
        public static bool IsDbContextType(Type type)
        {
            // 检查是否继承自非泛型 BaseDbContext
            if (typeof(BaseDbContext).IsAssignableFrom(type))
                return true;

            // 检查是否继承自泛型 BaseDbContext<T>
            return IsGenericBaseDbContext(type);
        }

        /// <summary>
        /// 检查类型是否继承自泛型 BaseDbContext&lt;T&gt;
        /// </summary>
        private static bool IsGenericBaseDbContext(Type type)
        {
            var baseType = type.BaseType;
            while (baseType != null)
            {
                if (baseType.IsGenericType &&
                    baseType.GetGenericTypeDefinition() == typeof(BaseDbContext<>))
                {
                    return true;
                }
                baseType = baseType.BaseType;
            }
            return false;
        }

        private static void LoadDatabaseConfigs()
        {
            try
            {
                var optionsBuilder = new DbContextOptionsBuilder<SysDbContext>();
                var defaultConfig = AppSetting._connection.MainDb;

                DbTypeConfigurer.ConfigureDbType(
                    optionsBuilder,
                    defaultConfig.Connection,
                    defaultConfig.DbType,
                    defaultConfig.Version,
                    enableDetailedLogging: false
                );

                // 使用临时上下文，这里可以使用 using 因为是局部作用域
                using (var tempContext = new SysDbContext(optionsBuilder.Options))
                {
                    tempContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                    var configs = tempContext.Set<Sys_DatabaseConfigs>()
                        .Where(x => x.IsActive)
                        .ToList();

                    foreach (var config in configs)
                    {
                        try
                        {
                            string connectionString = BuildConnectionString(config);
                            string connKey = $"{config.Name}DbContext";
                            SetConnection(connKey, connectionString, config.Version);
                        }
                        catch (Exception ex)
                        {
                            LogError(ex, $"加载数据库配置失败 [{config.Name}]");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex, "加载数据库配置失败");
                LoadLocalConfigs();
            }
        }

        public static void RefreshConnection(string dbContextName)
        {
            BaseDbContext dbContext = null;
            try
            {
                dbContext = GetEFDbContext();
                var config = dbContext.Set<Sys_DatabaseConfigs>()
                    .FirstOrDefault(x => x.IsActive && x.Name + "DbContext" == dbContextName);

                if (config != null)
                {
                    SetConnection(dbContextName, BuildConnectionString(config),config.Version);
                }
                else if (AppSetting._connection.DbContexts.TryGetValue(dbContextName, out var localConfig))
                {
                    SetConnection(dbContextName, localConfig);
                }
            }
            finally
            {
                if (dbContext != null)
                {
                    dbContext.Dispose();
                }
            }
        }

        /// <summary>
        /// 根据实体model获取对应EF
        /// </summary>
        /// <typeparam name="TEntity"></typeparam>
        /// <returns></returns>
        public static BaseDbContext GetEFDbContext<TEntity>()
        {
            string dbServer = typeof(TEntity).GetTypeCustomValue<EntityAttribute>(x => x.DBServer);

            return GetEFDbContext(dbServer);
        }

        public static void SetDbContextConnection(SysDbContext sysContext, string dbName)
        {
            if (!ConnectionPool.ContainsKey(dbName))
            {
                throw new Exception("数据库连接名称错误");
            }
            sysContext.Database.GetDbConnection().ConnectionString = ConnectionPool[dbName].Connection;
        }

        /// <summary>
        /// 指定获取数据库,这里同时支持mysql、sqlserver等不同类型数据库2024.06.20
        /// 需要在appsettings.json中Connection添加xxxDbTyp:"MySql/SqlServe等属性"
        /// </summary>
        /// <param name="dbService"></param>
        /// <returns></returns>
        ///
        public static string GetDbEntityName(string dbServer)
        {
            return EntityTypeCache.GetDbEntityType(dbServer).Name;
        }

        public static string GetDbConnectionString(string key)
        {
            if (ConnectionPool.TryGetValue(key, out var config))
            {
                return config.Connection;
            }
            throw new Exception($"未配置[{key}]的数据库连接");
        }

        // 获取工作单元
        public static IUnitOfWork GetUnitOfWork()
        {
            EnsureInitialized();
            return _unitOfWork;
        }

        // 获取DbContext,建议优先使用GetUnitOfWork
        public static BaseDbContext GetDbContext<TDbContext>() where TDbContext : BaseDbContext<TDbContext>
        {
            return GetUnitOfWork().GetDbContext<TDbContext>();
        }

        /// <summary>
        /// 根据实体类型获取对应的DbContext
        /// </summary>
        /// <param name="entityType">实体类型</param>
        /// <returns>对应的DbContext实例</returns>
        public static BaseDbContext GetDbContextByEntityType(Type entityType)
        {
            EnsureInitialized();
            return _unitOfWork.GetDbContextByEntityType(entityType);
        }

        /// <summary>
        /// 根据实体类型获取对应的DbContext
        /// </summary>
        /// <typeparam name="TEntity">实体类型</typeparam>
        /// <returns>对应的DbContext实例</returns>
        public static BaseDbContext GetDbContextByEntityType<TEntity>()
        {
            return GetDbContextByEntityType(typeof(TEntity));
        }

        public static async Task<bool> CheckConnectionAsync(string connectionKey)
        {
            try
            {
                using var conn = GetDbConnection(GetConnectionString(connectionKey));
                // 根据不同连接类型调用对应的异步方法
                switch (conn)
                {
                    case SqlConnection sqlConn:
                        await sqlConn.OpenAsync();
                        break;

                    case MySqlConnection mysqlConn:
                        await mysqlConn.OpenAsync();
                        break;

                    case NpgsqlConnection npgsqlConn:
                        await npgsqlConn.OpenAsync();
                        break;

                    case OracleConnection oracleConn:
                        await oracleConn.OpenAsync();
                        break;

                    default:
                        // 对于不支持异步的连接，使用同步方法
                        conn.Open();
                        break;
                }
                return true;
            }
            catch (Exception ex)
            {
                LogError(ex, $"Connection check failed for {connectionKey}");
                return false;
            }
        }

        private static async Task<T> RetryOnFailureAsync<T>(Func<Task<T>> operation)
        {
            for (int i = 0; i < MaxRetries; i++)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex) when (i < MaxRetries - 1)
                {
                    LogError(ex, $"Operation failed, attempt {i + 1} of {MaxRetries}");
                    await Task.Delay(RetryDelay);
                }
            }
            return await operation(); // 最后一次尝试
        }
    }

    public class ConnectionPoolMetrics
    {
        public int TotalConnections { get; set; }
        public int ActiveConnections { get; set; }
        public DateTime LastRefreshTime { get; set; }
        public List<string> Configurations { get; set; }  // 添加当前配置的键列表
    }
}