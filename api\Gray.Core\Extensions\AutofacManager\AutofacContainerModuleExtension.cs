﻿using Autofac;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;
using Gray.Core.CacheManager;
using Gray.Core.Configuration;
using Gray.Core.Const;
using Gray.Core.Dapper;
using Gray.Core.DBManager;
using Gray.Core.EFDbContext;
using Gray.Core.Enums;
using Gray.Core.Extensions.AutofacManager;
//using Gray.Core.KafkaManager.IService;
//using Gray.Core.KafkaManager.Service;
using Gray.Core.ManageUser;
using Gray.Core.ObjectActionValidator;
using Gray.Core.Services;
using Gray.Core.UnitOfWorkMange;
using Gray.Core.Transaction;

namespace Gray.Core.Extensions
{
    public static class AutofacContainerModuleExtension
    {
        private static void RegisterAllDbContexts(IServiceCollection services, IConfiguration configuration)
        {
            try
            {
                // 获取所有项目程序集
                var assemblies = DependencyContext.Default.RuntimeLibraries
                    .Where(lib => lib.Type == "project")
                    .Select(lib => AssemblyLoadContext.Default.LoadFromAssemblyName(
                        new AssemblyName(lib.Name)))
                    .ToList();

                // 查找所有继承自 BaseDbContext 的具体类（支持泛型和非泛型）
                var dbContextTypes = assemblies
                    .SelectMany(a => a.GetTypes())
                    .Where(t => t.IsClass
                        && !t.IsAbstract
                        && (t.IsSubclassOf(typeof(BaseDbContext)) ||
                            (t.BaseType?.IsGenericType == true &&
                             t.BaseType.GetGenericTypeDefinition() == typeof(BaseDbContext<>)))
                        && t != typeof(BaseDbContext) // 排除基类本身
                        && t != typeof(SysDbContext))
                    .ToList();

                // 注册每个找到的 DbContext
                foreach (var contextType in dbContextTypes)
                {
                    try
            {
        //        Console.WriteLine($"正在注册动态发现的 DbContext: {contextType.Name}");
                // 使用反射调用泛型注册方法
                var registerMethod = typeof(DBServerProvider)
                    .GetMethod(nameof(DBServerProvider.RegisterDbContext))
                    .MakeGenericMethod(contextType);

                registerMethod.Invoke(null, new object[] { services, configuration });
                Console.WriteLine($"成功注册 DbContext: {contextType.Name}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"注册 {contextType.Name} 失败: {ex.Message}");
            }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(
                    "Failed to register database contexts", ex);
            }
        }
        //  private static bool _isMysql = false;
        public static IServiceCollection AddModule(this IServiceCollection services, ContainerBuilder builder, IConfiguration configuration)
        {
            //services.AddSession();
            //services.AddMemoryCache();
            //初始化配置文件
            AppSetting.Init(services, configuration);
            // 注册工作单元
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            builder.RegisterType<UnitOfWork>()
               .As<IUnitOfWork>()
               .InstancePerLifetimeScope();



            // 注册异步事务上下文（新的统一事务管理）
            services.AddScoped<IAsyncTransactionContext, AsyncTransactionContext>();
            builder.RegisterType<AsyncTransactionContext>()
               .As<IAsyncTransactionContext>()
               .InstancePerLifetimeScope();

               
            var serviceProvider = services.BuildServiceProvider();

            DBServerProvider.Configure(serviceProvider, configuration);
        

            //注册所有除了SysDb的上下文
            RegisterAllDbContexts(services, configuration);

           

            Type baseType = typeof(IDependency);
            var compilationLibrary = DependencyContext.Default
                .RuntimeLibraries
                .Where(x => !x.Serviceable
                && x.Type == "project")
                .ToList();
            var count1 = compilationLibrary.Count;
            List<Assembly> assemblyList = new List<Assembly>();

            foreach (var _compilation in compilationLibrary)
            {
                try
                {
                    assemblyList.Add(AssemblyLoadContext.Default.LoadFromAssemblyName(new AssemblyName(_compilation.Name)));
                }
                catch (Exception ex)
                {
                    Console.WriteLine(_compilation.Name + ex.Message);
                }
            }
            //插件式开发
            //try
            //{
            //    var provider = services.BuildServiceProvider();
            //    IWebHostEnvironment webHostEnvironment = provider.GetRequiredService<IWebHostEnvironment>();
            //    string rootPath = (webHostEnvironment.ContentRootPath + "\\plugs").ReplacePath();
            //    foreach (var item in Directory.GetFiles(rootPath).Where(x => x.EndsWith(".dll")))
            //    {
            //        string path = ($"{item}").ReplacePath();
            //        AssemblyName assemblyName = Assembly.LoadFrom(path).GetName(); ;
            //        assemblyList.Add(AssemblyLoadContext.Default.LoadFromAssemblyName(assemblyName));
            //    }
            //}
            //catch (Exception ex)
            //{
            //    Console.WriteLine($"解析类库异常：{ex.Message + ex.StackTrace}");
            //}

            var data = builder.RegisterAssemblyTypes(assemblyList.ToArray())
               .Where(type => baseType.IsAssignableFrom(type) && !type.IsAbstract);

            data.AsSelf().AsImplementedInterfaces()
            .InstancePerLifetimeScope();


            builder.RegisterType<UserContext>().InstancePerLifetimeScope();
            builder.RegisterType<ActionObserver>().InstancePerLifetimeScope();
            //model校验结果
            builder.RegisterType<ObjectModelValidatorState>().InstancePerLifetimeScope();
            //string connectionString = DBServerProvider.GetConnectionString(null);

            //services.AddDbContextPool<SysDbContext>(optionsBuilder => { optionsBuilder.UseSqlServer(connectionString); }, 64);

            //services.AddDbContextPool<ServiceDbContext>(optionsBuilder => { optionsBuilder.UseSqlServer(connectionString); }, 64);
            //if (DBType.Name == DbCurrentType.PgSql.ToString())
            {
                AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
                AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);
            }

            //启用缓存
            if (AppSetting.UseRedis)
            {
                builder.RegisterType<RedisCacheService>().As<ICacheService>().SingleInstance();
            }
            else
            {
                builder.RegisterType<MemoryCacheService>().As<ICacheService>().SingleInstance();
            }
            DapperParseGuidTypeHandler.InitParseGuid();
            DbCache.Init();
            //kafka注入
            //if (AppSetting.Kafka.UseConsumer)
            //    builder.RegisterType<KafkaConsumer<string, string>>().As<IKafkaConsumer<string, string>>().SingleInstance();
            //if (AppSetting.Kafka.UseProducer)
            //    builder.RegisterType<KafkaProducer<string, string>>().As<IKafkaProducer<string, string>>().SingleInstance();
            return services;
        }
    }
}