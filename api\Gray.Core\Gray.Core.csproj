﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName></SccProjectName>
    <SccProvider></SccProvider>
    <SccAuxPath></SccAuxPath>
    <SccLocalPath></SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ApplicationIcon />
    <OutputType>Library</OutputType>
    <StartupObject />
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;EF1001;</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;EF1001;</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="IRepositories\**" />
    <Compile Remove="IServices\**" />
    <Compile Remove="Repositories\**" />
    <Compile Remove="Services\API\**" />
    <Compile Remove="Services\Base\**" />
    <EmbeddedResource Remove="IRepositories\**" />
    <EmbeddedResource Remove="IServices\**" />
    <EmbeddedResource Remove="Repositories\**" />
    <EmbeddedResource Remove="Services\API\**" />
    <EmbeddedResource Remove="Services\Base\**" />
    <None Remove="IRepositories\**" />
    <None Remove="IServices\**" />
    <None Remove="Repositories\**" />
    <None Remove="Services\API\**" />
    <None Remove="Services\Base\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Autofac" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="CSRedisCore" />
    <PackageReference Include="Dapper" />
    <PackageReference Include="EFCore.BulkExtensions" />
    <PackageReference Include="EntityFrameworkCore.UseRowNumberForPaging" />
    <PackageReference Include="EPPlus" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Microsoft.Extensions.Caching.Redis.Core" />
    <PackageReference Include="Microsoft.Extensions.DependencyModel" />
    <PackageReference Include="Npgsql" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" />
    <PackageReference Include="NPOI" />
    <PackageReference Include="Oracle.EntityFrameworkCore" />
    <!--<PackageReference Include="MySql.Data" Version="8.0.13" />-->
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" />
    <PackageReference Include="Quartz" />
    <PackageReference Include="Refit" />
    <PackageReference Include="Refit.HttpClientFactory" />
    <PackageReference Include="Refit.Newtonsoft.Json" />
    <PackageReference Include="Serilog" />
    <PackageReference Include="Serilog.Enrichers.Environment" />
    <PackageReference Include="Serilog.Enrichers.Thread" />
    <PackageReference Include="Serilog.Settings.Configuration" />
    <PackageReference Include="Serilog.Sinks.Console" />
    <PackageReference Include="Serilog.Sinks.File" />
    <PackageReference Include="Serilog.Sinks.MSSqlServer" />
    <PackageReference Include="SkiaSharp" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
    <PackageReference Include="ZKWeb.System.Drawing" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Gray.Entity\Gray.Entity.csproj" />
  </ItemGroup>

</Project>
