/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    
    public partial class v_K3Info
    {
        //此处配置字段(字段配置见此model的另一个partial),如果表中没有此字段请加上 [NotMapped]属性，否则会异常

        [NotMapped]
        [Display(Name = "本区上月")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public double 本区上月 { get; set; } = 0;

        [NotMapped]
        [Display(Name = "今年发货")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public double 今年发货 { get; set; } =0;

        [NotMapped]
        [Display(Name = "本月排产")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public double 本月排产 { get; set; } = 0;

        [NotMapped]
        [Display(Name = "本月内销排产")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public double 本月内销排产 { get; set; } = 0;

        [NotMapped]
        [Display(Name = "本月外销排产")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public double 本月外销排产 { get; set; } = 0;

        [NotMapped]
        [Display(Name = "排产开始时间")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public double 排产开始时间 { get; set; } = 0;

        [NotMapped]
        [Display(Name = "内销库存")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public double 内销库存 { get; set; } = 0;

        [NotMapped]
        [Display(Name = "所有正品库存")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public double 所有正品库存 { get; set; } = 0;

       

        [NotMapped]
        [Display(Name = "本月生产")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public double? 本月生产 { get; set; } = 0;

        [NotMapped]
        [Display(Name = "最近24小时生产")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public double? 最近24小时生产 { get; set; } = 0;


        [NotMapped]
        [Display(Name = "排产结束时间")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public double 排产结束时间 { get; set; } = 0;

        [NotMapped]
        [Display(Name = "平均排产条数")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public double 平均排产条数 { get; set; } = 0;

        [NotMapped]
        [Display(Name = "当日计划")]
        [Column(TypeName = "numeric")]
        [Editable(true)]
        [Required(AllowEmptyStrings = false)]
        public int? 当日计划 { get; set; } = 0;

        public List<CureMonthPlanDetail> 月计划明细 { get; set; }
    }
}