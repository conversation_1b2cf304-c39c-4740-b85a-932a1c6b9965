/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "半成品日计划",TableName = "HalfPlanDaily",DBServer = "JL_APSDbContext")]
    public partial class HalfPlanDaily:JL_APSEntity
    {
        /// <summary>
       ///工序
       /// </summary>
       [Display(Name ="工序")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string 工序 { get; set; }

       /// <summary>
       ///部件名
       /// </summary>
       [Display(Name ="部件名")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string 部件名 { get; set; }

       /// <summary>
       ///分类
       /// </summary>
       [Display(Name ="分类")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string 分类 { get; set; }

       /// <summary>
       ///半成品代码
       /// </summary>
       [Display(Name ="半成品代码")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string 半成品代码 { get; set; }

       /// <summary>
       ///班次
       /// </summary>
       [Display(Name ="班次")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string 班次 { get; set; }

       /// <summary>
       ///日期
       /// </summary>
       [Display(Name ="日期")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? 日期 { get; set; }

       /// <summary>
       ///接班理论库存
       /// </summary>
       [Display(Name ="接班理论库存")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? 接班理论库存 { get; set; }

       /// <summary>
       ///当班成型需求
       /// </summary>
       [Display(Name ="当班成型需求")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? 当班成型需求 { get; set; }

       /// <summary>
       ///计划数量
       /// </summary>
       [Display(Name ="计划数量")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? 计划数量 { get; set; }

       /// <summary>
       ///实际完成数量
       /// </summary>
       [Display(Name ="实际完成数量")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? 实际完成数量 { get; set; }

       /// <summary>
       ///未完成计划原因
       /// </summary>
       [Display(Name ="未完成计划原因")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string 未完成计划原因 { get; set; }

       /// <summary>
       ///备注
       /// </summary>
       [Display(Name ="备注")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string 备注 { get; set; }

       /// <summary>
       ///Id
       /// </summary>
       [Key]
       [Display(Name ="Id")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int Id { get; set; }

       
    }
}