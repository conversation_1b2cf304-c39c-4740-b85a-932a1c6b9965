using System;

namespace Gray.Core.Extensions.Exceptions
{
    public class StoredSqlException : Exception
    {
        public StoredSqlException(string message) : base(message)
        {
        }

        public StoredSqlException(string message, Exception inner) : base(message, inner)
        {
        }

        // 添加更多异常信息属性
        public string SqlName { get; set; }

        public string Parameters { get; set; }
        public string ExecutionTime { get; set; }
        public StoredSqlErrorType ErrorType { get; set; }
    }

    public enum StoredSqlErrorType
    {
        NotFound = 1,
        Disabled = 2,
        ParameterValidationFailed = 3,
        ExecutionError = 4,
        PerformanceWarning = 5
    }
}