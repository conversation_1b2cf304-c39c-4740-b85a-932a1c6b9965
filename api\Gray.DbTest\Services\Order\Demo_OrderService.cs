/*
Tel:15662337743
 *所有业务编写全部应在Partial文件夹下Demo_OrderService与IDemo_OrderService中编写
 */
using Gray.DbTest.IRepositories;
using Gray.DbTest.IServices;
using Gray.Core.BaseProvider;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.DomainModels;

namespace Gray.DbTest.Services
{
    public partial class Demo_OrderService : ServiceBase<Demo_Order, IDemo_OrderRepository>
    , IDemo_OrderService, IDependency
    {
    public Demo_OrderService(IDemo_OrderRepository repository)
    : base(repository)
    {
    Init(repository);
    }
    public static IDemo_OrderService Instance
    {
      get { return AutofacContainerModule.GetService<IDemo_OrderService>(); } }
    }
 }
