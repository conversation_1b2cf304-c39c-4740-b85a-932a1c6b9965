﻿using Microsoft.Extensions.Configuration;
using Quartz.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Mail;
using System.Runtime.CompilerServices;
using System.Text;
using Gray.Core.Configuration;
using Gray.Core.Extensions;
using Gray.Core.Services;
using Logger = Gray.Core.Services.Logger;

namespace Gray.Core.Utilities
{
    public static class MailHelper
    {
        private static string address { get; set; }
        private static string authPwd { get; set; }
        private static string name { get; set; }
        private static string host { get; set; }
        private static int port;
        public static List<string> Attachments { get; set; }
        public static List<string> mailCC { get; set; }
        private static bool enableSsl { get; set; }
        static MailHelper()
        {
            IConfigurationSection section = AppSetting.GetSection("Mail");
            address = section["Address"];
            authPwd = section["AuthPwd"];
            name = section["Name"];
            host = section["Host"];
            port = section["Port"].GetInt();
            enableSsl = section["EnableSsl"].GetBool();
        }

        /// <summary>
        /// 发送邮件
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="content">内容</param>
        /// <param name="tolist">收件人</param>
        public static void Send(string title, string content, bool ishtmlbody = false, params string[] tolist)
        {
            Console.WriteLine(AppSetting.GetSection("ModifyMember")["DateUTCField"]);
            using MailMessage message = new MailMessage
            {
                From = new MailAddress(address, name)//发送人邮箱
            };
            foreach (var item in tolist)
            {
                // 验证邮箱地址格式并添加错误处理
                if (!string.IsNullOrWhiteSpace(item) && !item.Contains(";"))
                {
                    try
                    {
                        message.To.Add(item);//收件人地址
                    }
                    catch (FormatException ex)
                    {
                        Logger.Error($"邮箱地址格式不正确，已跳过: {item}，错误信息: {ex.Message}");
                    }
                }
                else
                {
                    Logger.Error($"邮箱地址包含无效字符或为空，已跳过: {item}");
                }
            }
            if (mailCC!=null)
            {
                foreach (var item in mailCC)
                {
                    // 验证抄送邮箱地址格式并添加错误处理
                    if (!string.IsNullOrWhiteSpace(item) && !item.Contains(";"))
                    {
                        try
                        {
                            message.CC.Add(item);
                        }
                        catch (FormatException ex)
                        {
                            Logger.Error($"抄送邮箱地址格式不正确，已跳过: {item}，错误信息: {ex.Message}");
                        }
                    }
                    else
                    {
                        Logger.Error($"抄送邮箱地址包含无效字符或为空，已跳过: {item}");
                    }
                }
            }
            if (Attachments!=null)
            {
                foreach (var item in Attachments)
                {
                    if  (Path.Exists(item))
                    {
                        message.Attachments.Add(new Attachment(item));
                    }
                    else
                    {
                        Logger.Error("发送邮件但是附件不存在,已经跳过" + item);
                    }
                    
                }
            }
            // 检查是否有有效的收件人
            if (message.To.Count == 0)
            {
                Logger.Error("没有有效的收件人地址，邮件发送已取消");
                return;
            }

            message.Subject = title;//发送邮件的标题
            message.Body = content;//发送邮件的内容
            message.IsBodyHtml = ishtmlbody;
                                   //配置smtp服务地址
            using SmtpClient client = new SmtpClient
            {
                Host = host,
                Port = port,//端口587
                EnableSsl = enableSsl,
                //发送人邮箱与授权密码
                Credentials = new NetworkCredential(address, authPwd)
            };
            client.Send(message);
            Attachments = null;
            mailCC = null;
        }
    }
}
