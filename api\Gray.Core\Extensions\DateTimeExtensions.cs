﻿using System;

namespace Gray.Core.Extensions
{
    public static class DateTimeExtensions
    {
        /// <summary>
        /// 取得某年的第一天
        /// </summary>
        /// <param name="datetime">要取得月份第一天的时间</param>
        /// <returns></returns>
        public static DateTime FirstDayOfYear(this DateTime datetime)
        {
            return datetime.AddMonths(1 - datetime.Month).AddDays(1 - datetime.Day);
        }

        /// <summary>
        /// 取得某周的第一天
        /// </summary>
        /// <param name="datetime">要取得时间所在第一天的时间</param>
        /// <returns></returns>
        public static DateTime FirstDayOfWeek(this DateTime datetime)
        {
            DateTime dt = datetime;
            int dayOfWeek = -1 * (int)dt.Date.DayOfWeek;
            //Sunday = 0,Monday = 1,Tuesday = 2,Wednesday = 3,Thursday = 4,Friday = 5,Saturday = 6,

            DateTime weekStartTime = dt.AddDays(dayOfWeek + 1);//取本周一
            if (dayOfWeek == 0) //如果今天是周日，则开始时间是上周一
            {
                weekStartTime = weekStartTime.AddDays(-7);
            }
            return weekStartTime.Date;
        }

        /// <summary>
        /// 取得某周的最后一天
        /// </summary>
        /// <param name="datetime">要取得时间所在第一天的时间</param>
        /// <returns></returns>
        public static DateTime LastDayOfWeek(this DateTime datetime)
        {
            DateTime dt = datetime;
            int dayOfWeek = -1 * (int)dt.Date.DayOfWeek;
            //Sunday = 0,Monday = 1,Tuesday = 2,Wednesday = 3,Thursday = 4,Friday = 5,Saturday = 6,

            DateTime weekStartTime = dt.AddDays(dayOfWeek + 1);//取本周一
            if (dayOfWeek == 0) //如果今天是周日，则开始时间是上周一
            {
                weekStartTime = weekStartTime.AddDays(-7);
            }
            return weekStartTime.Date.AddDays(7);
        }

        /// <summary>
        /// 某年的最后一天
        /// </summary>
        /// <param name="datetime"></param>
        /// <returns></returns>
        public static DateTime LastDayOfYear(this DateTime datetime)
        {
            return datetime.AddMonths(1 - datetime.Month).AddDays(1 - datetime.Day).AddMonths(12).AddDays(-1);
        }

        /// <summary>
        /// 取得某月的第一天
        /// </summary>
        /// <param name="datetime">要取得月份第一天的时间</param>
        /// <returns></returns>
        public static DateTime FirstDayOfMonth(this DateTime datetime)
        {
            return datetime.AddDays(1 - datetime.Day);
        }

        /// <summary>
        /// 某月的最后一天
        /// </summary>
        /// <param name="datetime"></param>
        /// <returns></returns>
        public static DateTime LastDayOfMonth(this DateTime datetime)
        {
            return datetime.AddDays(1 - datetime.Day).AddMonths(1).AddDays(-1);
        }

        public static DateTime StartOfDay(this DateTime datetime)
        {
            return Convert.ToDateTime(datetime.ToString("D").ToString());
        }

        public static DateTime StartOfHour(this DateTime datetime)
        {
            return Convert.ToDateTime(datetime.ToString("yyyy-MM-dd HH:00:00").ToString());
        }

        public static DateTime EndOfDay(this DateTime datetime)
        {
            return Convert.ToDateTime(datetime.AddDays(1).ToString("D").ToString()).AddSeconds(-1);
        }

        public static DateTime EndOfHour(this DateTime datetime)
        {
            return Convert.ToDateTime(datetime.AddHours(1).ToString("yyyy-MM-dd HH:00:00").ToString()).AddSeconds(-1);
        }

        /// 取得上个月第一天
        /// </summary>
        /// <param name="datetime">要取得上个月第一天的当前时间</param>
        /// <returns></returns>
        public static DateTime FirstDayOfPreviousMonth(this DateTime datetime)
        {
            return datetime.AddDays(1 - datetime.Day).AddMonths(-1);
        }

        /// 取得上个月的最后一天
        /// </summary>
        /// <param name="datetime">要取得上个月最后一天的当前时间</param>
        /// <returns></returns>
        public static DateTime LastDayOfPrdviousMonth(this DateTime datetime)
        {
            return datetime.AddDays(1 - datetime.Day).AddDays(-1);
        }

        /// <summary>
        /// 获取当前时间的时间戳
        /// </summary>
        /// <param name="thisValue"></param>
        /// <returns></returns>
        public static string DateToTimeStamp(this DateTime thisValue)
        {
            TimeSpan ts = thisValue - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            return Convert.ToInt64(ts.TotalSeconds).ToString();
        }
    }
}