namespace Gray.Core.GrayHttpClient.Services
{
    /// <summary>
    /// 通用Token管理器接口
    /// 支持多系统JWT认证的统一管理
    /// </summary>
    public interface IUniversalTokenManager
    {
        /// <summary>
        /// 获取指定系统的访问Token
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <returns>访问Token</returns>
        Task<string?> GetAccessTokenAsync(string systemName);

        /// <summary>
        /// 检查指定系统的Token是否有效
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <returns>是否有效</returns>
        Task<bool> IsTokenValidAsync(string systemName);

        /// <summary>
        /// 刷新指定系统的Token
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <returns>是否刷新成功</returns>
        Task<bool> RefreshTokenAsync(string systemName);

        /// <summary>
        /// 清除指定系统的Token
        /// </summary>
        /// <param name="systemName">系统名称</param>
        Task ClearTokenAsync(string systemName);

        /// <summary>
        /// 清除所有系统的Token
        /// </summary>
        Task ClearAllTokensAsync();

        /// <summary>
        /// 获取所有已配置的系统名称
        /// </summary>
        /// <returns>系统名称列表</returns>
        IEnumerable<string> GetConfiguredSystems();

        /// <summary>
        /// 检查指定系统是否已配置
        /// </summary>
        /// <param name="systemName">系统名称</param>
        /// <returns>是否已配置</returns>
        bool IsSystemConfigured(string systemName);
    }
}
