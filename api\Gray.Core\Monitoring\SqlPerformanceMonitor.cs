using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using Newtonsoft.Json;
using System.Threading;
using System.Threading.Tasks;
using Gray.Core.Extensions.Exceptions;

namespace Gray.Core.Monitoring
{
    public class SqlPerformanceMonitor
    {
        private static readonly ConcurrentDictionary<string, SqlMetrics> _metrics 
            = new ConcurrentDictionary<string, SqlMetrics>();

        public static void RecordExecution(string sqlName, long executionTime)
        {
            var metrics = _metrics.GetOrAdd(sqlName, _ => new SqlMetrics());
            metrics.RecordExecution(executionTime);
        }

        public static SqlMetrics GetMetrics(string sqlName)
        {
            return _metrics.TryGetValue(sqlName, out var metrics) ? metrics : null;
        }

        public static void RecordError(object errorData)
        {
            // 实现错误记录逻辑
            Debug.WriteLine($"SQL错误: {JsonConvert.SerializeObject(errorData)}");
            // TODO: 可以将错误写入日志或发送到监控系统
        }

        public class SqlMetricsError
        {
            public string SqlName { get; set; }
            public string Error { get; set; }
            public long ExecutionTime { get; set; }
            public DateTime Timestamp { get; set; }
            public StoredSqlErrorType ErrorType { get; set; }
        }
    }

    public class SqlMetrics
    {
        private long _totalExecutions;
        private long _totalExecutionTime;
        private long _maxExecutionTime;
        private readonly object _lock = new object();

        public void RecordExecution(long executionTime)
        {
            lock (_lock)
            {
                _totalExecutions++;
                _totalExecutionTime += executionTime;
                if (executionTime > _maxExecutionTime)
                {
                    _maxExecutionTime = executionTime;
                }
            }
        }

        public SqlMetricsSnapshot GetSnapshot()
        {
            return new SqlMetricsSnapshot
            {
                TotalExecutions = Interlocked.Read(ref _totalExecutions),
                AverageExecutionTime = _totalExecutions > 0 
                    ? _totalExecutionTime / _totalExecutions 
                    : 0,
                MaxExecutionTime = Interlocked.Read(ref _maxExecutionTime)
            };
        }
    }

    public class SqlMetricsSnapshot
    {
        public long TotalExecutions { get; set; }
        public long AverageExecutionTime { get; set; }
        public long MaxExecutionTime { get; set; }
    }
}
