using Gray.Core.GrayHttpClient.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.IdentityModel.Tokens.Jwt;
using System.Text.Json;

namespace Gray.Core.GrayHttpClient.Services
{
    /// <summary>
    /// Token管理器实现
    /// 负责JWT Token的获取、刷新和管理
    /// </summary>
    public class TokenManager : ITokenManager
    {
        private readonly ILogger<TokenManager> _logger;
        private readonly HttpClientOptions _options;
        private readonly SemaphoreSlim _refreshSemaphore = new(1, 1);

        private string? _accessToken;
        private string? _refreshToken;
        private DateTime _tokenExpiryTime = DateTime.MinValue;

        public TokenManager(
            ILogger<TokenManager> logger,
            IOptions<HttpClientOptions> options)
        {
            _logger = logger;
            _options = options.Value;
        }

        /// <summary>
        /// 获取当前访问Token
        /// </summary>
        public async Task<string?> GetAccessTokenAsync()
        {
            // 如果Token即将过期，尝试刷新
            if (await IsTokenExpiringSoonAsync() && _options.Authentication.AutoRefreshToken)
            {
                await RefreshTokenAsync();
            }

            return _accessToken;
        }

        /// <summary>
        /// 设置Token
        /// </summary>
        public async Task SetTokenAsync(string accessToken, string? refreshToken = null, int? expiresIn = null)
        {
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new ArgumentException("访问Token不能为空", nameof(accessToken));
            }

            _accessToken = accessToken;
            _refreshToken = refreshToken;

            // 计算过期时间
            if (expiresIn.HasValue)
            {
                _tokenExpiryTime = DateTime.UtcNow.AddSeconds(expiresIn.Value);
            }
            else
            {
                // 尝试从JWT Token中解析过期时间
                try
                {
                    var handler = new JwtSecurityTokenHandler();
                    var jsonToken = handler.ReadJwtToken(accessToken);
                    _tokenExpiryTime = jsonToken.ValidTo;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "无法解析JWT Token的过期时间，使用默认过期时间");
                    _tokenExpiryTime = DateTime.UtcNow.AddHours(1); // 默认1小时过期
                }
            }

            _logger.LogDebug("Token已设置，过期时间: {ExpiryTime}", _tokenExpiryTime);
            await Task.CompletedTask;
        }

        /// <summary>
        /// 刷新Token
        /// </summary>
        public async Task<bool> RefreshTokenAsync()
        {
            if (!_options.Authentication.Enabled || string.IsNullOrEmpty(_refreshToken))
            {
                _logger.LogWarning("Token刷新失败：认证未启用或刷新Token为空");
                return false;
            }

            await _refreshSemaphore.WaitAsync();
            try
            {
                // 再次检查是否需要刷新（防止并发刷新）
                if (!await IsTokenExpiringSoonAsync())
                {
                    return true;
                }

                _logger.LogInformation("开始刷新Token");

                // 这里需要调用刷新Token的API
                // 由于我们还没有实现具体的API客户端，这里先返回false
                // 在实际实现中，应该调用认证服务的刷新Token接口

                _logger.LogWarning("Token刷新功能尚未完全实现");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新Token时发生异常");
                return false;
            }
            finally
            {
                _refreshSemaphore.Release();
            }
        }

        /// <summary>
        /// 清除Token
        /// </summary>
        public async Task ClearTokenAsync()
        {
            _accessToken = null;
            _refreshToken = null;
            _tokenExpiryTime = DateTime.MinValue;

            _logger.LogDebug("Token已清除");
            await Task.CompletedTask;
        }

        /// <summary>
        /// 检查Token是否即将过期
        /// </summary>
        public async Task<bool> IsTokenExpiringSoonAsync()
        {
            if (string.IsNullOrEmpty(_accessToken) || _tokenExpiryTime == DateTime.MinValue)
            {
                return true;
            }

            var thresholdTime = DateTime.UtcNow.AddMinutes(_options.Authentication.TokenRefreshThresholdMinutes);
            var isExpiringSoon = _tokenExpiryTime <= thresholdTime;

            if (isExpiringSoon)
            {
                _logger.LogDebug("Token即将过期，当前时间: {CurrentTime}, 过期时间: {ExpiryTime}, 阈值时间: {ThresholdTime}",
                    DateTime.UtcNow, _tokenExpiryTime, thresholdTime);
            }

            return await Task.FromResult(isExpiringSoon);
        }

        /// <summary>
        /// 检查Token是否有效
        /// </summary>
        public async Task<bool> IsTokenValidAsync()
        {
            if (string.IsNullOrEmpty(_accessToken))
            {
                return false;
            }

            // 检查是否过期
            if (_tokenExpiryTime != DateTime.MinValue && _tokenExpiryTime <= DateTime.UtcNow)
            {
                _logger.LogDebug("Token已过期");
                return false;
            }

            // 可以在这里添加更多的Token验证逻辑
            // 例如：验证Token格式、签名等

            return await Task.FromResult(true);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _refreshSemaphore?.Dispose();
        }
    }
}
