# Gray.Core.HttpClient

一个完全配置驱动的Refit HTTP客户端模块，遵循Gray.Core架构模式，提供与Gray.WebApi和Gray.Core.Gateway的可靠通信。

## 特性

- ✅ **配置驱动**：通过JSON配置文件完全控制HTTP客户端行为
- ✅ **Refit集成**：声明式API客户端，类型安全
- ✅ **认证支持**：JWT Token自动管理和刷新
- ✅ **重试机制**：基于Polly的智能重试和熔断
- ✅ **日志记录**：详细的请求/响应日志
- ✅ **健康检查**：客户端健康状态监控
- ✅ **多端点支持**：同时管理多个API端点

## 快速开始

### 1. 添加服务注册

在`Program.cs`或`Startup.cs`中注册服务：

```csharp
using Gray.Core.HttpClient.Extensions;

// 注册HTTP客户端服务
builder.Services.AddHttpClientServices(builder.Configuration);
```

### 2. 配置文件

将`gray.core.httpclient.json`配置文件放在项目的Config目录下：

```json
{
  "HttpClient": {
    "Enabled": true,
    "DefaultTimeout": 30,
    "Authentication": {
      "Enabled": true,
      "Type": "JWT",
      "AutoRefreshToken": true,
      "LoginEndpoint": "/api/Login/GetJwtToken3"
    },
    "ApiEndpoints": {
      "WebApi": {
        "Name": "Gray.WebApi",
        "BaseUrl": "https://localhost:7000",
        "Timeout": 30
      }
    }
  }
}
```

### 3. 使用API客户端

```csharp
// 注入API客户端
public class LoginService
{
    private readonly IUserApiClient _userApiClient;
    private readonly ITokenManager _tokenManager;

    public LoginService(IUserApiClient userApiClient, ITokenManager tokenManager)
    {
        _userApiClient = userApiClient;
        _tokenManager = tokenManager;
    }

    public async Task<bool> LoginAsync(string username, string password)
    {
        var loginInfo = new LoginWithoutCodeInfo
        {
            UserName = username,
            Password = password
        };

        var response = await _userApiClient.LoginWithoutCodeAsync(loginInfo);
        
        if (response.IsSuccessStatusCode && response.Content?.Status == true)
        {
            // 保存Token
            await _tokenManager.SetTokenAsync(
                response.Content.Token, 
                response.Content.TokenInfo?.TokenValue,
                (int?)response.Content.TokenInfo?.TokenTimeout
            );
            
            return true;
        }

        return false;
    }
}
```

## 配置选项

### 基础配置

- `Enabled`: 是否启用HTTP客户端
- `DefaultTimeout`: 默认超时时间（秒）
- `EnableLogging`: 是否启用日志记录

### 认证配置

- `Authentication.Enabled`: 是否启用认证
- `Authentication.Type`: 认证类型（JWT）
- `Authentication.AutoRefreshToken`: 是否自动刷新Token
- `Authentication.TokenRefreshThresholdMinutes`: Token刷新阈值（分钟）

### API端点配置

每个API端点可以独立配置：

- `BaseUrl`: API基础地址
- `Timeout`: 超时时间
- `EnableRetry`: 是否启用重试
- `MaxRetryAttempts`: 最大重试次数
- `Headers`: 默认请求头

### Polly策略配置

- `Retry`: 重试策略配置
- `CircuitBreaker`: 熔断器策略配置
- `Timeout`: 超时策略配置

## 可用的API客户端

### IUserApiClient
用于与Gray.WebApi用户相关API通信：
- 用户登录/认证
- 用户信息管理
- 用户数据CRUD操作

### IGatewayApiClient
用于与Gray.Core.Gateway通信：
- 网关健康检查
- 路由信息获取
- 服务发现

## 高级功能

### 健康检查

```csharp
public class HealthCheckService
{
    private readonly IHttpClientManager _clientManager;

    public async Task<bool> CheckApiHealthAsync()
    {
        var health = await _clientManager.GetClientHealthAsync("WebApi");
        return health.IsHealthy;
    }
}
```

### 自定义客户端

```csharp
public class CustomService
{
    private readonly IHttpClientManager _clientManager;

    public async Task CallCustomApiAsync()
    {
        var httpClient = _clientManager.GetHttpClient("WebApi");
        var response = await httpClient.GetAsync("/api/custom");
        // 处理响应...
    }
}
```

## 错误处理

模块提供了完善的错误处理机制：

- **自动重试**：网络错误和临时故障自动重试
- **熔断保护**：防止级联故障
- **认证处理**：401错误自动刷新Token并重试
- **详细日志**：记录所有请求/响应详情用于调试

## 最佳实践

1. **配置管理**：将敏感配置（如API密钥）放在安全的配置源中
2. **错误处理**：始终检查API响应的状态码和内容
3. **性能优化**：合理配置超时和重试参数
4. **监控**：利用健康检查功能监控API可用性
5. **日志**：在生产环境中适当调整日志级别

## 故障排除

### 常见问题

1. **编译错误**：确保所有必需的NuGet包已正确安装
2. **配置错误**：检查JSON配置文件格式和路径
3. **网络问题**：验证API端点地址和网络连接
4. **认证失败**：检查JWT Token配置和有效性

### 调试技巧

1. 启用详细日志记录
2. 使用健康检查验证连接
3. 检查Polly策略配置
4. 验证API端点响应格式
