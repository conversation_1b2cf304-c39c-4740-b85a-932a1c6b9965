using Gray.Core.DBManager;
using Gray.Core.EFDbContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Gray.Core.Examples
{
    /// <summary>
    /// 示例Startup类，展示如何在应用程序启动时注册实体加载功能
    /// </summary>
    public class StartupExample
    {
        public IConfiguration Configuration { get; }
        
        public StartupExample(IConfiguration configuration)
        {
            Configuration = configuration;
        }
        
        /// <summary>
        /// 配置服务
        /// </summary>
        public void ConfigureServices(IServiceCollection services)
        {
            // 1. 配置数据库上下文
            ConfigureDbContexts(services);
            
            // 2. 配置实体加载
            EntityLoadingExample.ConfigureEntityLoading(services);
            
            // 其他服务配置...
        }
        
        /// <summary>
        /// 配置数据库上下文
        /// </summary>
        private void ConfigureDbContexts(IServiceCollection services)
        {
            // 注册示例数据库上下文
            services.AddDbContext<SampleDbContext>(options =>
            {
                // 使用配置中的连接字符串
                var connectionString = Configuration.GetConnectionString("SampleConnection");
                options.UseSqlServer(connectionString);
            });
            
            // 注册其他数据库上下文...
        }
        
        /// <summary>
        /// 应用程序启动后的配置
        /// </summary>
        public void Configure(IServiceProvider serviceProvider)
        {
            // 在应用程序启动后，可以手动触发重新加载实体类型
            // 例如，在插件加载后
            ReloadEntitiesAfterPluginLoading(serviceProvider);
            
            // 其他配置...
        }
        
        /// <summary>
        /// 在插件加载后重新加载实体类型
        /// </summary>
        private void ReloadEntitiesAfterPluginLoading(IServiceProvider serviceProvider)
        {
            try
            {
                Console.WriteLine("插件加载完成，开始重新加载实体类型...");
                
                // 1. 重新加载所有实体类型
                EntityTypeCache.ReloadEntityTypes();
                
                // 2. 加载特定插件程序集中的实体
                var pluginAssemblyNames = new[] { "Plugin1.Entity", "Plugin2.Entity" };
                foreach (var assemblyName in pluginAssemblyNames)
                {
                    try
                    {
                        // 加载基本实体
                        EntityTypeCache.LoadTypesFromAssembly(assemblyName);
                        
                        // 加载额外的实体（例如实现了IEntity接口的实体）
                        EntityTypeCache.LoadCustomTypesFromAssembly(
                            assemblyName,
                            type => type.IsClass && 
                                   !type.IsAbstract && 
                                   typeof(IEntity).IsAssignableFrom(type)
                        );
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"加载插件程序集 {assemblyName} 失败: {ex.Message}");
                    }
                }
                
                Console.WriteLine("实体类型重新加载完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"重新加载实体类型失败: {ex.Message}");
            }
        }
    }
} 