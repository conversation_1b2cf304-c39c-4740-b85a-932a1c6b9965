using Gray.Core.UnitOfWorkMange;
using Gray.Core.EFDbContext;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Data;
using System.Threading;
using System.Threading.Tasks;

namespace Gray.Core.Transaction
{
    /// <summary>
    /// 异步事务上下文接口
    /// </summary>
    public interface IAsyncTransactionContext
    {
        /// <summary>
        /// 当前事务
        /// </summary>
        IDbContextTransaction CurrentTransaction { get; }
        
        /// <summary>
        /// 是否有活动事务
        /// </summary>
        bool HasActiveTransaction { get; }
        
        /// <summary>
        /// 事务嵌套级别
        /// </summary>
        int TransactionLevel { get; }
        
        /// <summary>
        /// 在事务中执行异步操作（完全解耦模式，支持指定 DbContext）
        /// </summary>
        /// <typeparam name="TDbContext">DbContext 类型</typeparam>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">异步操作，接收 IUnitOfWork 作为参数</param>
        /// <param name="isolationLevel">隔离级别</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        Task<T> ExecuteInTransactionAsync<TDbContext, T>(
            Func<IUnitOfWork, Task<T>> operation,
            IsolationLevel isolationLevel = IsolationLevel.ReadUncommitted,
            CancellationToken cancellationToken = default)
            where TDbContext : BaseDbContext<TDbContext>;

        /// <summary>
        /// 在事务中执行异步操作（无返回值，完全解耦模式，支持指定 DbContext）
        /// </summary>
        /// <typeparam name="TDbContext">DbContext 类型</typeparam>
        /// <param name="operation">异步操作，接收 IUnitOfWork 作为参数</param>
        /// <param name="isolationLevel">隔离级别</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task ExecuteInTransactionAsync<TDbContext>(
            Func<IUnitOfWork, Task> operation,
            IsolationLevel isolationLevel = IsolationLevel.ReadUncommitted,
            CancellationToken cancellationToken = default)
            where TDbContext : BaseDbContext<TDbContext>;

        /// <summary>
        /// 在事务中执行异步操作（使用默认 SysDbContext，向后兼容）
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">异步操作，接收 IUnitOfWork 作为参数</param>
        /// <param name="isolationLevel">隔离级别</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        Task<T> ExecuteInTransactionAsync<T>(
            Func<IUnitOfWork, Task<T>> operation,
            IsolationLevel isolationLevel = IsolationLevel.ReadUncommitted,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 在事务中执行异步操作（无返回值，使用默认 SysDbContext，向后兼容）
        /// </summary>
        /// <param name="operation">异步操作，接收 IUnitOfWork 作为参数</param>
        /// <param name="isolationLevel">隔离级别</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task ExecuteInTransactionAsync(
            Func<IUnitOfWork, Task> operation,
            IsolationLevel isolationLevel = IsolationLevel.ReadUncommitted,
            CancellationToken cancellationToken = default);
    }
}
