using Gray.Core.UnitOfWorkMange;
using Microsoft.Extensions.Logging;
using System;
using System.Data;
using System.Threading.Tasks;

namespace Gray.Core.Transaction
{
    /// <summary>
    /// 事务装饰器基类
    /// </summary>
    /// <typeparam name="TService">服务接口类型</typeparam>
    public abstract class TransactionDecorator<TService> where TService : class
    {
        protected readonly TService _decoratedService;
        protected readonly IAsyncTransactionContext _asyncTransactionContext;
        protected readonly ILogger _logger;

        protected TransactionDecorator(TService decoratedService, IAsyncTransactionContext asyncTransactionContext, ILogger logger)
        {
            _decoratedService = decoratedService ?? throw new ArgumentNullException(nameof(decoratedService));
            _asyncTransactionContext = asyncTransactionContext ?? throw new ArgumentNullException(nameof(asyncTransactionContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 在事务中执行操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">操作</param>
        /// <param name="isolationLevel">隔离级别</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <returns>操作结果</returns>
        protected T ExecuteInTransaction<T>(Func<T> operation, IsolationLevel? isolationLevel = null, string operationName = null)
        {
            var name = operationName ?? "Unknown Operation";
            _logger.LogDebug($"开始事务操作: {name}");

            try
            {
                // 注意：AsyncTransactionContext 只支持异步操作，这里使用 GetAwaiter().GetResult()
                // 建议将同步方法改为异步方法
                return _asyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
                {
                    return operation();
                }, isolationLevel ?? IsolationLevel.ReadCommitted).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"事务操作失败: {name}");
                throw;
            }
        }

        /// <summary>
        /// 在事务中执行异步操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">异步操作</param>
        /// <param name="isolationLevel">隔离级别</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <returns>操作结果</returns>
        protected async Task<T> ExecuteInTransactionAsync<T>(Func<IUnitOfWork, Task<T>> operation, IsolationLevel? isolationLevel = null, string operationName = null)
        {
            var name = operationName ?? "Unknown Async Operation";
            _logger.LogDebug($"开始异步事务操作: {name}");

            try
            {
                return await _asyncTransactionContext.ExecuteInTransactionAsync(operation, isolationLevel ?? IsolationLevel.ReadCommitted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"异步事务操作失败: {name}");
                throw;
            }
        }

        /// <summary>
        /// 在事务中执行操作（无返回值）
        /// </summary>
        /// <param name="operation">操作</param>
        /// <param name="isolationLevel">隔离级别</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        protected void ExecuteInTransaction(Action operation, IsolationLevel? isolationLevel = null, string operationName = null)
        {
            var name = operationName ?? "Unknown Void Operation";
            _logger.LogDebug($"开始事务操作: {name}");

            try
            {
                _asyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
                {
                    operation();
                }, isolationLevel ?? IsolationLevel.ReadCommitted).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"事务操作失败: {name}");
                throw;
            }
        }

        /// <summary>
        /// 在事务中执行异步操作（无返回值）
        /// </summary>
        /// <param name="operation">异步操作</param>
        /// <param name="isolationLevel">隔离级别</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        protected async Task ExecuteInTransactionAsync(Func<Task> operation, IsolationLevel? isolationLevel = null, string operationName = null)
        {
            var name = operationName ?? "Unknown Async Void Operation";
            _logger.LogDebug($"开始异步事务操作: {name}");

            try
            {
                await _asyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
                {
                    await operation();
                }, isolationLevel ?? IsolationLevel.ReadCommitted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"异步事务操作失败: {name}");
                throw;
            }
        }

        /// <summary>
        /// 使用事务范围执行操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">操作</param>
        /// <param name="autoRollback">是否自动回滚</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <returns>操作结果</returns>
        protected T ExecuteWithTransactionScope<T>(Func<T> operation, bool autoRollback = true, string operationName = null)
        {
            var name = operationName ?? "Unknown Scope Operation";
            _logger.LogDebug($"开始事务范围操作: {name}");

            try
            {
                // 注意：AsyncTransactionContext 不支持 TransactionScope，使用 ExecuteInTransactionAsync 替代
                return _asyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
                {
                    return operation();
                }).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"事务范围操作失败: {name}");
                throw;
            }
        }

        /// <summary>
        /// 使用事务范围执行异步操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">异步操作</param>
        /// <param name="autoRollback">是否自动回滚</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <returns>操作结果</returns>
        protected async Task<T> ExecuteWithTransactionScopeAsync<T>(Func<Task<T>> operation, bool autoRollback = true, string operationName = null)
        {
            var name = operationName ?? "Unknown Async Scope Operation";
            _logger.LogDebug($"开始异步事务范围操作: {name}");

            try
            {
                // 注意：AsyncTransactionContext 不支持 TransactionScope，使用 ExecuteInTransactionAsync 替代
                return await _asyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
                {
                    return await operation();
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"异步事务范围操作失败: {name}");
                throw;
            }
        }
    }

    /// <summary>
    /// 事务装饰器工厂
    /// </summary>
    public static class TransactionDecoratorFactory
    {
        /// <summary>
        /// 创建事务装饰器
        /// </summary>
        /// <typeparam name="TService">服务类型</typeparam>
        /// <typeparam name="TDecorator">装饰器类型</typeparam>
        /// <param name="service">原始服务</param>
        /// <param name="asyncTransactionContext">异步事务上下文</param>
        /// <param name="logger">日志器</param>
        /// <returns>装饰后的服务</returns>
        public static TDecorator Create<TService, TDecorator>(TService service, IAsyncTransactionContext asyncTransactionContext, ILogger logger)
            where TService : class
            where TDecorator : TransactionDecorator<TService>
        {
            return (TDecorator)Activator.CreateInstance(typeof(TDecorator), service, asyncTransactionContext, logger);
        }
    }
}
