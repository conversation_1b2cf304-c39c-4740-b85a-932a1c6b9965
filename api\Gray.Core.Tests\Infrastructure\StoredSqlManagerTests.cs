using Xunit;
using Gray.Core.Infrastructure;
using Gray.Entity.DomainModels;

namespace Gray.Core.Tests.Infrastructure
{
    public class StoredSqlManagerTests
    {
        [Fact]
        public void ValidateParameters_WithValidParameters_ReturnsTrue()
        {
            // Arrange
            var sqlName = "TestSql";
            var parameters = new { Id = 1, Name = "Test" };

            // Act 
            var result = StoredSqlManager.ValidateParameters(sqlName, parameters);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void ExecuteStoredSql_WithInvalidSql_ThrowsException()
        {
            // Arrange
            var sqlName = "NonExistentSql";

            // Act & Assert
            Assert.Throws<StoredSqlException>(() => 
                StoredSqlManager.ExecuteStoredSql(sqlName));
        }
    }
}
