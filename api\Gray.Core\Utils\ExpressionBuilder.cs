
using System;
using System.Linq;
using System.Linq.Expressions;

namespace Gray.Core.Utilities
{
    public static class ExpressionBuilder
    {
        public static Expression<Func<T, bool>> CreateEqual<T>(string propertyName, string value) 
        {
            try
            {
                var parameter = Expression.Parameter(typeof(T), "x");
                var property = Expression.Property(parameter, propertyName);
                var constant = Expression.Constant(value); 
                var equal = Expression.Equal(property, constant);
                return Expression.Lambda<Func<T, bool>>(equal, parameter);
            }
            catch (Exception ex)
            {
                throw new Exception($"创建相等表达式失败: {ex.Message}");
            }
        }

        public static Expression<Func<T, bool>> CreateFilter<T>(FieldFilter filter)
        {
            try
            {
                // 获取参数
                var parameter = Expression.Parameter(typeof(T), "x");
                var property = Expression.Property(parameter, filter.Field);
                var constant = Expression.Constant(filter.Value);

                // 根据比较类型创建表达式
                Expression condition = null;
                switch (filter.Type)
                {
                    case "=":
                        condition = Expression.Equal(property, constant);
                        break;
                    case ">":
                        condition = Expression.GreaterThan(property, constant);
                        break;
                    case ">=":
                        condition = Expression.GreaterThanOrEqual(property, constant); 
                        break;
                    case "<":
                        condition = Expression.LessThan(property, constant);
                        break;
                    case "<=":
                        condition = Expression.LessThanOrEqual(property, constant);
                        break;
                    case "!=":
                        condition = Expression.NotEqual(property, constant);
                        break;
                    case "contains":
                        var contains = typeof(string).GetMethod("Contains", new[] { typeof(string) });
                        condition = Expression.Call(property, contains, constant);
                        break;
                    case "notcontains":
                        var notContains = typeof(string).GetMethod("Contains", new[] { typeof(string) });
                        condition = Expression.Not(Expression.Call(property, notContains, constant));
                        break;
                    default:
                        throw new NotSupportedException($"不支持的比较类型: {filter.Type}");
                }

                return Expression.Lambda<Func<T, bool>>(condition, parameter);
            }
            catch (Exception ex)
            {
                throw new Exception($"创建过滤器表达式失败: {ex.Message}");
            }
        }

        public static Expression<Func<T, bool>> Combine<T>(
            this Expression<Func<T, bool>> expr1,
            Expression<Func<T, bool>> expr2)
        {
            if (expr1 == null)
                return expr2;
            if (expr2 == null)
                return expr1;

            var parameter = Expression.Parameter(typeof(T), "x");
            var combined = Expression.AndAlso(
                Expression.Invoke(expr1, parameter),
                Expression.Invoke(expr2, parameter)
            );

            return Expression.Lambda<Func<T, bool>>(combined, parameter);
        }
    }

    public class FieldFilter
    {
        public string Field { get; set; }
        public string Type { get; set; }
        public object Value { get; set; }
    }
}
