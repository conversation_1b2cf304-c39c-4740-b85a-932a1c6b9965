/*
Tel:15662337743
 *所有业务编写全部应在Partial文件夹下TestDbService与ITestDbService中编写
 */
using Gray.DbTest.IRepositories;
using Gray.DbTest.IServices;
using Gray.Core.BaseProvider;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.DomainModels;

namespace Gray.DbTest.Services
{
    public partial class TestDbService : ServiceBase<TestDb, ITestDbRepository>
    , ITestDbService, IDependency
    {
    public TestDbService(ITestDbRepository repository)
    : base(repository)
    {
    Init(repository);
    }
    public static ITestDbService Instance
    {
      get { return AutofacContainerModule.GetService<ITestDbService>(); } }
    }
 }
