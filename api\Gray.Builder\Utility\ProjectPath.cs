﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Gray.Core.Extensions;

namespace Gray.Builder.Utility
{
    public class ProjectPath
    {
        //   private int findCount = 1;

        /// <summary>
        /// 获取web父目录所在位置
        /// </summary>
        /// <returns></returns>
        public static DirectoryInfo GetProjectDirectoryInfo()
        {
            return GetProjectDirectoryInfo(new DirectoryInfo("".MapPath()), 1);
        }
        public static string GetProjectPath(string projectName)
        {
            if (string.IsNullOrEmpty(projectName))
                throw new ArgumentNullException(nameof(projectName));

            // 获取基础目录
            var baseDir = AppDomain.CurrentDomain.BaseDirectory;

            // 向上查找直到找到解决方案文件
            var directory = new DirectoryInfo(baseDir);
            var solutionDirectory = FindSolutionDirectory(directory);

            if (solutionDirectory == null)
                return string.Empty;

            // 递归搜索所有目录查找项目文件
            var projectFile = FindProjectFile(solutionDirectory, projectName);
            if (projectFile == null)
                return string.Empty;

            // 确认是否为 ASP.NET Core 项目
            try
            {
                var content = File.ReadAllText(projectFile.FullName);
                if (!content.Contains("<TargetFramework>net") && !content.Contains("<TargetFrameworks>net"))
                    return string.Empty;

                // 返回项目文件所在目录的完整路径
               var dp =    new DirectoryInfo( Path.GetDirectoryName(projectFile.FullName)).Parent;
                return dp.FullName;
            }
            catch
            {
                return string.Empty;
            }
        }

        private static DirectoryInfo FindSolutionDirectory(DirectoryInfo startDir)
        {
            while (startDir != null)
            {
                // 在当前目录及所有子目录中查找解决方案文件
                var solutionFiles = startDir.GetFiles("*.sln", SearchOption.AllDirectories);
                if (solutionFiles.Any())
                {
                    // 找到解决方案文件后，返回包含该文件的目录
                    return solutionFiles.First().Directory;
                }
                startDir = startDir.Parent;
            }
            return null;
        }

        private static FileInfo FindProjectFile(DirectoryInfo rootDir, string projectName)
        {
            // 规范化项目名称
            if (!projectName.EndsWith(".csproj"))
                projectName = $"{projectName}.csproj";

            var projectFiles = rootDir.GetFiles(projectName, SearchOption.AllDirectories);

            // 如果找到多个同名项目，返回第一个
            return projectFiles.FirstOrDefault();
        }
        //public static string GetProjectFileName(string startsWith)
        //{
        //    var directories = GetProjectDirectoryInfo()?.GetDirectories()
        //        .Where(c =>
        //            c.Name != startsWith + ".Core"
        //            && c.Name != startsWith + ".Entity"
        //            && !c.Name.ToLower().EndsWith(".web")
        //              && !c.Name.ToLower().EndsWith(".idea")
        //            && !c.Name.ToLower().EndsWith(".webapi")
        //            && !c.Name.ToLower().EndsWith(".builder")
        //            && c.Name.ToLower() != ".vs"
        //        );

        //    if (directories != null && directories.Any())
        //    {
        //        var result = new List<string>();
        //        foreach (var dir in directories)
        //        {
        //            // Add main directory
        //            result.Add(dir.Name);

        //            // Get subdirectories
        //            var subDirs = dir.GetDirectories()
        //                .Where(sd =>
        //                    !sd.Name.ToLower().EndsWith(".web")
        //                    && !sd.Name.ToLower().EndsWith(".webapi")
        //                    && !sd.Name.ToLower().EndsWith(".builder")
        //                       && !sd.Name.ToLower().EndsWith("bin")
        //                        && !sd.Name.ToLower().EndsWith("obj")
        //                             && !sd.Name.ToLower().EndsWith("Bin")
        //                                 && !sd.Name.ToLower().EndsWith("Obj")
        //                    && sd.Name.ToLower() != ".vs"
        //                );

        //            // Add main directory + subdirectory paths
        //            result.AddRange(subDirs.Select(sd =>
        //                Path.Combine(dir.Name, sd.Name)));
        //        }

        //        return result.Serialize();
        //    }

        //    // Fallback to original file search if no directories found
        //    var fileNames = new DirectoryInfo("".MapPath()).GetFiles()
        //        .Where(x => x.Name.EndsWith(".dll")
        //            && !x.Name.EndsWith(".Core.dll")
        //            && !x.Name.EndsWith(".Entity.dll")
        //            && !x.Name.EndsWith(".Builder.dll")
        //            && !x.Name.ToLower().EndsWith(".web")
        //            && !x.Name.ToLower().EndsWith(".webapi")
        //            && !x.Name.ToLower().EndsWith(".builder")
        //        )
        //        .Select(x => x.Name.Replace(".dll", ""))
        //        .ToList()
        //        .Serialize();

        //    return fileNames ?? "''";
        //}

        public static string GetProjectFileName(string startsWith)
        {
            // 获取基础目录
            var baseDir = AppDomain.CurrentDomain.BaseDirectory;

            // 向上查找直到找到解决方案文件
            var directory = new DirectoryInfo(baseDir);
            while (directory != null && !directory.GetFiles("*.sln").Any())
            {
                directory = directory.Parent;
            }

            if (directory == null)
                return "''";

            // 获取所有项目文件
            var projectFiles = directory.GetFiles("*.csproj", SearchOption.AllDirectories);

            var projects = projectFiles
                .Select(file => new
                {
                    Name = Path.GetFileNameWithoutExtension(file.Name),
                    Content = File.ReadAllText(file.FullName)
                })
                .Where(p =>
                    (p.Content.Contains("<TargetFramework>net") || p.Content.Contains("<TargetFrameworks>net"))
                    && p.Name != startsWith + ".Core"
                    && p.Name != startsWith + ".Entity"
                    && !p.Name.ToLower().EndsWith(".web")
                    && !p.Name.ToLower().EndsWith(".webapi")
                    && !p.Name.ToLower().EndsWith(".builder")
                    && p.Name.ToLower() != ".vs"
                )
                .Select(p => p.Name)
                .ToList();

            // 如果没有找到项目，尝试从dll文件中查找
            if (!projects.Any())
            {
                var dllFiles = new DirectoryInfo("".MapPath()).GetFiles()
                    .Where(x => x.Name.EndsWith(".dll")
                        && !x.Name.EndsWith(".Core.dll")
                        && !x.Name.EndsWith(".Entity.dll")
                        && !x.Name.EndsWith(".Builder.dll")
                        && !x.Name.ToLower().EndsWith(".web")
                        && !x.Name.ToLower().EndsWith(".webapi")
                        && !x.Name.ToLower().EndsWith(".builder")
                    )
                    .Select(x => x.Name.Replace(".dll", ""))
                    .ToList();

                return dllFiles.Any() ? dllFiles.Serialize() : "''";
            }

            return projects.Serialize();
        }
        //public static string GetProjectFileName(string startsWith)
        //{
        //    string fileNames = GetProjectDirectoryInfo()?.GetDirectories()
        //             .Where(
        //        c =>
        //             //c.Name.StartsWith(startsWith)&& 
        //             c.Name != startsWith + ".Core"
        //             && c.Name != startsWith + ".Entity"
        //             && !c.Name.ToLower().EndsWith(".web")
        //             && !c.Name.ToLower().EndsWith(".webapi")
        //             && !c.Name.ToLower().EndsWith(".builder")
        //              && c.Name.ToLower() != ".vs"
        //             ).Select(x => x.Name).ToList().Serialize();
        //    if (string.IsNullOrEmpty(fileNames))
        //    {
        //        fileNames = new DirectoryInfo("".MapPath()).GetFiles().Where(x => x.Name.EndsWith(".dll")
        //          //&& x.Name.StartsWith(startsWith)
        //          && !x.Name.EndsWith(".Core.dll")
        //          && !x.Name.EndsWith(".Entity.dll")
        //          && !x.Name.EndsWith(".Builder.dll")
        //          && !x.Name.ToLower().EndsWith(".web")
        //          && !x.Name.ToLower().EndsWith(".webapi")
        //          && !x.Name.ToLower().EndsWith(".builder")
        //          ).Select(x => x.Name.Replace(".dll", "")).ToList().Serialize();
        //    }
        //    return fileNames ?? "''";
        //}
        /// <summary>
        /// 获取指定结尾的项目名称 
        /// </summary>
        /// <param name="lastIndexOfName"></param>
        /// <returns></returns>
        public static string GetLastIndexOfDirectoryName(string lastIndexOfName)
        {
            string projectName = GetProjectDirectoryInfo()?.GetDirectories()
                     .Where(c => c.Name.LastIndexOf(lastIndexOfName) != -1).Select(x => x.Name).FirstOrDefault();
            if (string.IsNullOrEmpty(projectName))
            {
                projectName = new DirectoryInfo("".MapPath()).GetFiles().Where(x => x.Name.LastIndexOf(lastIndexOfName + ".dll") != -1).FirstOrDefault().Name;
                if (!string.IsNullOrEmpty(projectName))
                {
                    projectName = projectName.Replace(".dll", "");
                }
            }
            return projectName;
        }
        /// <summary>
        /// 获取项目所在路径
        /// </summary>
        /// <param name="directoryInfo"></param>
        /// <returns></returns>
        private static DirectoryInfo GetProjectDirectoryInfo(DirectoryInfo directoryInfo, int findCount)
        {
            if (directoryInfo == null)
            {
                return null;
            }
            if (directoryInfo.Exists
                && directoryInfo.GetDirectories().Where(x => x.Name.LastIndexOf(".Web") != -1).FirstOrDefault() != null)
            {
                return directoryInfo;
            }
            if (findCount < 7)
            {
                findCount++;
                DirectoryInfo dir = GetProjectDirectoryInfo(directoryInfo.Parent, findCount);
                if (dir != null)
                {
                    return dir;
                }
            }
            return null;
        }
    }
}
