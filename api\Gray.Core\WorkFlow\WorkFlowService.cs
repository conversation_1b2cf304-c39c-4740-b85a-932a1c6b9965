using Gray.Core.Configuration;
using Gray.Core.EFDbContext;
using Gray.Core.Enums;
using Gray.Core.Extensions;
using Gray.Core.Infrastructure;
using Gray.Core.ManageUser;
using Gray.Core.UnitOfWorkMange;
using Gray.Core.Transaction;
using Gray.Core.Utilities;
using Gray.Entity.DomainModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace Gray.Core.WorkFlow
{
    public class WorkFlowService : IWorkFlowService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAsyncTransactionContext _asyncTransactionContext;
        private readonly ILogger<WorkFlowService> _logger;

        public WorkFlowService(
            IUnitOfWork unitOfWork,
            IAsyncTransactionContext asyncTransactionContext,
            ILogger<WorkFlowService> logger)
        {
            _unitOfWork = unitOfWork;
            _asyncTransactionContext = asyncTransactionContext;
            _logger = logger;
        }

        public bool Exists<T>(string workFlowTableName = null)
        {
            return WorkFlowContainer.Exists<T>(workFlowTableName);
        }

        public bool Exists<T>(T entity, string workFlowTableName = null) where T : class
        {
            return WorkFlowContainer.Exists<T>(workFlowTableName) && 
                   GetAuditFlowTable<T>(typeof(T).GetKeyProperty().GetValue(entity).ToString(), workFlowTableName) != null;
        }

        public bool Exists(string table)
        {
            return WorkFlowContainer.Exists(table);
        }

        public async Task<int> GetAuditStatus<T>(string value, string workFlowTableName)
        {
            var flowTable = await GetAuditFlowTable<T>(value, workFlowTableName);
            return flowTable?.AuditStatus ?? 0;
        }

        public async Task<Sys_WorkFlowTable> GetAuditFlowTable<T>(string workTableKey, string workFlowTableName = null)
        {
            var dbContext = _unitOfWork.GetDbContext<SysDbContext>();
            return await dbContext.Set<Sys_WorkFlowTable>()
                .Where(x => x.WorkTable == (workFlowTableName ?? typeof(T).GetEntityTableName(false)) && 
                           x.WorkTableKey == workTableKey)
                .FirstOrDefaultAsync();
        }

        public async Task<WebResponseContent> AddProceseAsync<T>(T entity, bool rewrite = false, bool changeTableStatus = true,
            Action<T, List<int>> addWorkFlowExecuted = null, bool checkId = false,
            string workFlowTableName = null) where T : class
        {
            WebResponseContent webResponseContent = new WebResponseContent();   
            return await ExecuteInTransactionAsync(async () =>
            {
                try
                {
                    var options = WorkFlowContainer.GetFlowOptions(entity, workFlowTableName);
                    if (options == null || options.FilterList.Count == 0)
                    {
                        return new WebResponseContent(true);
                    }

                    // 获取审批属性
                    var auditProperty = typeof(T).GetProperties()
                        .FirstOrDefault(x => x.Name.ToLower() == "auditstatus");

                    if (auditProperty == null)
                    {
                        _logger.LogError("表缺少审核状态字段：AuditStatus");
                        return  webResponseContent.Error( "表缺少审核状态字段：AuditStatus");
                    }

                    var userInfo = UserContext.Current.UserInfo;
                    string workTable = workFlowTableName ?? typeof(T).GetEntityTableName(false);
                    options.WorkTableName = WorkFlowContainer.GetName<T>(workFlowTableName);

                    // 重新生成流程的情况
                    if (rewrite)
                    {
                        await RewriteAsync(entity, options, changeTableStatus);
                        return new WebResponseContent(true);
                    }

                    // 设置审核状态
                    int auditStatus = (int)options.DefaultAuditStatus;
                    string tableKey = typeof(T).GetKeyProperty().GetValue(entity).ToString();

                    // 处理草稿提交
                    if (checkId)
                    {
                        var dbContext = _unitOfWork.GetDbContext<SysDbContext>();
                        var existingFlows = await dbContext.Set<Sys_WorkFlowTable>()
                            .Where(x => x.WorkTable == workTable && x.WorkTableKey == tableKey)
                            .Include(c => c.Sys_WorkFlowTableStep)
                            .AsNoTracking()
                            .ToListAsync();

                        if (existingFlows.Any())
                        {
                            dbContext.Set<Sys_WorkFlowTable>().RemoveRange(existingFlows);
                            await dbContext.SaveChangesAsync();
                        }
                        auditStatus = (int)AuditStatus.待审核;
                    }

                    // 设置实体的审核状态
                    auditProperty.SetValue(entity, auditStatus);

                    // 创建工作流表记录
                    Guid workFlowTable_Id = Guid.NewGuid();
                    var workFlowTable = new Sys_WorkFlowTable()
                    {
                        WorkFlowTable_Id = workFlowTable_Id,
                        AuditStatus = auditStatus,
                        Enable = 1,
                        WorkFlow_Id = options.WorkFlow_Id,
                        WorkName = options.WorkName,
                        WorkTable = workTable,
                        WorkTableKey = tableKey,
                        WorkTableName = options.WorkTableName,
                        DbServiceId = options.DbServiceId,
                        CreateID = userInfo.User_Id,
                        CreateDate = DateTime.Now,
                        Creator = userInfo.UserTrueName
                    };

                    // 生成工作流步骤
                    var steps = await GenerateWorkFlowSteps(entity, options, workFlowTable_Id, userInfo);
                    
                    if (steps.Count == 0)
                    {
                        return new WebResponseContent(true);
                    }

                    // 处理特殊审批类型
                    await HandleSpecialAuditTypes(steps, entity);

                    // 设置第一个审核节点
                    var nodeInfo = steps.Where(x => x.ParentId == steps[0].StepId)
                        .Select(s => new { s.StepId, s.StepName, s.StepType, s.StepValue })
                        .FirstOrDefault();

                    workFlowTable.CurrentStepId = nodeInfo?.StepId;
                    workFlowTable.StepName = nodeInfo?.StepName;
                    workFlowTable.Sys_WorkFlowTableStep = steps;

                    // 保存实体变更
                    var entityContext = _unitOfWork.GetDbContext<SysDbContext>();
                    entityContext.Entry(entity).Property(auditProperty.Name).IsModified = true;
                    await entityContext.SaveChangesAsync();

                    // 写入审核日志
                    if (options.DefaultAuditStatus != AuditStatus.草稿 
                        && options.DefaultAuditStatus != AuditStatus.待提交)
                    {
                        var log = new Sys_WorkFlowTableAuditLog()
                        {
                            Id = Guid.NewGuid(),
                            WorkFlowTable_Id = workFlowTable.WorkFlowTable_Id,
                            CreateDate = DateTime.Now,
                            AuditStatus = (int)AuditStatus.待审核,
                            Remark = $"[{userInfo.UserTrueName}]提交了数据"
                        };

                        var dbContext = _unitOfWork.GetDbContext<SysDbContext>();
                        await dbContext.Set<Sys_WorkFlowTable>().AddAsync(workFlowTable);
                        await dbContext.Set<Sys_WorkFlowTableAuditLog>().AddAsync(log);
                        await dbContext.SaveChangesAsync();
                    }

                    // 执行回调
                    if (addWorkFlowExecuted != null)
                    {
                        var userIds = await GetAuditUserIdsAsync(nodeInfo?.StepType ?? 0, nodeInfo?.StepValue);
                        addWorkFlowExecuted.Invoke(entity, userIds);
                    }

                    return new WebResponseContent(true);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "添加流程失败");
                    return  webResponseContent.Error( $"添加流程失败:{ex.Message}");
                }
            }, "添加工作流程");
        }

        // 新增的私有辅助方法
        private async Task RewriteAsync<T>(T entity, WorkFlowTableOptions workFlow, bool changeTableStatus) where T : class
        {
            try 
            {
                var auditProperty = typeof(T).GetProperties()
                    .FirstOrDefault(x => x.Name.ToLower() == "auditstatus");
                if (auditProperty == null)
                {
                    return;
                }

                string value = typeof(T).GetKeyProperty().GetValue(entity).ToString();
                var dbContext = _unitOfWork.GetDbContext<SysDbContext>();

                // 查询工作流表及其步骤
                var workTable = await dbContext.Set<Sys_WorkFlowTable>()
                    .Where(x => x.WorkTableKey == value && x.WorkFlow_Id == workFlow.WorkFlow_Id)
                    .AsNoTracking()
                    .Include(x => x.Sys_WorkFlowTableStep)
                    .FirstOrDefaultAsync();

                if (workTable == null || workFlow.Sys_WorkFlowStep == null || workFlow.Sys_WorkFlowStep.Count == 0)
                {
                    _logger.LogWarning($"未查到流程数据，id：{workFlow.WorkFlow_Id}");
                    return;
                }

                // 重新设置第一个节点
                string startStepId = workTable.Sys_WorkFlowTableStep
                    .Where(x => x.StepAttrType == StepType.start.ToString())
                    .Select(s => s.StepId)
                    .FirstOrDefault();

                // 设置当前步骤为开始节点的下一个节点
                workTable.CurrentStepId = workTable.Sys_WorkFlowTableStep
                    .Where(x => x.ParentId == startStepId)
                    .Select(s => s.StepId)
                    .FirstOrDefault();

                // 重置审核状态
                workTable.AuditStatus = (int)AuditStatus.待审核;

                // 重置所有步骤的审核信息
                foreach (var step in workTable.Sys_WorkFlowTableStep)
                {
                    step.Enable = 0;
                    step.AuditId = null;
                    step.Auditor = null;
                    step.AuditDate = null;
                    step.Remark = null;
                }

                if (changeTableStatus)
                {
                    // 更新实体的审核状态
                    dbContext.Entry(entity).State = EntityState.Detached;
                    auditProperty.SetValue(entity, 0);
                    var entityEntry = dbContext.Entry(entity);
                    entityEntry.Property(auditProperty.Name).IsModified = true;
                    await dbContext.SaveChangesAsync();
                }

                // 更新工作流表
                dbContext.Entry(workTable).State = EntityState.Detached;
                dbContext.Update(workTable);
                await dbContext.SaveChangesAsync();

                _logger.LogInformation($"重写工作流成功: WorkFlow_Id={workFlow.WorkFlow_Id}, TableKey={value}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"重写工作流失败: WorkFlow_Id={workFlow.WorkFlow_Id}");
                throw;
            }
        }

        private async Task<List<Sys_WorkFlowTableStep>> GenerateWorkFlowSteps<T>(
            T entity,
            WorkFlowTableOptions workFlow,
            Guid workFlowTableId,
            UserInfo userInfo) where T : class
        {
            try
            {
                // 先生成开始节点
                var steps = workFlow.FilterList
                    .Where(x => x.StepAttrType == StepType.start.ToString())
                    .Select(s => new Sys_WorkFlowTableStep()
                    {
                        Sys_WorkFlowTableStep_Id = Guid.NewGuid(),
                        WorkFlowTable_Id = workFlowTableId,
                        WorkFlow_Id = workFlow.WorkFlow_Id,
                        StepId = s.StepId,
                        StepName = s.StepName ?? "流程开始",
                        StepType = s.StepType,
                        StepValue = s.StepValue,
                        StepAttrType = s.StepAttrType,
                        OrderId = 0,
                        Enable = 1,
                        CreateDate = DateTime.Now,
                        Creator = userInfo.UserTrueName,
                        CreateID = userInfo.User_Id
                    }).ToList();

                var entities = new List<T>() { entity };

                // 遍历处理每个节点
                for (int i = 0; i < steps.Count; i++)
                {
                    var currentStep = steps[i];

                    // 查找下一个满足条件的节点数据
                    var nextFilter = workFlow.FilterList
                        .Where(c => c.ParentIds.Contains(currentStep.StepId) 
                                   && c.FieldFilters.CheckFilter(entities, c.Expression))
                        .FirstOrDefault();

                    // 未找到满足条件的找无条件的节点
                    if (nextFilter == null)
                    {
                        nextFilter = workFlow.FilterList
                            .FirstOrDefault(c => c.ParentIds.Contains(currentStep.StepId) 
                                               && c.Expression == null);
                    }

                    if (nextFilter != null)
                    {
                        // 创建下一个节点
                        var nextStep = workFlow.Sys_WorkFlowStep
                            .Where(x => x.StepId == nextFilter.StepId)
                            .Select(s => new Sys_WorkFlowTableStep()
                            {
                                Sys_WorkFlowTableStep_Id = Guid.NewGuid(),
                                WorkFlowTable_Id = workFlowTableId,
                                WorkFlow_Id = s.WorkFlow_Id,
                                StepId = s.StepId,
                                StepName = s.StepName,
                                StepAttrType = s.StepAttrType,
                                NextStepId = null,
                                ParentId = currentStep.StepId,
                                StepType = s.StepType,
                                StepValue = s.StepValue,
                                OrderId = i + 1,
                                Enable = 1,
                                CreateDate = DateTime.Now
                            }).FirstOrDefault();

                        if (nextStep != null)
                        {
                            // 设置当前节点的下一个审核节点
                            currentStep.NextStepId = nextStep.StepId;

                            // 检查节点是否已存在
                            if (!steps.Any(x => x.StepId == nextStep.StepId))
                            {
                                // 处理多人会签情况
                                if (!string.IsNullOrEmpty(nextStep.StepValue) && nextStep.StepValue.Contains(","))
                                {
                                    var stepValues = nextStep.StepValue.Split(",");
                                    foreach (string stepValue in stepValues)
                                    {
                                        steps.Add(new Sys_WorkFlowTableStep()
                                        {
                                            Sys_WorkFlowTableStep_Id = Guid.NewGuid(),
                                            WorkFlowTable_Id = nextStep.WorkFlowTable_Id,
                                            WorkFlow_Id = nextStep.WorkFlow_Id,
                                            StepId = nextStep.StepId,
                                            StepName = nextStep.StepName,
                                            StepAttrType = nextStep.StepAttrType,
                                            NextStepId = null,
                                            ParentId = nextStep.ParentId,
                                            StepType = nextStep.StepType,
                                            StepValue = stepValue,
                                            OrderId = nextStep.OrderId,
                                            Enable = 1,
                                            CreateDate = DateTime.Now
                                        });
                                    }
                                }
                                else
                                {
                                    steps.Add(nextStep);
                                }
                            }
                        }
                    }
                    else
                    {
                        // 找不到满足条件的节点，添加结束节点
                        var endSteps = workFlow.Sys_WorkFlowStep
                            .Where(c => c.StepAttrType == StepType.end.ToString())
                            .ToList();

                        if (endSteps.Any())
                        {
                            currentStep.NextStepId = endSteps[0].StepId;
                            var endStep = endSteps.Select(s => new Sys_WorkFlowTableStep()
                            {
                                Sys_WorkFlowTableStep_Id = Guid.NewGuid(),
                                WorkFlowTable_Id = workFlowTableId,
                                WorkFlow_Id = s.WorkFlow_Id,
                                StepId = s.StepId,
                                StepName = s.StepName,
                                StepAttrType = s.StepAttrType,
                                NextStepId = null,
                                ParentId = currentStep.StepId,
                                StepType = s.StepType,
                                StepValue = s.StepValue,
                                OrderId = i + 1,
                                Enable = 1,
                                CreateDate = DateTime.Now
                            }).FirstOrDefault();

                            if (endStep != null)
                            {
                                steps.Add(endStep);
                            }
                        }
                        break; // 结束循环
                    }
                }

                _logger.LogInformation($"生成工作流步骤成功: WorkFlowTableId={workFlowTableId}, StepCount={steps.Count}");
                return steps;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"生成工作流步骤失败: WorkFlowTableId={workFlowTableId}");
                throw;
            }
        }

        private async Task HandleSpecialAuditTypes<T>(List<Sys_WorkFlowTableStep> steps, T entity) where T : class
        {
            try 
            {
                // 检查是否存在需要上级审批的步骤
                if (!steps.Exists(x => x.StepType == (int)AuditType.提交人上级部门审批 
                    || x.StepType == (int)AuditType.提交人上级角色审批))
                {
                    return;
                }

                string stepMsg = null;
                var dbContext = _unitOfWork.GetDbContext<SysDbContext>();

                // 处理上级部门审批
                if (steps.Exists(x => x.StepType == (int)AuditType.提交人上级部门审批))
                {
                    // 获取提交人上级部门ID
                    string parentDeptIds = await GetParentDeptIds<T>(entity);
                    
                    if (parentDeptIds == null)
                    {
                        stepMsg = "数据找不到提交人的上级部门,流程不能正常进行";
                        _logger.LogWarning($"表【{typeof(T).Name}】数据找不到提交人的上级部门,提交数据:{entity.Serialize()}");
                    }

                    // 更新所有上级部门审批步骤
                    foreach (var step in steps.Where(x => x.StepType == (int)AuditType.提交人上级部门审批))
                    {
                        step.StepType = (int)AuditType.部门审批;
                        step.StepValue = parentDeptIds;
                        step.Remark = stepMsg;
                    }
                }

                // 处理上级角色审批
                if (steps.Exists(x => x.StepType == (int)AuditType.提交人上级角色审批))
                {
                    // 获取提交人上级角色ID
                    string parentRoleIds = await GetParentRoleIds<T>(entity);

                    if (parentRoleIds == null)
                    {
                        stepMsg = "数据找不到提交人的上级角色,流程不能正常进行";
                        _logger.LogWarning($"表【{typeof(T).Name}】数据找不到提交人的上级角色,提交数据:{entity.Serialize()}");
                    }

                    // 更新所有上级角色审批步骤
                    foreach (var step in steps.Where(x => x.StepType == (int)AuditType.提交人上级角色审批))
                    {
                        step.StepType = (int)AuditType.角色审批;
                        step.StepValue = parentRoleIds;
                        step.Remark = stepMsg;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理特殊审批类型失败: Entity={typeof(T).Name}");
                throw;
            }
        }

        private async Task<string> GetParentDeptIds<T>(T entity)
        {
            try
            {
                var property = typeof(T).GetProperties()
                    .FirstOrDefault(x => x.Name == AppSetting.CreateMember.UserIdField);
                    
                if (property == null)
                {
                    return null;
                }

                int userId = property.GetValue(entity).GetInt();
                var dbContext = _unitOfWork.GetDbContext<SysDbContext>();

                // 获取用户部门
                var deptIds = await dbContext.Set<Sys_UserDepartment>()
                    .Where(x => x.Enable == 1 && x.UserId == userId)
                    .Select(s => s.DepartmentId)
                    .ToListAsync();

                // 获取这些部门的上级部门
                var parentDeptIds = await dbContext.Set<Sys_Department>()
                    .Where(s => deptIds.Contains(s.DepartmentId) && s.ParentId != null)
                    .Select(s => (Guid)s.ParentId)
                    .Distinct()
                    .ToListAsync();

                return string.Join(",", parentDeptIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取上级部门ID失败: UserId={AppSetting.CreateMember.UserIdField}");
                return null;
            }
        }

        private async Task<string> GetParentRoleIds<T>(T entity)
        {
            try 
            {
                var property = typeof(T).GetProperties()
                    .FirstOrDefault(x => x.Name == AppSetting.CreateMember.UserIdField);
                    
                if (property == null)
                {
                    return null;
                }

                int userId = property.GetValue(entity).GetInt();
                var dbContext = _unitOfWork.GetDbContext<SysDbContext>();

                // 获取用户角色
                var roleIds = await dbContext.Set<Sys_UserRole>()
                    .Where(x => x.Enable == 1 && x.UserId == userId)
                    .Select(s => s.RoleId)
                    .ToListAsync();

                // 获取这些角色的上级角色
                var parentRoleIds = await dbContext.Set<Sys_Role>()
                    .Where(s => roleIds.Contains(s.Role_Id) && s.ParentId > 0)
                    .Select(s => s.ParentId)
                    .Distinct()
                    .ToListAsync();

                return string.Join(",", parentRoleIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取上级角色ID失败: UserId={AppSetting.CreateMember.UserIdField}");
                return null;
            }
        }

        public async Task<WebResponseContent> AuditAsync<T>(
            BaseDbContext tableDbContext,
            T entity,
            AuditStatus status,
            string remark,
            PropertyInfo autditProperty = null,
            Func<T, AuditStatus, bool, WebResponseContent> workFlowExecuting = null,
            Func<T, AuditStatus, List<int>, bool, WebResponseContent> workFlowExecuted = null,
            bool init = false,
            Action<T, List<int>> initInvoke = null,
            FlowWriteState flowWriteState = FlowWriteState.审批,
            string workFlowTableName = null) where T : class
        {
            WebResponseContent webResponse = new WebResponseContent(true);
            if (init)
            {
                if (!WorkFlowContainer.Exists<T>(workFlowTableName))
                {
                    return webResponse;
                }
            }
            
            return await ExecuteInTransactionAsync(async () =>
            {
                try
                {
                    var dbContext = _unitOfWork.GetDbContext<SysDbContext>(true);
                    
                    if (string.IsNullOrEmpty(workFlowTableName))
                    {
                        workFlowTableName = typeof(T).GetEntityTableName();
                    }

                    var keyProperty = typeof(T).GetKeyProperty();
                    string key = keyProperty.GetValue(entity).ToString();
                    string workTable = workFlowTableName ?? typeof(T).GetEntityTableName(false);

                    // 移除Order表关键字的字符
                    workTable = workTable.Replace("[", "").Replace("]", "");
                    
                    var workFlow = await dbContext.Set<Sys_WorkFlowTable>()
                        .Where(x => x.WorkTable == workTable && x.WorkTableKey == key)
                        .Include(x => x.Sys_WorkFlowTableStep)
                        .FirstOrDefaultAsync();

                    if (workFlow == null)
                    {
                        return webResponse.Error("未查到流程信息,请检查数据是否被删除");
                    }

                    // 处理重新开始流程的情况
                    if (flowWriteState == FlowWriteState.重新开始)
                    {
                        AntiData antiData = new AntiData()
                        {
                            AuditReason = remark,
                            IsFlow = true
                        };
                        antiData.StepId = workFlow.Sys_WorkFlowTableStep
                            .Where(c => c.StepAttrType != StepType.start.ToString())
                            .OrderBy(c => c.OrderId)
                            .Select(s => s.StepId)
                            .FirstOrDefault();
                            
                        return await AntiAuditAsync<T>(antiData, tableDbContext, entity, workFlowTableName, true);
                    }

                    workFlow.AuditStatus = (int)status;
                    var currentStep = workFlow.Sys_WorkFlowTableStep
                        .FirstOrDefault(x => x.StepId == workFlow.CurrentStepId);

                    if (currentStep == null)
                    {
                        return webResponse.Error($"未查到流程节点[{workFlow.CurrentStepId}]信息,请检查数据是否被删除");
                    }

                    Sys_WorkFlowTableStep nextStep = null;
                    string nextStepId = null;

                    // 处理多人审批
                    bool isMultiAudit = workFlow.Sys_WorkFlowTableStep
                        .Count(x => x.StepId == workFlow.CurrentStepId 
                            && (x.AuditStatus == null || x.AuditStatus == (int)AuditStatus.待审核)) > 1;

                    var filterOptions = WorkFlowContainer.GetFlowOptions(x => x.WorkFlow_Id == workFlow.WorkFlow_Id)
                        .FirstOrDefault()
                        ?.FilterList
                        ?.FirstOrDefault(x => x.StepId == currentStep.StepId);

                    var user = UserContext.Current.UserInfo;

                    // 处理多人并签情况
                    if (isMultiAudit && filterOptions?.AuditMethod == 1)
                    {
                        nextStepId = currentStep.StepId;
                        nextStep = currentStep;
                    }
                    else
                    {
                        nextStepId = currentStep.NextStepId;
                        nextStep = workFlow.Sys_WorkFlowTableStep
                            .FirstOrDefault(x => x.StepId == nextStepId);
                    }

                    if (nextStep != null)
                    {
                        workFlow.StepName = nextStep.StepName;
                    }

                    // 更新审核步骤信息
                    var auditStep = workFlow.Sys_WorkFlowTableStep
                        .FirstOrDefault(x => workFlow.CurrentStepId == x.StepId
                            && (x.AuditStatus == null || x.AuditStatus == (int)AuditStatus.待审核)
                            && CheckAuditUserValue(x.StepType, x.StepValue));

                    if (auditStep != null)
                    {
                        auditStep.AuditId = user.User_Id;
                        auditStep.Auditor = user.UserTrueName;
                        auditStep.AuditDate = DateTime.Now;
                        auditStep.AuditStatus = (int)status;
                        auditStep.Remark = remark;
                    }

                    // 创建审核日志
                    var log = new Sys_WorkFlowTableAuditLog()
                    {
                        Id = Guid.NewGuid(),
                        StepId = currentStep.StepId,
                        WorkFlowTable_Id = currentStep.WorkFlowTable_Id,
                        WorkFlowTableStep_Id = currentStep.Sys_WorkFlowTableStep_Id,
                        AuditDate = DateTime.Now,
                        AuditId = user.User_Id,
                        Auditor = user.UserTrueName,
                        AuditResult = remark,
                        Remark = remark,
                        AuditStatus = (int)status,
                        CreateDate = DateTime.Now,
                        StepName = currentStep.StepName
                    };

                    if (filterOptions != null)
                    {
                        // 处理审核未通过或驳回的情况
                        if (flowWriteState != FlowWriteState.审批 
                            || status == AuditStatus.审核未通过 
                            || status == AuditStatus.驳回)
                        {
                            switch (flowWriteState)
                            {
                                case FlowWriteState.回退上一级节点:
                                    log.AuditStatus = (int)AuditStatus.审核中;
                                    break;
                                case FlowWriteState.重新开始:
                                    log.AuditStatus = (int)AuditStatus.待审核;
                                    break;
                                case FlowWriteState.终止:
                                    log.AuditStatus = (int)AuditStatus.终止;
                                    break;
                            }

                            await dbContext.Set<Sys_WorkFlowTableAuditLog>().AddAsync(log);

                            if (autditProperty == null)
                            {
                                autditProperty = typeof(T).GetProperties()
                                    .FirstOrDefault(s => s.Name.ToLower() == "auditstatus");
                            }
                            autditProperty?.SetValue(entity, (int)status);

                            await UpdateAuditStatus<T>(
                                tableDbContext, 
                                entity,
                                workFlow,
                                filterOptions,
                                currentStep,
                                status,
                                remark,
                                flowWriteState,
                                isMultiAudit,
                                workFlowTableName);

                            SendMail(workFlow, filterOptions, nextStep);

                            if (workFlowExecuted != null)
                            {
                                webResponse = workFlowExecuted.Invoke(
                                    entity,
                                    status,
                                    await GetAuditUserIdsAsync(nextStep?.StepType ?? 0, nextStep?.StepValue),
                                    false);
                            }

                            return webResponse;
                        }
                    }

                    // 继续处理正常审核流程
                    bool isLast = nextStep == null || nextStep.StepAttrType == StepType.end.ToString();

                    if (isLast)
                    {
                        if (status == AuditStatus.审核通过)
                        {
                            workFlow.CurrentStepId = "审核完成";
                            var dateProperty = typeof(T).GetProperties()
                                .FirstOrDefault(x => x.Name.ToLower() == "auditdate");
                            dateProperty?.SetValue(entity, DateTime.Now);
                        }
                        else
                        {
                            workFlow.CurrentStepId = "流程结束";
                        }
                        
                        workFlow.AuditStatus = (int)status;
                        
                        if (workFlowExecuting != null)
                        {
                            webResponse = workFlowExecuting.Invoke(entity, status, true);
                            if (!webResponse.Status)
                            {
                                return webResponse;
                            }
                        }

                        SendMail(workFlow, filterOptions, nextStep);

                        autditProperty?.SetValue(entity, (int)status);
                        dbContext.Update(workFlow);
                        await dbContext.Set<Sys_WorkFlowTableAuditLog>().AddAsync(log);
                        await dbContext.SaveChangesAsync();

                        tableDbContext.Update(entity);
                        await tableDbContext.SaveChangesAsync();

                        if (workFlowExecuted != null)
                        {
                            webResponse = workFlowExecuted.Invoke(
                                entity,
                                status,
                                await GetAuditUserIdsAsync(nextStep?.StepType ?? 0, nextStep?.StepValue),
                                true);
                        }

                        return webResponse;
                    }

                    // 指向下一个审批节点
                    if (nextStep != null && status == AuditStatus.审核通过)
                    {
                        workFlow.CurrentStepId = nextStep.StepId;
                        autditProperty?.SetValue(entity, (int)AuditStatus.审核中);
                        workFlow.AuditStatus = (int)AuditStatus.审核中;
                    }
                    else
                    {
                        autditProperty?.SetValue(entity, (int)status);
                    }

                    if (workFlowExecuting != null)
                    {
                        webResponse = workFlowExecuting.Invoke(entity, status, isLast);
                        if (!webResponse.Status)
                        {
                            return webResponse;
                        }
                    }

                    // 保存所有更改
                    tableDbContext.Update(entity);
                    await tableDbContext.SaveChangesAsync();

                    dbContext.Update(workFlow);
                    await dbContext.Set<Sys_WorkFlowTableAuditLog>().AddAsync(log);
                    await dbContext.SaveChangesAsync();

                    dbContext.Entry(workFlow).State = EntityState.Detached;
                    tableDbContext.Entry(entity).State = EntityState.Detached;

                    if (workFlowExecuted != null)
                    {
                        webResponse = workFlowExecuted.Invoke(
                            entity,
                            status,
                            await GetAuditUserIdsAsync(nextStep?.StepType ?? 0, nextStep?.StepValue),
                            isLast);
                    }

                    SendMail(workFlow, filterOptions, nextStep);
                    return webResponse;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "审核流程失败");
                    return webResponse.Error($"审核失败:{ex.Message}");
                }
            }, "审核工作流");
        }

        // 实现其他接口方法...

        public async Task<TResult> ExecuteInTransactionAsync<TResult>(
            Func<Task<TResult>> action,
            string operationName)
        {
            // 使用AsyncTransactionContext进行事务管理
            return await _asyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
            {
                try
                {
                    // 记录操作开始
                    _logger.LogDebug($"开始执行操作[{operationName}]");

                    // 添加性能监控跟踪
                    var result = await unitOfWork.ExecuteWithMetricsAsync(action, operationName);

                    // 记录操作成功日志
                    _logger.LogInformation($"操作[{operationName}]执行成功");

                    return result;
                }
                catch (Exception ex)
                {
                    // 记录异常日志
                    _logger.LogError(ex, $"操作[{operationName}]执行失败");
                    throw;
                }
            }, IsolationLevel.ReadCommitted);
        }

        // 实现工具方法
        private bool CheckAuditUserValue(int? stepType, string stepValue = null)
        {
            switch (stepType)
            {
                case (int)AuditType.角色审批:
                    return UserContext.Current.RoleIds.Contains(stepValue.GetInt());
                case (int)AuditType.部门审批:
                    return UserContext.Current.DeptIds.Contains((Guid)stepValue.GetGuid());
                default:
                    return UserContext.Current.UserId == stepValue.GetInt();
            }
        }

        private async void SendMail(Sys_WorkFlowTable workFlow, FilterOptions filterOptions, Sys_WorkFlowTableStep nextStep)
        {
            try 
            {
                // 检查是否需要发送邮件
                if (filterOptions == null || filterOptions.SendMail != 1)
                {
                    return;
                }

                // 初始化下一步骤
                if (nextStep == null)
                {
                    nextStep = new Sys_WorkFlowTableStep();
                }

                // 获取审批用户ID
                var userIds = await GetAuditUserIdsAsync(nextStep.StepType ?? 0, nextStep.StepValue);
                if (userIds.Count == 0)
                {
                    return; 
                }

                // 获取用户邮箱
                var dbContext = _unitOfWork.GetDbContext<SysDbContext>();
                var emails = await dbContext.Set<Sys_User>()
                    .Where(x => userIds.Contains(x.User_Id) && !string.IsNullOrEmpty(x.Email))
                    .Select(s => s.Email)
                    .ToListAsync();

                // 异步发送邮件
                Task.Run(() =>
                {
                    string msg = "";
                    try
                    {
                        // 构建邮件标题
                        string title = $"有新的任务待审批：流程【{workFlow.WorkName}】,任务【{nextStep.StepName}】";
                        
                        // 发送邮件
                        MailHelper.Send(title, title, false, string.Join(";", emails));
                        
                        // 记录日志
                        msg = $"审批流程发送邮件,流程名称：{workFlow.WorkName},流程id:{workFlow.WorkFlow_Id}," + 
                              $"步骤:{nextStep.StepName},步骤Id:{nextStep.StepId},收件人:{string.Join(";", emails)}";
                        //Logger.AddAsync(msg);
                    }
                    catch (Exception ex)
                    {
                        msg += "邮件发送异常：";
                      //  Logger.AddAsync(msg, ex.Message + ex.StackTrace);
                        _logger.LogError(ex, $"发送邮件失败: {msg}");
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理邮件发送失败");
            }
        }

      

        private async Task<WebResponseContent> UpdateAuditStatus<T>(
            BaseDbContext tableDbContext,
            T entity,
            Sys_WorkFlowTable workFlow,
            FilterOptions filterOptions,
            Sys_WorkFlowTableStep currentStep,
            AuditStatus status,
            string remark,
            FlowWriteState flowWriteState,
            bool isMultiAudit,
            string workFlowTableName = null) where T : class
        {
            WebResponseContent webResponse = new WebResponseContent(true);
            var auditProperty = typeof(T).GetProperties()
                .FirstOrDefault(x => x.Name.ToLower() == "auditstatus");

            if (auditProperty == null)
            {
                return webResponse.Error("表缺少审核状态字段：AuditStatus");
            }

            var dbContext = _unitOfWork.GetDbContext<SysDbContext>(true);
      
            if (flowWriteState == FlowWriteState.终止)
            {
                status = AuditStatus.终止;
                workFlow.AuditStatus = (int)status;
                auditProperty.SetValue(entity, (int)status);
            }
            else if (flowWriteState == FlowWriteState.回退上一级节点
                || (status == AuditStatus.审核未通过 && filterOptions.AuditRefuse == (int)AuditRefuse.返回上一节点)
                || (status == AuditStatus.驳回 && filterOptions.AuditBack == (int)AuditBack.返回上一节点))
            {
                // 获取上一个节点信息
                var preSteps = workFlow.Sys_WorkFlowTableStep
                    .Where(x => x.NextStepId == currentStep.StepId && x.StepAttrType == StepType.node.ToString())
                    .ToList();

                if (preSteps.Count > 0)
                {
                    foreach (var preStep in preSteps)
                    {
                        preStep.AuditStatus = null;
                        preStep.AuditId = null;
                        preStep.AuditDate = null;
                        preStep.Auditor = null;
                        preStep.Remark = null;

                        dbContext.Update(preStep);
                    }

                    workFlow.CurrentStepId = preSteps[0].StepId;
                    workFlow.StepName = preSteps[0].StepName;
                    workFlow.AuditStatus = (int)AuditStatus.审核中;
                    auditProperty.SetValue(entity, (int)AuditStatus.审核中);
                }
                else
                {
                    // 没有找到上一个节点，默认当前节点就是第一个节点
                    workFlow.CurrentStepId = currentStep.StepId;
                    workFlow.StepName = currentStep.StepName;
                    workFlow.AuditStatus = (int)AuditStatus.待审核;
                    auditProperty.SetValue(entity, (int)AuditStatus.待审核);
                }

                //清空当前节点的审批信息(2024.05.21)
                workFlow.Sys_WorkFlowTableStep.ForEach(x =>
                {
                    if (x.StepId == currentStep.StepId)
                    {
                        x.AuditStatus = null;
                        x.AuditId = null;
                        x.AuditDate = null;
                        x.Auditor = null;
                        x.Remark = null;
                        dbContext.Update(x);
                    }
                });
            }
            else if (flowWriteState == FlowWriteState.重新开始
                || (status == AuditStatus.审核未通过 && filterOptions.AuditRefuse == (int)AuditRefuse.流程重新开始)
                || (status == AuditStatus.驳回 && filterOptions.AuditBack == (int)AuditBack.流程重新开始))
            {
                // 获取流程初始配置状态
                var auditStatus = WorkFlowContainer.GetFlowOptions(entity, workFlowTableName)?.DefaultAuditStatus;
                if (auditStatus == null && !string.IsNullOrEmpty(workFlowTableName))
                {
                    auditStatus = WorkFlowContainer.GetFlowOptions(c => c.WorkTable == workFlowTableName)
                        .Select(s => s.DefaultAuditStatus)
                        .FirstOrDefault();
                }

                bool defaultAuditStatus = false;
                if (auditStatus == AuditStatus.草稿 || auditStatus == AuditStatus.待提交)
                {
                    defaultAuditStatus = true;
                    auditProperty.SetValue(entity, (int)auditStatus);
                }
                else
                {
                    auditProperty.SetValue(entity, (int)AuditStatus.待审核);
                }

                // 重置所有节点状态
                var steps = workFlow.Sys_WorkFlowTableStep
                    .Where(x => x.StepAttrType == StepType.node.ToString() && (x.AuditStatus >= 0))
                    .ToList();

                if (steps.Count > 0)
                {
                    foreach (var item in steps)
                    {
                        item.AuditStatus = null;
                        item.AuditId = null;
                        item.AuditDate = null;
                        item.Auditor = null;
                    }

                    // 重新指向第一个节点
                    workFlow.CurrentStepId = steps.OrderBy(c => c.OrderId).Select(c => c.StepId).FirstOrDefault();
                    workFlow.AuditStatus = defaultAuditStatus ? (int)auditStatus : (int)AuditStatus.审核中;
                    dbContext.UpdateRange(steps);
                }
            }

            var user = UserContext.Current.UserInfo;

            // 记录审核日志信息
            if (status == AuditStatus.审核未通过 || status == AuditStatus.驳回)
            {
                string msg = null;
                if (status == AuditStatus.审核未通过)
                {
                    msg = filterOptions.AuditRefuse == (int)AuditRefuse.返回上一节点
                        ? "审批未通过,返回上一节点"
                        : filterOptions.AuditRefuse == (int)AuditRefuse.流程重新开始
                            ? "审批未通过,流程重新开始"
                            : null;
                }
                else
                {
                    msg = filterOptions.AuditBack == (int)AuditBack.返回上一节点
                        ? "审批被驳回,返回上一节点"
                        : filterOptions.AuditBack == (int)AuditBack.流程重新开始
                            ? "审批被驳回,流程重新开始"
                            : null;
                }

                if (msg != null)
                {
                    var auditLog = new Sys_WorkFlowTableAuditLog()
                    {
                        Id = Guid.NewGuid(),
                        StepId = currentStep.StepId,
                        WorkFlowTable_Id = currentStep.WorkFlowTable_Id,
                        WorkFlowTableStep_Id = currentStep.Sys_WorkFlowTableStep_Id,
                        AuditDate = DateTime.Now,
                        AuditId = user.User_Id,
                        Auditor = user.UserTrueName,
                        AuditResult = remark,
                        Remark = msg,
                        AuditStatus = (int)status,
                        CreateDate = DateTime.Now,
                        StepName = currentStep.StepName
                    };
                    await dbContext.Set<Sys_WorkFlowTableAuditLog>().AddAsync(auditLog);
                }
            }

            // 保存更改
            dbContext.Update(workFlow);
            dbContext.SaveChanges();
            dbContext.Entry(workFlow).State = EntityState.Detached;

            tableDbContext.Update(entity);
            tableDbContext.SaveChanges();
            tableDbContext.Entry(entity).State = EntityState.Detached;

            return webResponse.OK();
        }

        public async Task<WebResponseContent> AntiAuditAsync<T>(
            AntiData antiData, 
            BaseDbContext dbContext, 
            T entity, 
            string workFlowTableName, 
            bool restart = false) where T : class
        {
            var webResponse = new WebResponseContent();
            var keyProperty = typeof(T).GetKeyProperty();
            string key = keyProperty.GetValue(entity).ToString();
            string workTable = workFlowTableName ?? typeof(T).GetEntityTableName(false);

            try 
            {
                var sysDbContext = _unitOfWork.GetDbContext<SysDbContext>();

                // 查询工作流信息
                var workFlow = await sysDbContext.Set<Sys_WorkFlowTable>()
                    .Where(x => x.WorkTable == workTable && x.WorkTableKey == key)
                    .Include(x => x.Sys_WorkFlowTableStep)
                    .FirstOrDefaultAsync();

                if (workFlow == null)
                {
                    return webResponse.Error("未查到流程信息,请检查数据是否被删除");
                }

                // 获取当前步骤
                var step = workFlow.Sys_WorkFlowTableStep
                    .FirstOrDefault(c => c.StepId == antiData.StepId);
                    
                if (step == null)
                {
                    return webResponse.Error("未找到指定的审批步骤");
                }

                // 获取当前步骤及后续步骤
                var steps = workFlow.Sys_WorkFlowTableStep
                    .Where(c => c.OrderId >= step.OrderId)
                    .ToList();

                // 重置步骤审核状态
                foreach (var item in steps)
                {
                    item.AuditStatus = null;
                    item.AuditId = null;
                    item.AuditDate = null;
                    item.Auditor = null;
                    item.Remark = null;
                }

                // 更新工作流表
                workFlow.CurrentStepId = step.StepId;
                workFlow.StepName = step.StepName;
                workFlow.AuditStatus = (int)AuditStatus.审核中;

                // 更新实体的审核状态
                var auditProperty = typeof(T).GetProperties()
                    .FirstOrDefault(x => x.Name.ToLower() == "auditstatus");
                auditProperty?.SetValue(entity, (int)AuditStatus.审核中);

                // 保存更改
                sysDbContext.Update(workFlow);
                sysDbContext.UpdateRange(steps);
                await sysDbContext.SaveChangesAsync();

                dbContext.Update(entity);
                await dbContext.SaveChangesAsync();

                return webResponse.OK();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "反审核流程失败");
                return webResponse.Error($"反审核失败:{ex.Message}");
            }
        }

        public async Task<object> GetAuditFormDataAsync(string tableKey, string table)
        {
            try
            {
                // 获取表对应的类型
                Type type = WorkFlowContainer.GetType(table);
                if (type == null)
                {
                    return Array.Empty<object>();
                }

                // 获取明细配置
                var detailOptions = WorkFlowContainer.GetDetail(type).FirstOrDefault();

                // 使用反射调用泛型方法
                var methodInfo = typeof(WorkFlowService).GetMethod(nameof(GetFormDataAsync));
                var genericMethod = methodInfo.MakeGenericMethod(new Type[] { type, detailOptions?.Type ?? type });
                
                var result = await (Task<object>)genericMethod.Invoke(
                    this, 
                    new object[] { tableKey, table, detailOptions }
                );

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取审批表单数据失败: Table={table}, Key={tableKey}");
                return Array.Empty<object>();
            }
        }

        public async Task<object> GetFormDataAsync<T, Detail>(
            string tableKey, 
            string table,
            WorkFlowFormDetails flowFormDetails)
            where T : class 
            where Detail : class
        {
            try
            {
                // 获取需要显示的字段
                string[] fields = WorkFlowContainer.GetFormFields(table);
                if (fields == null || fields.Length == 0)
                {
                    return Array.Empty<object>();
                }

                // 构建查询条件
                string keyName = typeof(T).GetKeyName();
                var dbContext = _unitOfWork.GetDbContext<SysDbContext>();
                var query = dbContext.Set<T>().AsQueryable();

                // 查询主表数据
                var data = await query.FirstOrDefaultAsync(ExpressionBuilder.CreateEqual<T>(keyName, tableKey));
                if (data == null)
                {
                    _logger.LogWarning($"未查到数据,表：{table},id:{tableKey}");
                    return Array.Empty<object>();
                }

                // 处理主表数据
                var mainList = GetFormData(new List<T> { data }, fields);

                if (mainList.Count == 0)
                {
                    string msg = $"未查到审核流程表【{typeof(T).Name}】id【{tableKey}】数据";
                    _logger.LogWarning(msg);
                    return new { data = mainList.FirstOrDefault() };
                }

                // 如果有明细表配置,处理明细表数据
                if (flowFormDetails != null)
                {
                    var detailQuery = dbContext.Set<Detail>().AsQueryable();
                    var detailData = await detailQuery
                        .Where(ExpressionBuilder.CreateEqual<Detail>(keyName, tableKey))
                        .ToListAsync();

                    var detailFormData = GetFormData(detailData, flowFormDetails.FormFields);

                    return new
                    {
                        key = keyName,
                        data = mainList[0],
                        detail = new
                        {
                            name = typeof(Detail).GetEntityTableCnName(),
                            data = detailFormData
                        }
                    };
                }

                return new
                {
                    key = keyName,
                    data = mainList[0]
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取表单数据失败: Table={table}, Key={tableKey}");
                throw;
            }
        }

        public List<object> GetFormData<T>(List<T> entities, string[] fields) where T : class
        {
            try
            {
                string table = typeof(T).Name;

                // 获取列配置信息
                var dbContext = _unitOfWork.GetDbContext<SysDbContext>();
                var tableOptions = dbContext.Set<Sys_TableColumn>()
                    .Where(c => c.TableName == table && fields.Contains(c.ColumnName))
                    .Select(s => new
                    {
                        s.ColumnName,
                        s.ColumnCnName,
                        s.DropNo,
                        isDate = s.IsImage == 4,
                        s.ColumnType,
                        s.EditRowNo,
                        s.EditType,
                        s.IsNull
                    })
                    .ToList();

                // 获取数据字典
                List<Sys_Dictionary> dictionaries = new List<Sys_Dictionary>();
                var dicNos = tableOptions.Select(s => s.DropNo).ToList();
                if (dicNos.Any())
                {
                    dictionaries = DictionaryManager.GetDictionaries(dicNos, true).ToList();
                }

                // 获取可编辑字段
                string[] editFormFields = WorkFlowContainer.GetEditFields(table) ?? Array.Empty<string>();
                var properties = typeof(T).GetProperties();

                var resultList = new List<object>();
                var index = 0;

                foreach (var entity in entities)
                {
                    index++;
                    var item = new Dictionary<string, object>();

                    foreach (var field in fields)
                    {
                        var property = properties.FirstOrDefault(p => p.Name == field);
                        string value = property?.GetValue(entity)?.ToString();
                        var option = tableOptions.FirstOrDefault(c => c.ColumnName == field);
                        
                        string name = option?.ColumnCnName ?? property?.GetDisplayName();

                        if (option == null || string.IsNullOrEmpty(value))
                        {
                            if (editFormFields.Contains(field))
                            {
                                item[field] = new
                                {
                                    name,
                                    field,
                                    value,
                                    isEdit = true,
                                    editRow = option?.EditRowNo,
                                    editType = option?.EditType,
                                    formType = option?.EditType,
                                    require = option?.IsNull == 1 ? false : true,
                                    dropNo = option?.DropNo
                                };
                            }
                            else
                            {
                                item[field] = new
                                {
                                    name,
                                    field,
                                    value,
                                    formType = option?.EditType,
                                    dropNo = option?.DropNo
                                };
                            }
                            continue;
                        }

                        string orgVal = value;
                        
                        // 处理日期类型
                        if (option.isDate)
                        {
                            value = value.GetDateTime()?.ToString("yyyy-MM-dd");
                        }
                        else if (option.ColumnType == "DateTime")
                        {
                            value = value.GetDateTime()?.ToString("yyyy-MM-dd HH:mm:ss");
                        }
                        // 处理下拉框类型
                        else if (!string.IsNullOrEmpty(option.DropNo))
                        {
                            string val = null;
                            var dictionary = dictionaries.FirstOrDefault(d => d.DicNo == option.DropNo);

                            if (option.EditType == "selectList" 
                                || option.EditType == "checkbox" 
                                || option.EditType == "treeSelect")
                            {
                                var arr = value.Split(",")
                                    .Select(v => dictionary?.Sys_DictionaryList
                                        .FirstOrDefault(d => d.DicValue == v)?.DicName)
                                    .Where(n => n != null)
                                    .ToArray();
                                val = string.Join(",", arr);
                            }
                            else
                            {
                                val = dictionary?.Sys_DictionaryList
                                    .FirstOrDefault(d => d.DicValue == value)?.DicName;
                            }

                            if (!string.IsNullOrEmpty(val))
                            {
                                value = val;
                            }
                        }

                        if (editFormFields.Contains(field))
                        {
                            item[field] = new
                            {
                                name,
                                field,
                                value,
                                orgVal,
                                isEdit = true,
                                formType = option.EditType,
                                editRow = option.EditRowNo,
                                editType = option.EditType,
                                require = option.IsNull == 1 ? false : true,
                                dropNo = option.DropNo
                            };
                        }
                        else
                        {
                            item[field] = new
                            {
                                name,
                                field,
                                value,
                                orgVal,
                                formType = option.EditType,
                                dropNo = option.DropNo
                            };
                        }
                    }

                    resultList.Add(item);
                }

                return resultList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理表单数据失败: Entity={typeof(T).Name}");
                throw;
            }
        }

       

       

      

        public async Task<WebResponseContent> UpdateFlowDataAsync(Sys_WorkFlow workFlow, List<Sys_WorkFlowStep> add)
        {
            try
            {
                // 如果不允许审核中编辑,直接返回
                if (workFlow.AuditingEdit != 1)
                {
                    return new WebResponseContent(true);
                }

                // 检查步骤是否存在
                if (add == null || add.Count == 0)
                {
                    return new WebResponseContent(true);
                }

                var dbContext = _unitOfWork.GetDbContext<SysDbContext>();

                // 获取原有流程步骤
                var flowSteps = await dbContext.Set<Sys_WorkFlowStep>()
                    .Where(x => x.WorkFlow_Id == workFlow.WorkFlow_Id)
                    .ToListAsync();

                // 设置原有步骤的下一步
                foreach (var item in flowSteps)
                {
                    item.NextStepIds = flowSteps
                        .Where(c => c.ParentId == item.StepId)
                        .Select(c => c.StepId)
                        .FirstOrDefault();
                }

                // 设置新增步骤的下一步
                foreach (var item in add)
                {
                    item.NextStepIds = flowSteps
                        .Where(c => c.StepId == item.StepId)
                        .Select(c => c.NextStepIds)
                        .FirstOrDefault();
                }

                // 只处理节点类型的步骤
                add = add.Where(x => x.StepAttrType == StepType.node.ToString()).ToList();

                // 查询正在审核中的工作流
                var flowTables = await dbContext.Set<Sys_WorkFlowTable>()
                    .Where(x => x.WorkFlow_Id == workFlow.WorkFlow_Id
                        && (x.AuditStatus == (int)AuditStatus.待审核 
                            || x.AuditStatus == (int)AuditStatus.审核中))
                    .Include(x => x.Sys_WorkFlowTableStep)
                    .ToListAsync();

                List<Guid> updateFlowIds = new List<Guid>();
                List<Guid> ignoreFlowIds = new List<Guid>();

                foreach (var workFlowTable in flowTables)
                {
                    for (int i = 0; i < workFlowTable.Sys_WorkFlowTableStep.Count; i++)
                    {
                        var step = workFlowTable.Sys_WorkFlowTableStep[i];

                        // 记录父级节点变更
                        string parentStepId = add
                            .Where(x => x.NextStepIds == step.StepId)
                            .Select(c => c.StepId)
                            .FirstOrDefault();

                        if (!string.IsNullOrEmpty(parentStepId))
                        {
                            step.ParentId = parentStepId;
                        }

                        // 找出新加的节点前一个节点(上级)
                        var addItems = add.Where(x => x.ParentId == step.StepId).ToList();
                        if (addItems.Count > 0)
                        {
                            // 设置原有节点的下一个流程
                            step.NextStepId = addItems[0].StepId;

                            // 找最后一个节点
                            if (addItems[0].NextStepIds == null)
                            {
                                addItems[0].NextStepIds = workFlowTable.Sys_WorkFlowTableStep
                                    .Where(x => x.StepAttrType == StepType.end.ToString())
                                    .Select(c => c.StepId)
                                    .FirstOrDefault();
                            }

                            // 添加新节点
                            workFlowTable.Sys_WorkFlowTableStep.AddRange(addItems.Select(s => new Sys_WorkFlowTableStep()
                            {
                                WorkFlowTable_Id = workFlowTable.WorkFlowTable_Id,
                                WorkFlow_Id = s.WorkFlow_Id,
                                StepId = s.StepId,
                                StepName = s.StepName,
                                StepType = s.StepType,
                                StepValue = s.StepValue,
                                StepAttrType = s.StepAttrType,
                                Enable = 1,
                                NextStepId = s.NextStepIds,
                                OrderId = null,
                                ParentId = s.ParentId,
                                CreateDate = DateTime.Now
                            }));
                        }
                    }

                    // 检查上下级节点关系
                    bool invalidFlow = workFlowTable.Sys_WorkFlowTableStep
                        .Any(x => (x.StepAttrType != StepType.start.ToString() 
                                  && !workFlowTable.Sys_WorkFlowTableStep.Any(c => c.StepId == x.ParentId))
                            || (x.StepAttrType != StepType.end.ToString() 
                                && !workFlowTable.Sys_WorkFlowTableStep.Any(c => c.StepId == x.NextStepId)));

                    if (invalidFlow)
                    {
                        ignoreFlowIds.Add(workFlowTable.WorkFlowTable_Id);
                        continue;
                    }

                    // 重新排序
                    string parentStep = workFlowTable.Sys_WorkFlowTableStep
                        .Where(x => x.StepAttrType == StepType.start.ToString())
                        .Select(c => c.StepId)
                        .FirstOrDefault();

                    int index = 1;
                    while (!string.IsNullOrEmpty(parentStep))
                    {
                        var list = workFlowTable.Sys_WorkFlowTableStep
                            .Where(x => x.ParentId == parentStep)
                            .ToList();

                        if (list.Count > 0)
                        {
                            foreach (var item in list)
                            {
                                item.OrderId = index++;
                            }
                            parentStep = list[0].StepId;
                        }
                        else
                        {
                            break;
                        }
                    }

                    // 设置当前审批节点
                    string currentId = workFlowTable.Sys_WorkFlowTableStep
                        .Where(x => x.StepAttrType != StepType.start.ToString() 
                            && (x.AuditStatus == null || x.AuditStatus == (int)AuditStatus.待审核)
                            && !workFlowTable.Sys_WorkFlowTableStep
                                .Any(c => c.OrderId > x.OrderId && c.AuditStatus > (int)AuditStatus.待审核))
                        .OrderBy(x => x.OrderId)
                        .Select(s => s.StepId)
                        .FirstOrDefault();

                    if (!string.IsNullOrEmpty(currentId) && currentId != workFlowTable.CurrentStepId)
                    {
                        workFlowTable.CurrentStepId = currentId;
                        updateFlowIds.Add(workFlowTable.WorkFlowTable_Id);
                    }
                }

                // 更新工作流表
                if (updateFlowIds.Count > 0)
                {
                    var updateList = flowTables
                        .Where(x => updateFlowIds.Contains(x.WorkFlowTable_Id))
                        .ToList();
                    dbContext.UpdateRange(updateList, x => new { x.CurrentStepId });
                }

                // 处理新增步骤
                var newSteps = flowTables
                    .SelectMany(x => x.Sys_WorkFlowTableStep)
                    .Where(c => c.Sys_WorkFlowTableStep_Id == Guid.Empty && c.OrderId > 0)
                    .ToList();

                foreach (var item in newSteps)
                {
                    item.Sys_WorkFlowTableStep_Id = Guid.NewGuid();
                }

                if (newSteps.Any())
                {
                    await dbContext.AddRangeAsync(newSteps);
                }

                // 更新现有步骤
                var updateSteps = flowTables
                    .Where(x => !ignoreFlowIds.Contains(x.WorkFlowTable_Id))
                    .SelectMany(x => x.Sys_WorkFlowTableStep)
                    .ToList();

                if (updateSteps.Any())
                {
                    dbContext.UpdateRange(updateSteps, x => new { x.NextStepId, x.ParentId, x.OrderId });
                }

                await dbContext.SaveChangesAsync();

                return new WebResponseContent(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新工作流数据失败");
                return new WebResponseContent(false).Error($"更新失败:{ex.Message}");
            }
        }

        public async Task<List<int>> GetAuditUserIdsAsync(int stepType, string nextId = null)
        {
            try
            {
                var userIds = new List<int>();
                
                // 检查输入参数
                if (stepType == 0 || string.IsNullOrEmpty(nextId))
                {
                    return userIds;
                }

                var dbContext = _unitOfWork.GetDbContext<SysDbContext>();

                switch (stepType)
                {
                    case (int)AuditType.角色审批:
                        // 获取角色下的用户
                        int roleId = nextId.GetInt();
                        userIds = await dbContext.Set<Sys_UserRole>()
                            .Where(s => s.RoleId == roleId && s.Enable == 1)
                            .Take(200)
                            .Select(s => s.UserId)
                            .ToListAsync();
                        break;

                    case (int)AuditType.部门审批:
                        // 获取部门下的用户 
                        Guid departmentId = nextId.GetGuid() ?? Guid.Empty;
                        userIds = await dbContext.Set<Sys_UserDepartment>()
                            .Where(s => s.DepartmentId == departmentId && s.Enable == 1)
                            .Take(200)
                            .Select(s => s.UserId)
                            .ToListAsync();
                        break;

                    default:
                        // 直接使用指定的用户ID列表
                        userIds = nextId.Split(",")
                            .Select(c => c.GetInt())
                            .ToList();
                        break;
                }

                _logger.LogInformation($"获取审批用户成功: StepType={stepType}, NextId={nextId}, UserCount={userIds.Count}");
                return userIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取审批用户失败: StepType={stepType}, NextId={nextId}");
                return new List<int>();
            }
        }

        /// <summary>
        /// 增加审批记录
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="keys">主键值数组</param>
        /// <param name="auditStatus">审核状态</param>
        /// <param name="auditReason">审核原因</param>
        public async Task AddAuditLogAsync<T>(object[] keys, int? auditStatus, string auditReason) where T : class
        {
            try
            {
                if (keys == null || !auditStatus.HasValue)
                {
                    return;
                }

                var userInfo = UserContext.Current.UserInfo;
                if (userInfo == null)
                {
                    return;
                }

                // 获取主表数据
                var dbContext = _unitOfWork.GetDbContext<SysDbContext>();
                var tableName = typeof(T).GetEntityTableName(false);
                string keyName = typeof(T).GetKeyName();

                // 构建查询表达式
                var keyValue = string.Join(",", keys);
                var whereExpression = keyName.CreateExpression<T>(keyValue, LinqExpressionType.In);
                var entities = await dbContext.Set<T>().Where(whereExpression).ToListAsync();
                
                if (!entities.Any())
                {
                    _logger.LogWarning($"审核日志记录未找到数据,表:{tableName},id:{keyValue}");
                    return;
                }

                // 获取流程记录
                var workFlowTables = await dbContext.Set<Sys_WorkFlowTable>()
                    .Where(x => x.WorkTable == tableName && keys.Contains(x.WorkTableKey))
                    .Include(x => x.Sys_WorkFlowTableStep)
                    .ToListAsync();

                var logs = new List<Sys_WorkFlowTableAuditLog>();
                
                foreach (var workFlow in workFlowTables)
                {
                    // 构建审核日志
                    var currentStepId = workFlow.CurrentStepId;
                    var currentStep = workFlow.Sys_WorkFlowTableStep
                        .FirstOrDefault(x => x.StepId == currentStepId);

                    var log = new Sys_WorkFlowTableAuditLog
                    {
                        Id = Guid.NewGuid(),
                        WorkFlowTable_Id = workFlow.WorkFlowTable_Id,
                        WorkFlowTableStep_Id = currentStep?.Sys_WorkFlowTableStep_Id,
                        StepId = currentStep?.StepId,
                        StepName = currentStep?.StepName ?? "",
                        AuditId = userInfo.User_Id,
                        Auditor = userInfo.UserTrueName,
                        AuditResult = auditReason,
                        AuditDate = DateTime.Now,
                        AuditStatus = auditStatus.Value,
                        Remark = auditReason,
                        CreateDate = DateTime.Now
                    };

                    logs.Add(log);
                }

                if (logs.Any())
                {
                    // 批量添加日志
                    await dbContext.Set<Sys_WorkFlowTableAuditLog>().AddRangeAsync(logs);
                    await dbContext.SaveChangesAsync();
                    
                    _logger.LogInformation(
                        $"添加审核日志成功,表:{tableName},Keys:[{keyValue}],状态:{auditStatus},原因:{auditReason}"
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, 
                    $"添加审核日志失败,Keys:[{string.Join(",", keys)}],状态:{auditStatus},原因:{auditReason}"
                );
                throw;
            }
        }
    }
}
