﻿using Dapper;
using System;
using Gray.Core.Const;
using Gray.Core.Enums;

namespace Gray.Core.Dapper
{
    public class DapperParseGuidTypeHandler
    {
        public static void InitParseGuid()
        {
            if (DBType.Name == DbCurrentType.MySql.ToString())
            {
                SqlMapper.AddTypeHandler(new DapperParseGuidTypeHandlerMySql());
                SqlMapper.RemoveTypeMap(typeof(Guid?));
            }
            else if (DBType.Name == DbCurrentType.Oracle.ToString())
            {
                SqlMapper.AddTypeHandler(new DapperParseGuidTypeHandlerOracle());
                SqlMapper.RemoveTypeMap(typeof(Guid));
                SqlMapper.AddTypeHandler(new DapperParseGuidNullTypeHandlerOracle());
                SqlMapper.RemoveTypeMap(typeof(Guid?));
            }
        }
    }
}