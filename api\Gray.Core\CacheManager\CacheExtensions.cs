using Microsoft.Extensions.DependencyInjection;
using System;

namespace Gray.Core.CacheManager
{
    public static class CacheExtensions
    {
        public static IServiceCollection AddCacheService(this IServiceCollection services)
        {
            // 注册默认内存缓存服务作为备选
            services.AddSingleton<ICacheService, MemoryCacheService>();
            
            return services;
        }
    }
}
