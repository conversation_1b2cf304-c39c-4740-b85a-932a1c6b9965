﻿using Microsoft.EntityFrameworkCore;
using Gray.Core.DBManager;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.SystemModels;
using System;

namespace Gray.Core.EFDbContext
{
    public class JL_EAMDbContext : BaseDbContext<JL_EAMDbContext>, IDependency
    {
        protected override string ConnectionString
        {
            get
            {
                return DBServerProvider.GetConnection("JL_EAMConn");
            }
        }

        public JL_EAMDbContext() : base()
        {
        }

        public JL_EAMDbContext(DbContextOptions<JL_EAMDbContext> options) : base(options)
        {
        }

       

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.UseDbTypeFromConfig(optionsBuilder, this.GetType().Name);
            //默认禁用实体跟踪
            optionsBuilder = optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            optionsBuilder
           .UseSqlServer(o => o.UseCompatibilityLevel(120));
            base.OnConfiguring(optionsBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
        }

        protected override Type GetBaseEntityType()
        {
            // 默认返回null，由派生类实现
            return typeof(JL_EAMEntity);
        }
    }
}