using Gray.Core.Configuration;
using Gray.Core.Const;
using Gray.Core.Dapper;
using Gray.Core.EFDbContext;
using Gray.Core.Enums;
using Gray.Core.Extensions;
using Gray.Core.ManageUser;
using Gray.Entity;
using Microsoft.Data.SqlClient;
using MySqlConnector;
using Npgsql;
using Oracle.ManagedDataAccess.Client;
using System;
using System.Data;

namespace Gray.Core.DBManager
{
    public partial class DBServerProvider
    {
        public static ISqlDapper SqlMain
        {
            get
            {
                return SqlDapper;
            }
        }

        public static ISqlDapper SqlDapper
        {
            get
            {
                return new SqlDapper(MainDB);
            }
        }
        /// <summary>
        /// 扩展dapper 获取MSSQL数据库DbConnection，默认系统获取配置文件的DBType数据库类型，
        /// </summary>
        /// <param name="connString">如果connString为null 执行重载GetDbConnection(string connString = null)</param>
        /// <param name="dapperType">指定连接数据库的类型：MySql/MsSql/PgSql</param>
        /// <returns></returns>
        public static IDbConnection GetDbConnection(string connString = null, DbCurrentType dbCurrentType = DbCurrentType.Default)
        {
            return RetryOnFailure(() =>
            {
                if (connString == null)
                {
                    connString = ConnectionPool[MainDB].Connection;
                }

                if (dbCurrentType == DbCurrentType.Default)
                {
                    dbCurrentType = (DbCurrentType)Enum.Parse(typeof(DbCurrentType), DBType.Name);
                }

                IDbConnection connection = null;
                switch (dbCurrentType)
                {
                    case DbCurrentType.MySql:
                        connection = new MySqlConnection(connString);
                        break;

                    case DbCurrentType.PgSql:
                        connection = new NpgsqlConnection(connString);
                        break;

                    case DbCurrentType.Oracle:
                        connection = new OracleConnection(connString);
                        break;

                    default:
                        connection = new SqlConnection(connString);
                        break;
                }

                // 测试连接
                connection.Open();
                connection.Close();

                return connection;
            });
        }
        /// <summary>
        /// 指定获取数据库,这里同时支持mysql、sqlserver等不同类型数据库2024.06.20
        /// 需要在appsettings.json中Connection添加xxxDbTyp:"MySql/SqlServe等属性"
        /// </summary>
        /// <param name="dbService"></param>
        /// <returns></returns>
        public static ISqlDapper GetSqlDapperWidthDbService(string dbService)
        {
            string dbType = DBServerProvider.GetConnConfig(dbService).DbType ?? DBType.Name;
            return GetSqlDapper((DbCurrentType)Enum.Parse(typeof(DbCurrentType), dbType), dbService);
        }

        /// <summary>
        /// 根据数据库id或数据库key获取链接
        /// </summary>
        public static ISqlDapper GetSqlDapper(string dbName = null)
        {
            return new SqlDapper(string.IsNullOrEmpty(dbName) ? MainDB : dbName);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="dbCurrentType">指定数据库类型：MySql/MsSql/PgSql</param>
        /// <param name="dbName">指定数据连串名称</param>
        /// <returns></returns>
        public static ISqlDapper GetSqlDapper(DbCurrentType dbCurrentType, string dbName = null)
        {
            if (dbName != null && nameof(ServiceDbContext) == dbName && AppSetting.UseDynamicShareDB)
            {
                return GetSqlDapper(UserContext.CurrentServiceId.ToString());
            }
            return new SqlDapper(dbName ?? MainDB, dbCurrentType);
        }

        public static ISqlDapper GetSqlDapper<TEntity>()
        {
            string dbName = typeof(TEntity).GetTypeCustomValue<EntityAttribute>(x => x.DBServer) ?? MainDB;
            return GetSqlDapperWidthDbService(dbName);
        }
    }
}