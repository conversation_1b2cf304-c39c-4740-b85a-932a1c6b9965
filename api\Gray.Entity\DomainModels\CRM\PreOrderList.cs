/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "月订单计划明细",TableName = "PreOrderList",DBServer = "JL_CRMDbContext")]
    public partial class PreOrderList:JL_CRMEntity
    {
        /// <summary>
       ///预订单列Id
       /// </summary>
       [Key]
       [Display(Name ="预订单列Id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid PreOrderListId { get; set; }

       /// <summary>
       ///商品Id
       /// </summary>
       [Display(Name ="商品Id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       public Guid? GoodsId { get; set; }

       /// <summary>
       ///商品代码
       /// </summary>
       [Display(Name ="商品代码")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string GoodsCode { get; set; }

       /// <summary>
       ///商品规格
       /// </summary>
       [Display(Name ="商品规格")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string Specs { get; set; }

       /// <summary>
       ///单价
       /// </summary>
       [Display(Name ="单价")]
       [Column(TypeName="float")]
       public float? Price { get; set; }

       /// <summary>
       ///品牌
       /// </summary>
       [Display(Name ="品牌")]
       [MaxLength(50)]
       [Column(TypeName="nvarchar(50)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string GoodsBrand { get; set; }

       /// <summary>
       ///上半月发
       /// </summary>
       [Display(Name ="上半月发")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? QtySBY { get; set; }

       /// <summary>
       ///下半月发
       /// </summary>
       [Display(Name ="下半月发")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? QtyXBY { get; set; }

       /// <summary>
       ///总计数量
       /// </summary>
       [Display(Name ="总计数量")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int QtyCount { get; set; }

       /// <summary>
       ///合计价格
       /// </summary>
       [Display(Name ="合计价格")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? PriceCount { get; set; }

       /// <summary>
       ///商品名
       /// </summary>
       [Display(Name ="商品名")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string GoodsName { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Img")]
       [MaxLength(500)]
       [Column(TypeName="nvarchar(500)")]
       [Editable(true)]
       public string Img { get; set; }

       /// <summary>
       ///单重
       /// </summary>
       [Display(Name ="单重")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? WeightSingle { get; set; }

       /// <summary>
       ///总重
       /// </summary>
       [Display(Name ="总重")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? WeightCount { get; set; }

       /// <summary>
       ///备注
       /// </summary>
       [Display(Name ="备注")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       public string Remark { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? CreateID { get; set; }

       /// <summary>
       ///创建人
       /// </summary>
       [Display(Name ="创建人")]
       [MaxLength(30)]
       [Column(TypeName="nvarchar(30)")]
       [Editable(true)]
       public string Creator { get; set; }

       /// <summary>
       ///创建时间
       /// </summary>
       [Display(Name ="创建时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? CreateDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ModifyID { get; set; }

       /// <summary>
       ///修改人
       /// </summary>
       [Display(Name ="修改人")]
       [MaxLength(30)]
       [Column(TypeName="nvarchar(30)")]
       [Editable(true)]
       public string Modifier { get; set; }

       /// <summary>
       ///预订单id
       /// </summary>
       [Display(Name ="预订单id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       public Guid? PreOrderId { get; set; }

       /// <summary>
       ///修改日期
       /// </summary>
       [Display(Name ="修改日期")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? ModifyDate { get; set; }

       /// <summary>
       ///本区今年
       /// </summary>
       [Display(Name ="本区今年")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? SaleCurrentYear { get; set; }

       /// <summary>
       ///本区上月
       /// </summary>
       [Display(Name ="本区上月")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? SaleLastMonth { get; set; }

       
    }
}