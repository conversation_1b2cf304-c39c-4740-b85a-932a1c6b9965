/*
Tel:15662337743
 *所有业务编写全部应在Partial文件夹下App_ReportPriceService与IApp_ReportPriceService中编写
 */
using Gray.AppManager.IRepositories;
using Gray.AppManager.IServices;
using Gray.Core.BaseProvider;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.DomainModels;

namespace Gray.AppManager.Services
{
    public partial class App_ReportPriceService : ServiceBase<App_ReportPrice, IApp_ReportPriceRepository>, IApp_ReportPriceService, IDependency
    {
        public App_ReportPriceService(IApp_ReportPriceRepository repository)
             : base(repository) 
        { 
           Init(repository);
        }
        public static IApp_ReportPriceService Instance
        {
           get { return AutofacContainerModule.GetService<IApp_ReportPriceService>(); }
        }
    }
}
