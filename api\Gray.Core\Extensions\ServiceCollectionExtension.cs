using Gray.Core.Monitoring;
using Microsoft.Extensions.DependencyInjection;
using System;

public static class ServiceCollectionExtension
{
    public static IServiceCollection AddStoredSqlManager(this IServiceCollection services, 
        Action<SqlMonitoringOptions> configure = null)
    {
        var options = new SqlMonitoringOptions();
        configure?.Invoke(options);
        
        services.AddSingleton(options);
        services.AddSingleton<SqlPerformanceMonitor>();
        
        return services;
    }
}
