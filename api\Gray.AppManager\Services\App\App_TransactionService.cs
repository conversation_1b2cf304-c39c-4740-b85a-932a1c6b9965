/*
Tel:15662337743
 *所有业务编写全部应在Partial文件夹下App_TransactionService与IApp_TransactionService中编写
 */
using Gray.AppManager.IRepositories;
using Gray.AppManager.IServices;
using Gray.Core.BaseProvider;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.DomainModels;

namespace Gray.AppManager.Services
{
    public partial class App_TransactionService : ServiceBase<App_Transaction, IApp_TransactionRepository>, IApp_TransactionService, IDependency
    {
        public App_TransactionService(IApp_TransactionRepository repository)
             : base(repository) 
        { 
           Init(repository);
        }
        public static IApp_TransactionService Instance
        {
           get { return AutofacContainerModule.GetService<IApp_TransactionService>(); }
        }
    }
}
