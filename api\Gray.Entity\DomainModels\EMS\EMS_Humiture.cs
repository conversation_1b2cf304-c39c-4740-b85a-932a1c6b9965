/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "温湿度记录",TableName = "EMS_Humiture",DBServer = "JL_EMSDbContext")]
    public partial class EMS_Humiture:JL_EMSEntity
    {
        /// <summary>
       ///记录ID
       /// </summary>
       [Key]
       [Display(Name ="记录ID")]
       [Column(TypeName="bigint")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public long RecordID { get; set; }

       /// <summary>
       ///设备名称
       /// </summary>
       [Display(Name ="设备名称")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string DeviceName { get; set; }

       /// <summary>
       ///设备ID
       /// </summary>
       [Display(Name ="设备ID")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid DeviceID { get; set; }

       /// <summary>
       ///温度
       /// </summary>
       [Display(Name ="温度")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Temperature { get; set; }

       /// <summary>
       ///湿度
       /// </summary>
       [Display(Name ="湿度")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Humidity { get; set; }

       /// <summary>
       ///记录时间
       /// </summary>
       [Display(Name ="记录时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? RecordTime { get; set; }

       /// <summary>
       ///温度时间
       /// </summary>
       [Display(Name ="温度时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? TemperatureTime { get; set; }

       /// <summary>
       ///湿度时间
       /// </summary>
       [Display(Name ="湿度时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? HumidityTime { get; set; }

       /// <summary>
       ///设备时间戳
       /// </summary>
       [Display(Name ="设备时间戳")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? DeviceTimestamp { get; set; }

       
    }
}