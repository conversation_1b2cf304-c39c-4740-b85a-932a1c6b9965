﻿using System;

namespace Gray.Core.Extensions
{
    public static class DoubleExtensions
    {
        public static double 四舍五入(this double thisValue, int 保留小数位)
        {
            return Math.Round(thisValue, 保留小数位, MidpointRounding.AwayFromZero);
        }

        public static float 四舍五入(this float thisValue, int 保留小数位)
        {
            return Math.Round(thisValue.GetFloat(), 保留小数位, MidpointRounding.AwayFromZero).GetFloat();
        }

        /// <summary>
        /// 可空double类型的四舍五入扩展方法，如果为null则返回0
        /// </summary>
        /// <param name="thisValue">要处理的可空double值</param>
        /// <param name="保留小数位">保留的小数位数</param>
        /// <returns>四舍五入后的结果，如果输入为null则返回0</returns>
        public static double 四舍五入(this double? thisValue, int 保留小数位)
        {
            if (thisValue == null || !thisValue.HasValue)
                return 0;
            
            return Math.Round(thisValue.Value, 保留小数位, MidpointRounding.AwayFromZero);
        }

        /// <summary>
        /// 可空float类型的四舍五入扩展方法，如果为null则返回0
        /// </summary>
        /// <param name="thisValue">要处理的可空float值</param>
        /// <param name="保留小数位">保留的小数位数</param>
        /// <returns>四舍五入后的结果，如果输入为null则返回0</returns>
        public static float 四舍五入(this float? thisValue, int 保留小数位)
        {
            if (thisValue == null || !thisValue.HasValue)
                return 0f;
            
            return Math.Round(thisValue.Value.GetFloat(), 保留小数位, MidpointRounding.AwayFromZero).GetFloat();
        }
    }
}