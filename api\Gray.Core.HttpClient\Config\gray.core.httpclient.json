{
  "HttpClient": {
    "Enabled": true,
    "DefaultTimeout": 30,
    "MaxRetryAttempts": 3,
    "RetryDelaySeconds": 2,
    "EnableLogging": true,
    "EnableMetrics": true,
    "LogLevel": "Information",
    
    "Authentication": {
      "Enabled": true,
      "Type": "JWT",
      "TokenRefreshThresholdMinutes": 5,
      "AutoRefreshToken": true,
      "LoginEndpoint": "/api/Login/GetJwtToken3",
      "RefreshEndpoint": "/api/Login/RefreshToken"
    },

    // 多系统认证配置 - 新增功能
    "AuthenticationSystems": {
      "IOTSharp": {
        "Enabled": true,
        "Name": "IOTSharp物联网平台",
        "LoginEndpoint": "/api/Account/Login",
        "RefreshEndpoint": "/api/Account/RefreshToken",
        "Credentials": {
          "Username": "<EMAIL>",
          "Password": "C@ll0608"
        },
        "TokenMapping": {
          "AccessTokenField": "data.token.access_token",
          "RefreshTokenField": "data.token.refresh_token",
          "ExpiresInField": "data.token.expires_in",
          "TokenTypeField": "token_type"
        },
        "RequestFormat": {
          "Method": "POST",
          "ContentType": "application/json",
          "BodyTemplate": {
            "userName": "{Username}",
            "password": "{Password}"
          }
        },
        "AutoRefreshToken": true,
        "TokenRefreshThresholdMinutes": 30
      },
      "ExternalSystem1": {
        "Enabled": false,
        "Name": "外部系统1",
        "LoginEndpoint": "/api/auth/login",
        "RefreshEndpoint": "/api/auth/refresh",
        "Credentials": {
          "Username": "your_username",
          "Password": "your_password",
          "ClientId": "optional_client_id",
          "ClientSecret": "optional_client_secret"
        },
        "TokenMapping": {
          "AccessTokenField": "access_token",
          "RefreshTokenField": "refresh_token",
          "ExpiresInField": "expires_in",
          "TokenTypeField": "token_type"
        },
        "RequestFormat": {
          "Method": "POST",
          "ContentType": "application/json",
          "BodyTemplate": {
            "username": "{Username}",
            "password": "{Password}",
            "grant_type": "password"
          }
        },
        "AutoRefreshToken": true,
        "TokenRefreshThresholdMinutes": 5
      },
      "ExternalSystem2": {
        "Enabled": true,
        "Name": "外部系统2",
        "LoginEndpoint": "/oauth/token",
        "RefreshEndpoint": "/oauth/refresh",
        "Credentials": {
          "ClientId": "client_id_here",
          "ClientSecret": "client_secret_here"
        },
        "TokenMapping": {
          "AccessTokenField": "accessToken",
          "RefreshTokenField": "refreshToken",
          "ExpiresInField": "expiresIn"
        },
        "RequestFormat": {
          "Method": "POST",
          "ContentType": "application/x-www-form-urlencoded",
          "BodyTemplate": {
            "grant_type": "client_credentials",
            "client_id": "{ClientId}",
            "client_secret": "{ClientSecret}"
          }
        },
        "AutoRefreshToken": true,
        "TokenRefreshThresholdMinutes": 3
      }
    },

    "ApiEndpoints": {
      "Gateway": {
        "Name": "Gray.Core.Gateway",
        "BaseUrl": "http://localhost:9111",
        "Timeout": 30,
        "EnableRetry": true,
        "MaxRetryAttempts": 3,
        "EnableCircuitBreaker": true,
        "CircuitBreakerFailureThreshold": 5,
        "CircuitBreakerRecoveryTimeSeconds": 30,
        "Headers": {
          "User-Agent": "Gray.Core.HttpClient/1.0",
          "Accept": "application/json"
        }
      },
      "WebApi": {
        "Name": "Gray.WebApi",
        "BaseUrl": "https://localhost:7000",
        "Timeout": 30,
        "EnableRetry": true,
        "MaxRetryAttempts": 3,
        "EnableCircuitBreaker": true,
        "CircuitBreakerFailureThreshold": 5,
        "CircuitBreakerRecoveryTimeSeconds": 30,
        "Headers": {
          "User-Agent": "Gray.Core.HttpClient/1.0",
          "Accept": "application/json"
        }
      },
      "IOTSharp": {
        "Name": "IOTSharp物联网平台",
        "BaseUrl": "http://*********:5000",
        "Timeout": 30,
        "EnableRetry": true,
        "MaxRetryAttempts": 3,
        "EnableCircuitBreaker": true,
        "CircuitBreakerFailureThreshold": 5,
        "CircuitBreakerRecoveryTimeSeconds": 30,
        "AuthenticationSystem": "IOTSharp",
        "Headers": {
          "User-Agent": "Gray.Core.HttpClient/1.0",
          "Accept": "application/json"
        }
      },
      "ExternalSystem1": {
        "Name": "外部系统1",
        "BaseUrl": "https://external-system1.example.com",
        "Timeout": 30,
        "EnableRetry": true,
        "MaxRetryAttempts": 3,
        "EnableCircuitBreaker": true,
        "CircuitBreakerFailureThreshold": 5,
        "CircuitBreakerRecoveryTimeSeconds": 30,
        "AuthenticationSystem": "ExternalSystem1",
        "Headers": {
          "User-Agent": "Gray.Core.HttpClient/1.0",
          "Accept": "application/json"
        }
      },
      "ExternalSystem2": {
        "Name": "外部系统2",
        "BaseUrl": "https://external-system2.example.com",
        "Timeout": 30,
        "EnableRetry": true,
        "MaxRetryAttempts": 3,
        "EnableCircuitBreaker": true,
        "CircuitBreakerFailureThreshold": 5,
        "CircuitBreakerRecoveryTimeSeconds": 30,
        "AuthenticationSystem": "ExternalSystem2",
        "Headers": {
          "User-Agent": "Gray.Core.HttpClient/1.0",
          "Accept": "application/json"
        }
      }
    },

    "Polly": {
      "Retry": {
        "Enabled": true,
        "MaxAttempts": 3,
        "DelaySeconds": 2,
        "BackoffMultiplier": 2.0,
        "MaxDelaySeconds": 30,
        "RetryOnHttpStatusCodes": [ 408, 429, 500, 502, 503, 504 ]
      },
      "CircuitBreaker": {
        "Enabled": true,
        "FailureThreshold": 5,
        "SamplingDurationSeconds": 60,
        "MinimumThroughput": 10,
        "DurationOfBreakSeconds": 30
      },
      "Timeout": {
        "Enabled": true,
        "TimeoutSeconds": 30
      }
    },

    "Logging": {
      "LogRequests": true,
      "LogResponses": true,
      "LogHeaders": false,
      "LogBody": false,
      "LogSensitiveData": false,
      "MaxBodyLogLength": 1000,
      "SensitiveHeaders": [ "Authorization", "Cookie", "Set-Cookie" ]
    },

    "Cache": {
      "Enabled": false,
      "DefaultExpirationMinutes": 5,
      "CacheKeyPrefix": "HttpClient:",
      "CacheableHttpMethods": [ "GET" ],
      "CacheableStatusCodes": [ 200, 201, 202 ]
    }
  },

  "Note": "HTTP客户端配置 - 支持多个API端点、认证、重试、熔断、日志等功能"
}
