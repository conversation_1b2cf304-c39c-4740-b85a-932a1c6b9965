
using System;
using Gray.Core.DBManager;
using Gray.Core.EFDbContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Gray.Core.UnitOfWorkMange
{
    public class DbContextFactory : IDbContextFactory
    {
        private readonly IServiceProvider _serviceProvider;
        
        public DbContextFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        public TContext CreateDbContext<TContext>() where TContext : BaseDbContext<TContext>
        {
            try
            {
                // 使用泛型 DbContextOptions，提供类型安全
                var optionsBuilder = new DbContextOptionsBuilder<TContext>();
                var config = DBServerProvider.GetConnConfig(typeof(TContext).FullName);

                if (config == null)
                {
                    throw new InvalidOperationException($"未找到数据库配置信息: {typeof(TContext).FullName}");
                }

                DbTypeConfigurer.ConfigureDbType(
                    optionsBuilder,
                    config.Connection,
                    config.DbType,
                    config.Version,
                    enableDetailedLogging: false // 生产环境不启用详细日志
                );

                return ActivatorUtilities.CreateInstance<TContext>(
                    _serviceProvider,
                    optionsBuilder.Options
                );
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"创建数据库上下文失败: {typeof(TContext).Name}", ex);
            }
        }
    }
}
