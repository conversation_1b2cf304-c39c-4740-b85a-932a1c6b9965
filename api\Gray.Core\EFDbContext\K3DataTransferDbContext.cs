﻿using Gray.Core.DBManager;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.SystemModels;
using Microsoft.EntityFrameworkCore;
using System;

namespace Gray.Core.EFDbContext
{
    public class K3DataTransferDbContext : BaseDbContext<K3DataTransferDbContext>, IDependency
    {
        protected override string ConnectionString
        {
            get
            {
                return DBServerProvider.GetConnection(this.GetType().Name);
            }
        }

        public K3DataTransferDbContext() : base()
        {
        }

        public K3DataTransferDbContext(DbContextOptions<K3DataTransferDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.UseDbTypeFromConfig(optionsBuilder, this.GetType().Name);
            //默认禁用实体跟踪
            optionsBuilder = optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            optionsBuilder = optionsBuilder.UseSqlServer(o => o.UseCompatibilityLevel(120));
            base.OnConfiguring(optionsBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
        }

        protected override Type GetBaseEntityType()
        {
            // 默认返回null，由派生类实现
            return typeof(K3DataTransferEntity);
        }
    }
}