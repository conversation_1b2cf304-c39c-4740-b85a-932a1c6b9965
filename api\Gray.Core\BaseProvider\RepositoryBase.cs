﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Threading.Tasks;
using System.Transactions;
using Gray.Core.Configuration;
using Gray.Core.Dapper;
using Gray.Core.DBManager;
using Gray.Core.EFDbContext;
using Gray.Core.Enums;
using Gray.Core.Extensions;
using Gray.Core.Utilities;
using Gray.Entity;
using Gray.Entity.SystemModels;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;
using Gray.Core.UnitOfWorkMange;
using System.Threading;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.DependencyInjection;
using System.Text;
using Gray.Core.Services;

namespace Gray.Core.BaseProvider
{
    public abstract class RepositoryBase<TEntity> :IRepository<TEntity> where TEntity : BaseEntity
    {
        protected readonly IUnitOfWork _unitOfWork;
        private readonly SemaphoreSlim _asyncLock = new SemaphoreSlim(1, 1);
        private readonly string _dbContextTypeName;

        public RepositoryBase(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
            // 获取实体对应的DbContext类型
            _dbContextTypeName = typeof(TEntity).GetTypeCustomValue<EntityAttribute>(x => x.DBServer) ?? typeof(SysDbContext).Name;
        }

        public BaseDbContext DbContext => GetDbContext();

        private BaseDbContext GetDbContext()
        {
            try
            {
                // 使用UnitOfWork的GetDbContextByEntityType方法
                // 这个方法已经处理了EntityAttribute解析和类型查找逻辑
                return _unitOfWork.GetDbContextByEntityType<TEntity>();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"通过UnitOfWork获取DbContext失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查类型是否继承自泛型 BaseDbContext&lt;T&gt;
        /// </summary>
        private static bool IsGenericBaseDbContext(Type type)
        {
            var baseType = type.BaseType;
            while (baseType != null)
            {
                if (baseType.IsGenericType &&
                    baseType.GetGenericTypeDefinition() == typeof(BaseDbContext<>))
                {
                    return true;
                }
                baseType = baseType.BaseType;
            }
            return false;
        }

        // 移除事务管理方法 - 这些职责现在由TransactionService承担

        public IUnitOfWork UnitOfWork => _unitOfWork;

        public RepositoryBase()
        {
        }
        // public RepositoryBase(BaseDbContext dbContext)
        // {
        //     this.DefaultDbContext = dbContext ?? throw new Exception("dbContext未实例化。");
        // }

        private BaseDbContext DefaultDbContext { get=>DbContext;}
        private BaseDbContext EFContext
        {
            get
            {
                //  DBServerProvider.GetDbContextConnection<TEntity>(BaseDbContext);
                return DefaultDbContext;
            }
        }

      
        private DbSet<TEntity> DBSet
        {
            get { return EFContext.Set<TEntity>(); }
        }
        public ISqlDapper DapperContext
        {
            get { return DBServerProvider.GetSqlDapper<TEntity>(); }
        }

     

        // 移除事务管理方法 DbContextBeginTransaction - 现在由TransactionService负责
        // 移除异步事务管理方法 DbContextBeginTransactionAsync - 现在由TransactionService负责
        public virtual bool Exists(Expression<Func<TEntity, bool>> predicate, bool filterDeleted = true)
        {
            // 使用同步方法避免AsyncLocal问题
            var context = DbContext;
            return context.Set<TEntity>(filterDeleted).Any(predicate);
        }

        public virtual Task<bool> ExistsAsync(Expression<Func<TEntity, bool>> predicate, bool filterDeleted = true)
        {
            return ExecuteWithContextAsync<bool>(async context => 
            {
                return await context.Set<TEntity>(filterDeleted).AnyAsync(predicate);
            });
        }
        /// <summary>
        /// 查询字段不为null或者为空
        /// </summary>
        /// <param name="field">x=>new {x.字段}</param>
        /// <param name="value">查询的类</param>
        /// <param name="linqExpression">查询类型</param>
        /// <returns></returns>
        public virtual IQueryable<TEntity> WhereIF([NotNull] Expression<Func<TEntity, object>> field, string value, LinqExpressionType linqExpression = LinqExpressionType.Equal)
        {
            // 这里不使用异步方法，因为返回的是 IQueryable，需要延迟执行
            // 但我们可以确保使用 DbContext 是通过 UnitOfWork 获取的
            var context = DbContext;
            return context.Set<TEntity>().WhereNotEmpty(field, value, linqExpression);
        }

        public virtual IQueryable<TEntity> WhereIF(bool checkCondition, Expression<Func<TEntity, bool>> predicate)
        {
            var context = DbContext;
            if (checkCondition)
            {
                return context.Set<TEntity>().Where(predicate);
            }
            return context.Set<TEntity>();
        }

        public virtual IQueryable<T> WhereIF<T>(bool checkCondition, Expression<Func<T, bool>> predicate) where T : class
        {
            var context = DbContext;
            if (checkCondition)
            {
                return context.Set<T>().Where(predicate);
            }
            return context.Set<T>();
        }


        public virtual List<TFind> Find<TFind>(Expression<Func<TFind, bool>> predicate, bool filterDeleted = true) where TFind : class
        {
            // 使用同步方法避免AsyncLocal问题
            var context = DbContext;
            return context.Set<TFind>(filterDeleted).Where(predicate).ToList();
        }

        public virtual Task<TFind> FindAsyncFirst<TFind>(Expression<Func<TFind, bool>> predicate, bool filterDeleted = true) where TFind : class
        {
            return ExecuteWithContextAsync<TFind>(async context => 
            {
                return await context.Set<TFind>(filterDeleted).Where(predicate).FirstOrDefaultAsync();
            });
        }

        public virtual Task<List<TFind>> FindAsync<TFind>(Expression<Func<TFind, bool>> predicate, bool filterDeleted = true) where TFind : class
        {
            return ExecuteWithContextAsync<List<TFind>>(async context => 
            {
                return await context.Set<TFind>(filterDeleted).Where(predicate).ToListAsync();
            });
        }

        public virtual Task<List<TEntity>> FindAsync(Expression<Func<TEntity, bool>> predicate, bool filterDeleted = true)
        {
            return ExecuteWithContextAsync<List<TEntity>>(async context => 
            {
                return await context.Set<TEntity>(filterDeleted).Where(predicate).ToListAsync();
            });
        }

        public virtual Task<TEntity> FindAsyncFirst(Expression<Func<TEntity, bool>> predicate, bool filterDeleted = true)
        {
            return ExecuteWithContextAsync<TEntity>(async context => 
            {
                return await context.Set<TEntity>(filterDeleted).Where(predicate).FirstOrDefaultAsync();
            });
        }

        public virtual Task<List<T>> FindAsync<T>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, T>> selector, bool filterDeleted = true)
        {
            return ExecuteWithContextAsync<List<T>>(async context => 
            {
                return await context.Set<TEntity>(filterDeleted).Where(predicate).Select(selector).ToListAsync();
            });
        }

        public virtual Task<T> FindFirstAsync<T>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, T>> selector, bool filterDeleted = true)
        {
            return ExecuteWithContextAsync<T>(async context => 
            {
                return await context.Set<TEntity>(filterDeleted).Where(predicate).Select(selector).FirstOrDefaultAsync();
            });
        }

        private IQueryable<TFind> FindAsIQueryable<TFind>(Expression<Func<TFind, bool>> predicate, bool filterDeleted = true) where TFind : class
        {
            var context = DbContext;
            return context.Set<TFind>(filterDeleted).Where(predicate);
        }


        public virtual List<T> Find<T>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, T>> selector, bool filterDeleted = true)
        {
            // 使用同步方法避免AsyncLocal问题
            var context = DbContext;
            return context.Set<TEntity>(filterDeleted).Where(predicate).Select(selector).ToList();
        }
        /// <summary>
        /// 单表查询
        /// </summary>
        /// <param name="predicate"></param>
        /// <returns></returns>
        public virtual List<TEntity> Find(Expression<Func<TEntity, bool>> predicate, bool filterDeleted = true)
        {
            return FindAsIQueryable(predicate, filterDeleted).ToList();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="predicate"></param>
        /// <param name=""></param>
        /// <param name="orderBy">排序字段</param>
        /// <returns></returns>
        public virtual TEntity FindFirst(Expression<Func<TEntity, bool>> predicate, bool filterDeleted = true)
        {
            // 使用同步方法避免AsyncLocal问题
            var context = DbContext;
            return context.Set<TEntity>(filterDeleted).Where(predicate).FirstOrDefault();
        }


        public IQueryable<TEntity> FindAsIQueryable(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, Dictionary<object, QueryOrderBy>>> orderBy = null)
        {
            if (orderBy != null)
                return DbContext.Set<TEntity>().Where(predicate).GetIQueryableOrderBy(orderBy.GetExpressionToDic());
            return DbContext.Set<TEntity>().Where(predicate);
        }

        public IIncludableQueryable<TEntity, TProperty> Include<TProperty>(Expression<Func<TEntity, TProperty>> incluedProperty)
        {
            return DbContext.Set<TEntity>().Include(incluedProperty);
        }

        /// <summary>
        /// 通过条件查询返回指定列的数据(将TEntity映射到匿名或实体T)
        ///var result = Sys_UserRepository.GetInstance.Find(x => x.UserName == loginInfo.userName, p => new { uname = p.UserName });
        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TKey"></typeparam>
        /// <param name="pageIndex"></param>
        /// <param name="pagesize"></param>
        /// <param name="rowcount"></param>
        /// <param name="predicate">查询条件</param>
        /// <param name="orderBySelector">多个排序字段key为字段，value为升序/降序</param>
        /// <returns></returns>
        public virtual IQueryable<TFind> IQueryablePage<TFind>(int pageIndex, int pagesize, out int rowcount, Expression<Func<TFind, bool>> predicate, Expression<Func<TEntity, Dictionary<object, QueryOrderBy>>> orderBy, bool returnRowCount = true) where TFind : class
        {
            pageIndex = pageIndex <= 0 ? 1 : pageIndex;
            pagesize = pagesize <= 0 ? 10 : pagesize;
            if (predicate == null)
            {
                predicate = x => 1 == 1;
            }
            var _db = DbContext.Set<TFind>();
            rowcount = returnRowCount ? _db.Count(predicate) : 0;
            return DbContext.Set<TFind>().Where(predicate)
                .GetIQueryableOrderBy(orderBy.GetExpressionToDic())
                .Skip((pageIndex - 1) * pagesize)
                .Take(pagesize);
        }
        public virtual async Task<(IQueryable<TEntity> data, int total)> IQueryablePageAsync(
            IQueryable<TEntity> queryable,
            int pageIndex,
            int pageSize,
            Dictionary<string, QueryOrderBy> orderBy = null,
            bool returnRowCount = true)
        {
            try
            {
                int total = 0;
                if (returnRowCount)
                {
                    total = await queryable.CountAsync();
                }

                // 应用排序
                if (orderBy?.Count > 0)
                {
                    queryable = queryable.GetIQueryableOrderBy(orderBy);
                }

                // 如果 pageSize = 0，返回所有数据
                if (pageSize == 0)
                {
                    return (queryable.AsNoTracking(), total);
                }

                // 否则执行分页
                pageIndex = Math.Max(1, pageIndex);
                pageSize = Math.Max(10, pageSize);

                var result = queryable
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .AsNoTracking();

                return (result, total);
            }
            catch (Exception ex)
            {
                throw new Exception($"分页查询失败: {ex.Message}", ex);
            }
        }

        // 同样修改泛型版本
        public virtual async Task<(IQueryable<TFind> data, int total)> IQueryablePageAsync<TFind>(
            int pageIndex,
            int pageSize,
            Expression<Func<TFind, bool>> predicate,
            Expression<Func<TEntity, Dictionary<object, QueryOrderBy>>> orderBy,
            bool returnRowCount = true) where TFind : class
        {
            try
            {
                var dbSet = DbContext.Set<TFind>();
                var query = dbSet.Where(predicate ?? (x => true));

                int total = 0;
                if (returnRowCount)
                {
                    total = await query.CountAsync();
                }

                // 应用排序
                if (orderBy != null)
                {
                    query = query.GetIQueryableOrderBy(orderBy.GetExpressionToDic());
                }

                // 如果 pageSize = -1，返回所有数据
                if (pageSize == 0)
                {
                    return (query.AsNoTracking(), total);
                }

                // 否则执行分页
                pageIndex = Math.Max(1, pageIndex);
                pageSize = Math.Max(10, pageSize);

                var result = query
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .AsNoTracking();

                return (result, total);
            }
            catch (Exception ex)
            {
                throw new Exception($"分页查询失败: {ex.Message}", ex);
            }
        }
        /// <summary>
        /// 分页排序
        /// </summary>
        /// <param name="queryable"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pagesize"></param>
        /// <param name="rowcount"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        public virtual IQueryable<TEntity> IQueryablePage(IQueryable<TEntity> queryable, int pageIndex, int pagesize, out int rowcount, Dictionary<string, QueryOrderBy> orderBy, bool returnRowCount = true)
        {
            pageIndex = pageIndex <= 0 ? 1 : pageIndex;
            pagesize = pagesize <= 0 ? 10 : pagesize;
            rowcount = returnRowCount ? queryable.Count() : 0;
            return queryable.GetIQueryableOrderBy<TEntity>(orderBy)
                .Skip((pageIndex - 1) * pagesize)
                .Take(pagesize);
        }



        /// <summary>
        /// 更新表数据
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="saveChanges">是否保存</param>
        /// <param name="properties">格式 Expression<Func<entityt, object>> expTree = x => new { x.字段1, x.字段2 };</param>
        public virtual int Update(TEntity entity, Expression<Func<TEntity, object>> properties, bool saveChanges = false)
        {
            return Update<TEntity>(entity, properties, saveChanges);
        }

        public virtual int Update<TSource>(TSource entity, Expression<Func<TSource, object>> properties, bool saveChanges = false) where TSource : class
        {
            return UpdateRange(new List<TSource>
            {
                entity
            }, properties, saveChanges);
        }


        public virtual int Update<TSource>(TSource entity, string[] properties, bool saveChanges = false) where TSource : class
        {
            return UpdateRange<TSource>(new List<TSource>() { entity }, properties, saveChanges);
        }
        public virtual int Update<TSource>(TSource entity, bool saveChanges = false) where TSource : class
        {
            return UpdateRange<TSource>(new List<TSource>() { entity }, new string[0], saveChanges);
        }
        public virtual int UpdateRange<TSource>(IEnumerable<TSource> entities, Expression<Func<TSource, object>> properties, bool saveChanges = false) where TSource : class
        {
            return UpdateRange<TSource>(entities, properties?.GetExpressionProperty(), saveChanges);
        }
        public virtual int UpdateRange<TSource>(IEnumerable<TSource> entities, bool saveChanges = false) where TSource : class
        {
            return UpdateRange<TSource>(entities, new string[0], saveChanges);
        }

        /// <summary>
        /// 更新表数据
        /// </summary>
        /// <param name="models"></param>
        /// <param name="properties">格式 Expression<Func<entityt, object>> expTree = x => new { x.字段1, x.字段2 };</param>
        public int UpdateRange<TSource>(IEnumerable<TSource> entities, string[] properties, bool saveChanges = false) where TSource : class
        {
            if (properties != null && properties.Length > 0)
            {
                PropertyInfo[] entityProperty = typeof(TSource).GetProperties()
                        .Where(x => x.GetCustomAttribute<NotMappedAttribute>() == null&&!(x.PropertyType.IsGenericType && x.PropertyType.GetGenericTypeDefinition() == typeof(List<>))).ToArray();
                string keyName = entityProperty.GetKeyName();
                if (properties.Contains(keyName))
                {
                    properties = properties.Where(x => x != keyName).ToArray();
                }
                properties = properties.Where(x => entityProperty.Select(s => s.Name).Contains(x)).ToArray();
            }
            foreach (TSource item in entities)
            {
                if (properties == null || properties.Length == 0)
                {
                    DbContext.Entry<TSource>(item).State = EntityState.Modified;
                    continue;
                }
                var entry = DbContext.Entry(item);
                properties.ToList().ForEach(x =>
                {
                    entry.Property(x).IsModified = true;
                });
            }
            if (!saveChanges) return 0;

            //2020.04.24增加更新时并行重试处理
            try
            {
                // Attempt to save changes to the database
                return DbContext.SaveChanges();
            }
            catch (DbUpdateConcurrencyException ex)
            {
                int affectedRows = 0;
                foreach (var entry in ex.Entries)
                {
                    var proposedValues = entry.CurrentValues;

                    var databaseValues = entry.GetDatabaseValues();
                    //databaseValues == null说明数据已被删除
                    if (databaseValues != null)
                    {
                        foreach (var property in properties == null
                            || properties.Length == 0 ? proposedValues.Properties
                            : proposedValues.Properties.Where(x => properties.Contains(x.Name)))
                        {
                            var proposedValue = proposedValues[property];
                            var databaseValue = databaseValues[property];
                        }
                        affectedRows++;
                        entry.OriginalValues.SetValues(databaseValues);
                    }
                }
                if (affectedRows == 0) return 0;

                return DbContext.SaveChanges();
            }
        }




        /// <summary>
        ///
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="updateDetail">是否修改明细</param>
        /// <param name="delNotExist">是否删除明细不存在的数据</param>
        /// <param name="updateMainFields">主表指定修改字段</param>
        /// <param name="updateDetailFields">明细指定修改字段</param>
        /// <param name="saveChange">是否保存</param>
        /// <returns></returns>
        public virtual WebResponseContent UpdateRange<Detail>(TEntity entity,
            bool updateDetail = false,
            bool delNotExist = false,
            Expression<Func<TEntity, object>> updateMainFields = null,
            Expression<Func<Detail, object>> updateDetailFields = null,
            bool saveChange = false) where Detail : class
        {
            WebResponseContent webResponse = new WebResponseContent();
            Update(entity, updateMainFields);
            string message = "";
            if (updateDetail)
            {
                PropertyInfo[] properties = typeof(TEntity).GetProperties();
                PropertyInfo detail = properties.Where(x => x.PropertyType.Name == "List`1").ToList().FirstOrDefault();
                if (detail != null)
                {
                    PropertyInfo key = properties.GetKeyProperty();
                    object obj = detail.GetValue(entity);
                    Type detailType = typeof(TEntity).GetCustomAttribute<EntityAttribute>().DetailTable[0];
                    message = UpdateDetail<Detail>(obj as List<Detail>, key.Name, key.GetValue(entity), updateDetailFields, delNotExist);
                }
            }
            if (!saveChange) return webResponse.OK();

            DbContext.SaveChanges();
            return webResponse.OK("修改成功,明细" + message, entity);
        }
        private string UpdateDetail<TDetail>(List<TDetail> list,
            string keyName,
            object keyValue,
            Expression<Func<TDetail, object>> updateDetailFields = null,
            bool delNotExist = false) where TDetail : class
        {
            if (list == null) return "";
            PropertyInfo property = typeof(TDetail).GetKeyProperty();
            string detailKeyName = property.Name;
            DbSet<TDetail> details = DbContext.Set<TDetail>();
            Expression<Func<TDetail, object>> selectExpression = detailKeyName.GetExpression<TDetail, object>();
            Expression<Func<TDetail, bool>> whereExpression = keyName.CreateExpression<TDetail>(keyValue, LinqExpressionType.Equal);

            List<object> detailKeys = details.Where(whereExpression).Select(selectExpression).ToList();

            //获取主键默认值
            string keyDefaultVal = property.PropertyType
                .Assembly
                .CreateInstance(property.PropertyType.FullName).ToString();
            int addCount = 0;
            int editCount = 0;
            int delCount = 0;
            PropertyInfo mainKeyProperty = typeof(TDetail).GetProperty(keyName);
            List<object> keys = new List<object>();
            list.ForEach(x =>
            {
                var set = DbContext.Set<TDetail>();
                object val = property.GetValue(x);
                //主键是默认值的为新增的数据
                if (val.ToString() == keyDefaultVal)
                {
                    x.SetCreateDefaultVal();
                    //设置主表的值，也可以不设置
                    mainKeyProperty.SetValue(x, keyValue);
                    details.Add(x);
                    addCount++;
                }
                else//修改的数据
                {
                    //获取所有修改的key,如果从数据库查来的key,不在修改中的key，则为删除的数据
                    keys.Add(val);
                    x.SetModifyDefaultVal();
                    Update<TDetail>(x, updateDetailFields);
                    //  repository.DbContext.Entry<TDetail>(x).State = EntityState.Modified;
                    editCount++;
                }
            });
            //删除
            if (delNotExist)
            {
                detailKeys.Where(x => !keys.Contains(x)).ToList().ForEach(d =>
                {
                    delCount++;
                    TDetail detail = Activator.CreateInstance<TDetail>();
                    property.SetValue(detail, d);
                    DbContext.Entry<TDetail>(detail).State = EntityState.Deleted;
                    for (int i = 0; i < list.Count(); i++)
                    {
                        if (property.GetValue(list[i]) == d)
                        {
                            list.RemoveAt(i);
                        }
                    }
                });
            }
            return $"修改[{editCount}]条,新增[{addCount}]条,删除[{delCount}]条";
        }

        public virtual void Delete(TEntity model, bool saveChanges)
        {
            DBSet.Remove(model);
            if (saveChanges)
            {
                DbContext.SaveChanges();
            }
        }
        /// <summary>
        /// 通过主键批量删除
        /// </summary>
        /// <param name="keys">主键key</param>
        /// <param name="delList">是否连明细一起删除</param>
        /// <returns></returns>
        public virtual int DeleteWithKeys(object[] keys, bool saveChange = false)
        {
            var keyPro = typeof(TEntity).GetKeyProperty();
            foreach (var key in keys.Distinct())
            {
                TEntity entity = Activator.CreateInstance<TEntity>();
                keyPro.SetValue(entity, key.ChangeType(keyPro.PropertyType));
                DbContext.Entry<TEntity>(entity).State = EntityState.Deleted;
            }
            if (saveChange)
            {
                DbContext.SaveChanges();
            }
            return keys.Length;
        }


        public virtual int Delete([NotNull] Expression<Func<TEntity, bool>> wheres, bool saveChange = false)
        {
            return Delete<TEntity>(wheres, saveChange);
        }
        public virtual int Delete<T>([NotNull] Expression<Func<T, bool>> wheres, bool saveChange = false) where T : class
        {
            var keyProperty = typeof(T).GetKeyProperty();
            string keyName = typeof(T).GetKeyProperty().Name;
            var expression = keyName.GetExpression<T, object>();
            var ids = DbContext.Set<T>().Where(wheres).Select(expression).ToList();
            List<T> list = new List<T>();
            foreach (var id in ids)
            {
                T entity = Activator.CreateInstance<T>();
                keyProperty.SetValue(entity, id);
                list.Add(entity);
            }
            DbContext.RemoveRange(list);
            if (saveChange)
            {
                return DbContext.SaveChanges();
            }
            return 0;
        }

        public virtual Task AddAsync(TEntity entities)
        {
            return DBSet.AddRangeAsync(entities);
        }

        public virtual Task AddRangeAsync(IEnumerable<TEntity> entities)
        {
            return DBSet.AddRangeAsync(entities);
        }

        public virtual void Add(TEntity entities, bool saveChanges = false)
        {
            try 
            {
                AddRange(new List<TEntity>() { entities }, saveChanges);
            }
            catch (Exception ex)
            {
                var errorMessage = new StringBuilder();
                errorMessage.AppendLine($"添加实体时发生错误: {ex.Message}");
                if (ex.InnerException != null)
                {
                    errorMessage.AppendLine($"内部错误: {ex.InnerException.Message}");
                }
                Logger.Error(LoggerType.Add, "添加实体失败", errorMessage.ToString(), ex.ToString());
                throw new Exception(errorMessage.ToString(), ex);
            }
        }
        public virtual void Add<T>(T entity, bool saveChanges = false) where T: class
        {
            try 
            {
                AddRange<T>(new List<T>() { entity }, saveChanges);
            }
            catch (Exception ex)
            {
                var errorMessage = new StringBuilder();
                errorMessage.AppendLine($"添加实体时发生错误: {ex.Message}");
                if (ex.InnerException != null)
                {
                    errorMessage.AppendLine($"内部错误: {ex.InnerException.Message}");
                }
                Logger.Error(LoggerType.Add, "添加实体失败", errorMessage.ToString(), ex.ToString());
                throw new Exception(errorMessage.ToString(), ex);
            }
        }
        public virtual void AddRange(IEnumerable<TEntity> entities, bool saveChanges = false)
        {
            try 
            {
                AddRange<TEntity>(entities, saveChanges);
            }
            catch (Exception ex)
            {
                var errorMessage = new StringBuilder();
                errorMessage.AppendLine($"批量添加实体时发生错误: {ex.Message}");
                if (ex.InnerException != null)
                {
                    errorMessage.AppendLine($"内部错误: {ex.InnerException.Message}");
                }
                Logger.Error(LoggerType.Add, "批量添加实体失败", errorMessage.ToString(), ex.ToString());
                throw new Exception(errorMessage.ToString(), ex);
            }
        }

        public virtual void AddRange<T>(IEnumerable<T> entities, bool saveChanges = false)
            where T : class
        {
            try
            {
                if (entities.Count() == 0)
                {
                    return;
                }

                if (AppSetting.UseSnow)
                {
                    PropertyInfo keyPro = typeof(T).GetKeyProperty();
                    if (keyPro.PropertyType == typeof(long))
                    {
                        //生成雪花id
                        var idWorker = new IdWorker();
                        foreach (var item in entities)
                        {
                            keyPro.SetValue(item, idWorker.NextId());
                        }
                    }
                }

                DbContext.Set<T>().AddRange(entities);
                
                if (saveChanges)
                {
                    try 
                    {
                        DbContext.SaveChanges();
                    }
                    catch (DbUpdateException ex)
                    {
                        var errorMessage = new StringBuilder();
                        errorMessage.AppendLine($"保存更改时发生错误: {ex.Message}");
                        if (ex.InnerException != null)
                        {
                            errorMessage.AppendLine($"内部错误: {ex.InnerException.Message}");
                            if (ex.InnerException.InnerException != null)
                            {
                                errorMessage.AppendLine($"数据库错误: {ex.InnerException.InnerException.Message}");
                            }
                        }
                        Logger.Error(LoggerType.Add, "保存更改失败", errorMessage.ToString(), ex.ToString());
                        throw new Exception(errorMessage.ToString(), ex);
                    }
                }
            }
            catch (Exception ex)
            {
                var errorMessage = new StringBuilder();
                errorMessage.AppendLine($"添加实体时发生错误: {ex.Message}");
                if (ex.InnerException != null)
                {
                    errorMessage.AppendLine($"内部错误: {ex.InnerException.Message}");
                }
                Logger.Error(LoggerType.Add, "添加实体失败", errorMessage.ToString(), ex.ToString());
                throw new Exception(errorMessage.ToString(), ex);
            }
        }


        public virtual int SaveChanges()
        {
            try
            {
                // 直接使用UnitOfWork的同步SaveChanges，避免GetAwaiter().GetResult()破坏AsyncLocal
                return _unitOfWork.SaveChanges();
            }
            catch (DbUpdateException ex)
            {
                var errorMessage = new StringBuilder();
                errorMessage.AppendLine($"保存更改时发生错误: {ex.Message}");
                var innerException = ex.InnerException;
                if (innerException != null)
                {
                    errorMessage.AppendLine($"内部异常: {innerException.Message}");
                    if (innerException.InnerException != null)
                    {
                        errorMessage.AppendLine($"数据库错误: {innerException.InnerException.Message}");
                    }
                }
                
                // 记录详细错误信息到日志
                Logger.Error(LoggerType.Add, "保存更改失败", errorMessage.ToString(), ex.ToString());
                throw new Exception(errorMessage.ToString(), ex);
            }
        }

        public virtual async Task<int> SaveChangesAsync()
        {
            try 
            {
                return await ExecuteWithContextAsync<int>(async context => 
                {
                    return await context.SaveChangesAsync();
                });
            }
            catch (DbUpdateException ex)
            {
                var errorMessage = new StringBuilder();
                errorMessage.AppendLine($"异步保存更改时发生错误: {ex.Message}");
                var innerException = ex.InnerException;
                if (innerException != null)
                {
                    errorMessage.AppendLine($"内部异常: {innerException.Message}");
                    if (innerException.InnerException != null)
                    {
                        errorMessage.AppendLine($"更深层异常: {innerException.InnerException.Message}");
                    }
                }
                
                // 记录详细错误信息到日志
                Logger.Error(LoggerType.Add, "异步保存更改失败", errorMessage.ToString(), ex.ToString());
                throw new Exception(errorMessage.ToString(), ex);
            }
        }

        public virtual int ExecuteSqlCommand(string sql, params SqlParameter[] sqlParameters)
        {
            return DbContext.Database.ExecuteSqlRaw(sql, sqlParameters);
        }

        public virtual List<TEntity> FromSql(string sql, params SqlParameter[] sqlParameters)
        {
            return DBSet.FromSqlRaw(sql, sqlParameters).ToList();
        }

        /// <summary>
        /// 执行sql
        /// 使用方式 FormattableString sql=$"select * from xx where name ={xx} and pwd={xx1} "，
        /// FromSqlInterpolated内部处理sql注入的问题，直接在{xx}写对应的值即可
        /// 注意：sql必须 select * 返回所有TEntity字段，
        /// </summary>
        /// <param name="formattableString"></param>
        /// <returns></returns>
        public virtual IQueryable<TEntity> FromSqlInterpolated([NotNull] FormattableString sql)
        {
            //DBSet.FromSqlInterpolated(sql).Select(x => new { x,xxx}).ToList();
            return DBSet.FromSqlInterpolated(sql);
        }

        /// <summary>
        /// 取消上下文跟踪
        /// </summary>
        /// <param name="entity"></param>
        public virtual void Detached(TEntity entity)
        {
            DbContext.Entry(entity).State = EntityState.Detached;
        }
        public virtual void DetachedRange(IEnumerable<TEntity> entities)
        {
            foreach (var entity in entities)
            {
                DbContext.Entry(entity).State = EntityState.Detached;
            }
        }

        public virtual async Task<T> ExecuteWithContextAsync<T>(Func<BaseDbContext, Task<T>> operation)
        {
            try
            {
                var dbContextType = Type.GetType(_dbContextTypeName);
                if (dbContextType == null)
                {
                    var assembly = typeof(SysDbContext).Assembly;
                    dbContextType = assembly.GetTypes()
                        .FirstOrDefault(t => t.Name == _dbContextTypeName && typeof(BaseDbContext).IsAssignableFrom(t));
                        
                    if (dbContextType == null)
                    {
                        throw new InvalidOperationException($"未找到DbContext类型: {_dbContextTypeName}");
                    }
                }

                // 使用反射调用泛型方法
                var method = typeof(IUnitOfWork).GetMethod(nameof(IUnitOfWork.ExecuteWithContextAsync));
                var genericMethod = method.MakeGenericMethod(dbContextType, typeof(T));
                
                // 创建委托
                var delegateType = typeof(Func<,>).MakeGenericType(dbContextType, typeof(Task<T>));
                var convertedDelegate = Delegate.CreateDelegate(delegateType, operation.Target, operation.Method);
                
                return await (Task<T>)genericMethod.Invoke(_unitOfWork, new object[] { convertedDelegate });
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"执行上下文操作失败: {ex.Message}", ex);
            }
        }

        // 添加缺失的接口方法实现
        public virtual Task<TEntity> FindFirstAsync(Expression<Func<TEntity, bool>> predicate, bool filterDeleted = true)
        {
            return ExecuteWithContextAsync<TEntity>(async context => 
            {
                return await context.Set<TEntity>(filterDeleted).Where(predicate).FirstOrDefaultAsync();
            });
        }

        public virtual bool Exists<TExists>(Expression<Func<TExists, bool>> predicate, bool filterDeleted = true) where TExists : class
        {
            // 使用同步方法避免AsyncLocal问题
            var context = DbContext;
            return context.Set<TExists>(filterDeleted).Any(predicate);
        }

        public virtual Task<bool> ExistsAsync<TExists>(Expression<Func<TExists, bool>> predicate, bool filterDeleted = true) where TExists : class
        {
            return ExecuteWithContextAsync<bool>(async context => 
            {
                return await context.Set<TExists>(filterDeleted).AnyAsync(predicate);
            });
        }

   
        public IQueryable<TEntity> FindAsIQueryable(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, Dictionary<object, QueryOrderBy>>> orderBy = null, bool filterDeleted = true)
        {
            if (orderBy != null)
                return DbContext.Set<TEntity>(filterDeleted).Where(predicate).GetIQueryableOrderBy(orderBy.GetExpressionToDic());
            return DbContext.Set<TEntity>(filterDeleted).Where(predicate);
        }
    }
}

