/*
Tel:15662337743
 *所有业务编写全部应在Partial文件夹下App_NewsService与IApp_NewsService中编写
 */
using Gray.AppManager.IRepositories;
using Gray.AppManager.IServices;
using Gray.Core.BaseProvider;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.DomainModels;

namespace Gray.AppManager.Services
{
    public partial class App_NewsService : ServiceBase<App_News, IApp_NewsRepository>, IApp_NewsService, IDependency
    {
        public App_NewsService(IApp_NewsRepository repository)
             : base(repository) 
        { 
           Init(repository);
        }
        public static IApp_NewsService Instance
        {
           get { return AutofacContainerModule.GetService<IApp_NewsService>(); }
        }
    }
}
