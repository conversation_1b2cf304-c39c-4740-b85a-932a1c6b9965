﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using Gray.Core.Enums;
using Gray.Core.Extensions;

namespace Gray.Core.WorkFlow
{
    public static class WorkFlowFilter
    {
        public static bool CheckFilter<T>(this List<FieldFilter> filters, List<T> entities, object where) where T : class
        {
            try
            {
                if (where != null)
                {
                    return entities.Any(((Func<T, bool>)where));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"流程表达式解析异常:" + ex.Message + ex.InnerException);
                if (filters!=null)
                {
                    //Nullable object must have a value.字段为null时
                    Func<T, bool> expression = filters.Create<T>().Compile();
                    return entities.AsQueryable().Any(expression);
                }
            }
            return false;
        }
        public static Expression<Func<T, bool>> Create<T>(this List<FieldFilter> filters) where T : class
        {
            if (filters == null || !filters.Any() || !filters.Any(x => !string.IsNullOrEmpty(x.Value)))
            {
                return x => true;
            }

            var parameter = Expression.Parameter(typeof(T), "x");
            Expression bodyExpression = null;

            foreach (var filter in filters.Where(f => !string.IsNullOrEmpty(f.Field) && !string.IsNullOrEmpty(f.Value)))
            {
                var property = typeof(T).GetProperty(filter.Field);
                if (property == null)
                {
                    throw new Exception($"属性未找到: {filter.Field}");
                }

                var propertyExp = Expression.Property(parameter, property);
                var propertyType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;

                try
                {
                    object convertedValue;
                    // 特殊处理字符串类型
                    if (propertyType == typeof(string))
                    {
                        convertedValue = filter.Value.Trim();
                    }
                    // 处理Guid类型
                    else if (propertyType == typeof(Guid))
                    {
                        convertedValue = Guid.Parse(filter.Value.Trim());
                    }
                    // 处理其他类型
                    else
                    {
                        convertedValue = Convert.ChangeType(filter.Value.Trim(), propertyType);
                    }

                    var constantExp = Expression.Constant(convertedValue, property.PropertyType);
                    Expression comparison;

                    switch (filter.FilterType?.ToLower())
                    {
                        case "!=":
                            comparison = Expression.NotEqual(propertyExp, constantExp);
                            break;
                        case ">":
                            comparison = Expression.GreaterThan(propertyExp, constantExp);
                            break;
                        case ">=":
                            comparison = Expression.GreaterThanOrEqual(propertyExp, constantExp);
                            break;
                        case "<":
                            comparison = Expression.LessThan(propertyExp, constantExp);
                            break;
                        case "<=":
                            comparison = Expression.LessThanOrEqual(propertyExp, constantExp);
                            break;
                        case "like":
                            if (propertyType != typeof(string))
                                throw new Exception($"LIKE操作符只能用于字符串类型: {filter.Field}");
                            comparison = Expression.Call(propertyExp,
                                typeof(string).GetMethod("Contains", new[] { typeof(string) }),
                                constantExp);
                            break;
                        case "in":
                            // 处理 IN 操作符，支持多个值用逗号分隔
                            var values = filter.Value.Split(',').Select(v => v.Trim()).ToArray();
                            var convertedValues = new List<object>();

                            foreach (var value in values)
                            {
                                if (propertyType == typeof(string))
                                {
                                    convertedValues.Add(value);
                                }
                                else if (propertyType == typeof(Guid))
                                {
                                    convertedValues.Add(Guid.Parse(value));
                                }
                                else
                                {
                                    convertedValues.Add(Convert.ChangeType(value, propertyType));
                                }
                            }

                            // 创建包含所有值的数组常量
                            var arrayType = propertyType.MakeArrayType();
                            var valuesArray = Array.CreateInstance(propertyType, convertedValues.Count);
                            for (int i = 0; i < convertedValues.Count; i++)
                            {
                                valuesArray.SetValue(convertedValues[i], i);
                            }
                            var arrayConstant = Expression.Constant(valuesArray, arrayType);

                            // 创建 Contains 方法调用
                            var containsMethod = typeof(Enumerable).GetMethods()
                                .Where(m => m.Name == "Contains" && m.GetParameters().Length == 2)
                                .Single()
                                .MakeGenericMethod(propertyType);

                            comparison = Expression.Call(null, containsMethod, arrayConstant, propertyExp);
                            break;
                        default:
                            comparison = Expression.Equal(propertyExp, constantExp);
                            break;
                    }

                    bodyExpression = bodyExpression == null ?
                        comparison :
                        Expression.AndAlso(bodyExpression, comparison);
                }
                catch (Exception ex)
                {
                    throw new Exception($"构建表达式失败: 字段={filter.Field}, 值={filter.Value}, 类型={propertyType}, 错误={ex.Message}");
                }
            }

            return Expression.Lambda<Func<T, bool>>(bodyExpression ?? Expression.Constant(true), parameter);
        }
    }
}
