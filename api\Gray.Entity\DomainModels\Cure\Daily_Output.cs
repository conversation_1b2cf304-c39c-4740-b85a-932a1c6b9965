/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "硫化实时条码表",TableName = "Daily_Output",DBServer = "JL_CureDbContext")]
    public partial class Daily_Output:JL_CureEntity
    {
        /// <summary>
       ///条码时间
       /// </summary>
       [Display(Name ="条码时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? time { get; set; }

       /// <summary>
       ///数量
       /// </summary>
       [Display(Name ="数量")]
       [MaxLength(50)]
       [Column(TypeName="nvarchar(50)")]
       [Editable(true)]
       public string count { get; set; }

       /// <summary>
       ///胎号
       /// </summary>
       [Display(Name ="胎号")]
       [MaxLength(50)]
       [Column(TypeName="nvarchar(50)")]
       [Editable(true)]
       public string tyerID { get; set; }

       /// <summary>
       ///硫化规格
       /// </summary>
       [Display(Name ="硫化规格")]
       [MaxLength(50)]
       [Column(TypeName="nvarchar(50)")]
       [Editable(true)]
       public string tyerStyle { get; set; }

       /// <summary>
       ///操作员
       /// </summary>
       [Display(Name ="操作员")]
       [MaxLength(50)]
       [Column(TypeName="nvarchar(50)")]
       [Editable(true)]
       public string @operator { get; set; }

       /// <summary>
       ///模壳
       /// </summary>
       [Display(Name ="模壳")]
       [MaxLength(50)]
       [Column(TypeName="nvarchar(50)")]
       [Editable(true)]
       public string mouldShell { get; set; }

       /// <summary>
       ///花纹
       /// </summary>
       [Display(Name ="花纹")]
       [MaxLength(50)]
       [Column(TypeName="nvarchar(50)")]
       [Editable(true)]
       public string pattern { get; set; }

       /// <summary>
       ///侧板
       /// </summary>
       [Display(Name ="侧板")]
       [MaxLength(50)]
       [Column(TypeName="nvarchar(50)")]
       [Editable(true)]
       public string sidePanel { get; set; }

       /// <summary>
       ///轮胎代码
       /// </summary>
       [Display(Name ="轮胎代码")]
       [MaxLength(50)]
       [Column(TypeName="nvarchar(50)")]
       [Editable(true)]
       public string TyreCode { get; set; }

       /// <summary>
       ///IP地址
       /// </summary>
       [Display(Name ="IP地址")]
       [MaxLength(50)]
       [Column(TypeName="nvarchar(50)")]
       [Editable(true)]
       public string IP { get; set; }

       /// <summary>
       ///自增Id
       /// </summary>
       [Key]
       [Display(Name ="自增Id")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int id { get; set; }

       
    }
}