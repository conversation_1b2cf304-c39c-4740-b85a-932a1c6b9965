using Gray.Core.GrayHttpClient.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;

namespace Gray.Core.GrayHttpClient.Services
{
    /// <summary>
    /// HTTP客户端管理器实现
    /// 遵循Gray.Core的服务实现模式
    /// </summary>
    public class HttpClientManager : IHttpClientManager
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<HttpClientManager> _logger;
        private readonly HttpClientOptions _options;

        public HttpClientManager(
            IHttpClientFactory httpClientFactory,
            IServiceProvider serviceProvider,
            ILogger<HttpClientManager> logger,
            IOptions<HttpClientOptions> options)
        {
            _httpClientFactory = httpClientFactory;
            _serviceProvider = serviceProvider;
            _logger = logger;
            _options = options.Value;
        }

        /// <summary>
        /// 获取指定名称的HTTP客户端
        /// </summary>
        public System.Net.Http.HttpClient GetHttpClient(string clientName)
        {
            if (string.IsNullOrEmpty(clientName))
            {
                throw new ArgumentException("客户端名称不能为空", nameof(clientName));
            }

            if (!_options.ApiEndpoints.ContainsKey(clientName))
            {
                throw new ArgumentException($"未找到名为 '{clientName}' 的客户端配置", nameof(clientName));
            }

            try
            {
                var client = _httpClientFactory.CreateClient(clientName);
                _logger.LogDebug("成功创建HTTP客户端: {ClientName}", clientName);
                return client;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建HTTP客户端失败: {ClientName}", clientName);
                throw;
            }
        }

        /// <summary>
        /// 获取指定类型的Refit客户端
        /// </summary>
        public T GetRefitClient<T>() where T : class
        {
            try
            {
                var client = _serviceProvider.GetRequiredService<T>();
                _logger.LogDebug("成功获取Refit客户端: {ClientType}", typeof(T).Name);
                return client;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Refit客户端失败: {ClientType}", typeof(T).Name);
                throw;
            }
        }

        /// <summary>
        /// 检查指定客户端是否可用
        /// </summary>
        public async Task<bool> IsClientAvailableAsync(string clientName)
        {
            try
            {
                var healthStatus = await GetClientHealthAsync(clientName);
                return healthStatus.IsHealthy;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检查客户端可用性时发生异常: {ClientName}", clientName);
                return false;
            }
        }

        /// <summary>
        /// 获取客户端健康状态
        /// </summary>
        public async Task<ClientHealthStatus> GetClientHealthAsync(string clientName)
        {
            var healthStatus = new ClientHealthStatus
            {
                ClientName = clientName,
                LastCheckTime = DateTime.UtcNow
            };

            if (!_options.ApiEndpoints.ContainsKey(clientName))
            {
                healthStatus.IsHealthy = false;
                healthStatus.Description = "客户端配置不存在";
                healthStatus.ErrorMessage = $"未找到名为 '{clientName}' 的客户端配置";
                return healthStatus;
            }

            try
            {
                var client = GetHttpClient(clientName);
                var stopwatch = Stopwatch.StartNew();

                // 发送健康检查请求（通常是GET /health 或 HEAD 请求）
                var response = await client.SendAsync(new HttpRequestMessage(HttpMethod.Head, "/"), 
                    CancellationToken.None);

                stopwatch.Stop();
                healthStatus.ResponseTimeMs = stopwatch.ElapsedMilliseconds;

                if (response.IsSuccessStatusCode)
                {
                    healthStatus.IsHealthy = true;
                    healthStatus.Description = "客户端健康";
                }
                else
                {
                    healthStatus.IsHealthy = false;
                    healthStatus.Description = $"服务器返回错误状态码: {response.StatusCode}";
                    healthStatus.ErrorMessage = response.ReasonPhrase;
                }
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                healthStatus.IsHealthy = false;
                healthStatus.Description = "请求超时";
                healthStatus.ErrorMessage = "健康检查请求超时";
            }
            catch (HttpRequestException ex)
            {
                healthStatus.IsHealthy = false;
                healthStatus.Description = "网络连接错误";
                healthStatus.ErrorMessage = ex.Message;
            }
            catch (Exception ex)
            {
                healthStatus.IsHealthy = false;
                healthStatus.Description = "健康检查失败";
                healthStatus.ErrorMessage = ex.Message;
                _logger.LogError(ex, "客户端健康检查失败: {ClientName}", clientName);
            }

            return healthStatus;
        }

        /// <summary>
        /// 获取所有已配置的客户端名称
        /// </summary>
        public IEnumerable<string> GetConfiguredClientNames()
        {
            return _options.ApiEndpoints.Keys;
        }
    }
}
