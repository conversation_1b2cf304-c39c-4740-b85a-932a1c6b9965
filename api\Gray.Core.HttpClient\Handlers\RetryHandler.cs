using Gray.Core.GrayHttpClient.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;

namespace Gray.Core.GrayHttpClient.Handlers
{
    /// <summary>
    /// 重试处理器
    /// 提供自定义的重试逻辑，作为Polly重试策略的补充
    /// </summary>
    public class RetryHandler : DelegatingHandler
    {
        private readonly ILogger<RetryHandler> _logger;
        private readonly HttpClientOptions _options;

        public RetryHandler(
            ILogger<RetryHandler> logger,
            IOptions<HttpClientOptions> options)
        {
            _logger = logger;
            _options = options.Value;
        }

        /// <summary>
        /// 发送HTTP请求的处理
        /// </summary>
        protected override async Task<HttpResponseMessage> SendAsync(
            HttpRequestMessage request, 
            CancellationToken cancellationToken)
        {
            var maxRetries = _options.MaxRetryAttempts;
            var retryDelay = TimeSpan.FromSeconds(_options.RetryDelaySeconds);

            for (int attempt = 0; attempt <= maxRetries; attempt++)
            {
                try
                {
                    // 克隆请求（因为HttpRequestMessage只能使用一次）
                    var clonedRequest = await CloneRequestAsync(request);
                    
                    var response = await base.SendAsync(clonedRequest, cancellationToken);

                    // 如果请求成功或不需要重试，直接返回
                    if (response.IsSuccessStatusCode || !ShouldRetry(response.StatusCode, attempt, maxRetries))
                    {
                        if (attempt > 0)
                        {
                            _logger.LogInformation("HTTP请求在第 {Attempt} 次尝试后成功: {Method} {Uri}",
                                attempt + 1, request.Method, request.RequestUri);
                        }
                        return response;
                    }

                    // 记录重试信息
                    _logger.LogWarning("HTTP请求失败，准备重试 {Attempt}/{MaxRetries}: {Method} {Uri}, 状态码: {StatusCode}",
                        attempt + 1, maxRetries + 1, request.Method, request.RequestUri, response.StatusCode);

                    // 如果不是最后一次尝试，等待后重试
                    if (attempt < maxRetries)
                    {
                        await Task.Delay(retryDelay, cancellationToken);
                        retryDelay = TimeSpan.FromMilliseconds(retryDelay.TotalMilliseconds * 2); // 指数退避
                    }
                    else
                    {
                        // 最后一次尝试失败，返回响应
                        _logger.LogError("HTTP请求在 {MaxRetries} 次重试后仍然失败: {Method} {Uri}, 最终状态码: {StatusCode}",
                            maxRetries + 1, request.Method, request.RequestUri, response.StatusCode);
                        return response;
                    }
                }
                catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException || cancellationToken.IsCancellationRequested)
                {
                    if (attempt < maxRetries && !cancellationToken.IsCancellationRequested)
                    {
                        _logger.LogWarning("HTTP请求超时，准备重试 {Attempt}/{MaxRetries}: {Method} {Uri}",
                            attempt + 1, maxRetries + 1, request.Method, request.RequestUri);
                        
                        await Task.Delay(retryDelay, cancellationToken);
                        retryDelay = TimeSpan.FromMilliseconds(retryDelay.TotalMilliseconds * 2);
                    }
                    else
                    {
                        _logger.LogError("HTTP请求在 {MaxRetries} 次重试后仍然超时: {Method} {Uri}",
                            maxRetries + 1, request.Method, request.RequestUri);
                        throw;
                    }
                }
                catch (HttpRequestException ex)
                {
                    if (attempt < maxRetries)
                    {
                        _logger.LogWarning(ex, "HTTP请求异常，准备重试 {Attempt}/{MaxRetries}: {Method} {Uri}",
                            attempt + 1, maxRetries + 1, request.Method, request.RequestUri);
                        
                        await Task.Delay(retryDelay, cancellationToken);
                        retryDelay = TimeSpan.FromMilliseconds(retryDelay.TotalMilliseconds * 2);
                    }
                    else
                    {
                        _logger.LogError(ex, "HTTP请求在 {MaxRetries} 次重试后仍然异常: {Method} {Uri}",
                            maxRetries + 1, request.Method, request.RequestUri);
                        throw;
                    }
                }
            }

            // 这里不应该到达，但为了编译器满意
            throw new InvalidOperationException("重试逻辑异常");
        }

        /// <summary>
        /// 判断是否应该重试
        /// </summary>
        private bool ShouldRetry(HttpStatusCode statusCode, int currentAttempt, int maxRetries)
        {
            if (currentAttempt >= maxRetries)
            {
                return false;
            }

            // 定义需要重试的状态码
            var retryableStatusCodes = new[]
            {
                HttpStatusCode.RequestTimeout,          // 408
                HttpStatusCode.TooManyRequests,         // 429
                HttpStatusCode.InternalServerError,     // 500
                HttpStatusCode.BadGateway,              // 502
                HttpStatusCode.ServiceUnavailable,      // 503
                HttpStatusCode.GatewayTimeout           // 504
            };

            return retryableStatusCodes.Contains(statusCode);
        }

        /// <summary>
        /// 克隆HTTP请求消息
        /// </summary>
        private async Task<HttpRequestMessage> CloneRequestAsync(HttpRequestMessage original)
        {
            var clone = new HttpRequestMessage(original.Method, original.RequestUri)
            {
                Version = original.Version
            };

            // 复制请求头
            foreach (var header in original.Headers)
            {
                clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            // 复制内容
            if (original.Content != null)
            {
                var contentBytes = await original.Content.ReadAsByteArrayAsync();
                clone.Content = new ByteArrayContent(contentBytes);

                // 复制内容头
                foreach (var header in original.Content.Headers)
                {
                    clone.Content.Headers.TryAddWithoutValidation(header.Key, header.Value);
                }
            }

            // 复制属性
            foreach (var property in original.Options)
            {
                clone.Options.Set(new HttpRequestOptionsKey<object?>(property.Key), property.Value);
            }

            return clone;
        }
    }
}
