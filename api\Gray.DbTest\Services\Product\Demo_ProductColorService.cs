/*
Tel:15662337743
 *所有业务编写全部应在Partial文件夹下Demo_ProductColorService与IDemo_ProductColorService中编写
 */
using Gray.DbTest.IRepositories;
using Gray.DbTest.IServices;
using Gray.Core.BaseProvider;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.DomainModels;

namespace Gray.DbTest.Services
{
    public partial class Demo_ProductColorService : ServiceBase<Demo_ProductColor, IDemo_ProductColorRepository>
    , IDemo_ProductColorService, IDependency
    {
    public Demo_ProductColorService(IDemo_ProductColorRepository repository)
    : base(repository)
    {
    Init(repository);
    }
    public static IDemo_ProductColorService Instance
    {
      get { return AutofacContainerModule.GetService<IDemo_ProductColorService>(); } }
    }
 }
