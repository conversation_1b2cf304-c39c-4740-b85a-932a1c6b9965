/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "商品实时库存",TableName = "Goods_INV",DBServer = "JL_CRMDbContext")]
    public partial class Goods_INV:JL_CRMEntity
    {
        /// <summary>
       ///代码
       /// </summary>
       [Display(Name ="代码")]
       [MaxLength(510)]
       [Column(TypeName="nvarchar(510)")]
       [Editable(true)]
       public string Code { get; set; }

       /// <summary>
       ///规格
       /// </summary>
       [Display(Name ="规格")]
       [MaxLength(1000)]
       [Column(TypeName="nvarchar(1000)")]
       [Editable(true)]
       public string SPEC { get; set; }

       /// <summary>
       ///实时库存
       /// </summary>
       [Display(Name ="实时库存")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? Qty { get; set; }

       /// <summary>
       ///id
       /// </summary>
       [Key]
       [Display(Name ="id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid GoodInvID { get; set; }

       /// <summary>
       ///日期
       /// </summary>
       [Display(Name ="日期")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? InvDate { get; set; }

       
    }
}