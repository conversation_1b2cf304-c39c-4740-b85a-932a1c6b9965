using Gray.Core.BaseProvider;
using Gray.Core.EFDbContext;
using Gray.Entity.SystemModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;

namespace Gray.Core.UnitOfWorkMange
{
    public interface IUnitOfWork : IDisposable
    {
        // 获取当前上下文
        TDbContext GetDbContext<TDbContext>(bool enableTracking = false) where TDbContext : BaseDbContext<TDbContext>;

        // 根据实体类型获取DbContext
        BaseDbContext GetDbContextByEntityType(Type entityType);

        // 根据实体类型获取DbContext (泛型版本)
        BaseDbContext GetDbContextByEntityType<TEntity>();

        // 获取仓储
        IRepository<TEntity> GetRepository<TEntity>() where TEntity : BaseEntity;

        // 保存更改
        int SaveChanges();

        // 异步保存
        Task<int> SaveChangesAsync();

        // 获取活动上下文数量
        int GetActiveContexts();

        // 诊断方法
        Dictionary<string, object> GetDiagnosticInfo();

        // 为TransactionService提供的内部事务管理方法（仅供TransactionService使用）
        IDbContextTransaction BeginTransaction();
        IDbContextTransaction BeginTransaction(System.Data.IsolationLevel isolationLevel);
        Task<IDbContextTransaction> BeginTransactionAsync();
        Task<IDbContextTransaction> BeginTransactionAsync(System.Data.IsolationLevel isolationLevel);
        void Commit();
        void Rollback();
        Task CommitAsync();
        Task RollbackAsync();
        Task RollbackAsync(CancellationToken cancellationToken);
        Task CommitAsync(CancellationToken cancellationToken);

        // 事务状态查询（仅供TransactionService使用）
        int TransactionCount { get; }
        bool HasActiveTransaction { get; }
        int TransactionLevel { get; }
        bool IsTransactionStarted { get; }
        TimeSpan TransactionTimeout { get; set; }
        string GetTransactionStatus();

        // 事务事件（仅供TransactionService使用）
        event EventHandler<Exception> OnTransactionError;
        event EventHandler<IDbContextTransaction> OnTransactionStarted;
        event EventHandler<bool> OnTransactionCompleted;

        // 事务作用域（仅供TransactionService使用）
        IDisposable CreateTransactionScope(bool autoRollback = true);
        IDisposable CreateTransactionScopeWithNewConnection(bool autoRollback = true);

        // 嵌套事务支持（仅供TransactionService使用）
        void EnableNestedTransactions();
        void DisableNestedTransactions();

        // 新增异步锁控制
        Task<bool> TryLockAsync(TimeSpan timeout, CancellationToken cancellationToken = default);
        void ReleaseLock();

        // 新增性能监控方法
        Task<T> ExecuteWithMetricsAsync<T>(Func<Task<T>> action, string operationName);

        // 新增批量操作接口
        Task BatchSaveChangesAsync(CancellationToken cancellationToken = default); 
        
        // 新增日志接口
        void LogTransactionInfo(string message);

        // 新增性能指标获取和清理方法
        IDictionary<string, long> GetMetrics();
        void ClearMetrics();

        // 新增诊断方法
        Dictionary<string, string> GetContextDiagnostics();
        bool ValidateContexts();

        // 新增安全执行方法
        Task<T> ExecuteWithContextAsync<TDbContext, T>(Func<TDbContext, Task<T>> operation)
            where TDbContext : BaseDbContext<TDbContext>;

        // 外部事务管理支持
        IDbContextTransaction CurrentTransaction { get; }
        void SetExternalTransaction(IDbContextTransaction transaction);
        void ClearExternalTransaction();
    }
}
