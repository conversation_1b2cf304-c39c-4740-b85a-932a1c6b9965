﻿/*
 * 编号生成解决方案 - 解决重复编号问题
 * 
 * 解决方案特性：
 * 1. 分布式锁机制：使用 SemaphoreSlim 确保同一规则的编码生成串行执行
 * 2. 数据库事务：使用 Serializable 隔离级别确保查询和更新的原子性
 * 3. 向后兼容：保留原有API接口，无需修改现有调用代码
 * 
 * 统计逻辑说明（修复后）：
 * 
 * 1. 日增长规则 (RuleIncremental = "day")：
 *    - 统计条件：前缀匹配 + 当天日期范围 (CreateDate >= 当天00:00:00)
 *    - 示例：ORDER前缀 + 2023-12-11当天的记录数
 *    - 生成编号：ORDER20231211001, ORDER20231211002...
 * 
 * 2. 月增长规则 (RuleIncremental = "month")：
 *    - 统计条件：前缀匹配 + 当月日期范围 (CreateDate >= 当月1号)
 *    - 示例：ORDER前缀 + 2023-12月的记录数
 *    - 生成编号：ORDER202312001, ORDER202312002...
 * 
 * 3. 年增长规则 (RuleIncremental = "year")：
 *    - 统计条件：前缀匹配 + 当年日期范围 (CreateDate >= 当年1月1号)
 *    - 示例：ORDER前缀 + 2023年的记录数
 *    - 生成编号：ORDER2023001, ORDER2023002...
 * 
 * 4. 无限制规则 (RuleIncremental = "none")：
 *    - 统计条件：只有前缀匹配，不限制日期范围
 *    - 示例：ORDER前缀的所有历史记录数
 *    - 生成编号：ORDER001, ORDER002...
 * 
 * 使用示例：
 * 
 * // 单个实体生成编号
 * var order = new Order();
 * order.CreateCode();
 * 
 * // 批量生成编号
 * var orderList = new List<Order> { new Order(), new Order() };
 * orderList.CreateCode();
 * 
 * // 自定义参数生成编号
 * var user = new User();
 * user.Create(x => x.UserCode, "USER", x => x.CreateDate, null, "day", "年月日", 4);
 * 
 * 并发安全保证：
 * - 每个编码规则使用独立的锁，不同规则之间不会相互阻塞
 * - 锁的粒度：{表名}_{前缀}_{增长规则}_{租户ID}
 * - 数据库事务确保查询到的记录数量在事务提交前不会被其他操作修改
 * 
 * 性能优化：
 * - 只有需要生成编号的操作会获取锁，正常的业务操作不受影响
 * - 使用 SemaphoreSlim 而不是 lock，支持异步操作
 * - 锁对象按需创建，避免内存浪费
 */

using Gray.Core.Configuration;
using Gray.Core.DBManager;
using Gray.Core.EFDbContext;
using Gray.Core.Enums;
using Gray.Core.ManageUser;
using Gray.Core.Services;
using Gray.Entity;
using Gray.Entity.DomainModels;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Data;

namespace Gray.Core.Extensions
{
    public static class IdentityCode
    {
        private static List<Sys_CodeRule> _codeRules = null;
        private static object ruleObject = new object();
        
        // 使用静态字典存储每个规则的锁对象，支持分布式锁
        private static readonly Dictionary<string, SemaphoreSlim> _lockObjects = new Dictionary<string, SemaphoreSlim>();
        // 用于保护_lockObjects的锁
        private static readonly object _lockObjectsLock = new object();

        public static void Init()
        {
            try
            {
                _codeRules = null;
                Logger.Info(LoggerType.System, "编码规则初始化成功");
            }
            catch (Exception ex)
            {
                Logger.Error(LoggerType.System, "编码规则初始化失败", null, ex.ToString());
                throw;
            }
        }

        public static List<Sys_CodeRule> CodeRules
        {
            get
            {
                try
                {
                    if (_codeRules == null)
                    {
                        lock (ruleObject)
                        {
                            if (_codeRules == null)
                            {
                                _codeRules = DBServerProvider.DbContext.Set<Sys_CodeRule>().ToList();
                                Logger.Info(LoggerType.System, $"成功加载 {_codeRules.Count} 条编码规则");
                            }
                        }
                    }
                    return _codeRules;
                }
                catch (Exception ex)
                {
                    Logger.Error(LoggerType.System, "获取编码规则失败", null, ex.ToString());
                    throw;
                }
            }
        }

        /// <summary>
        /// 生成单据号(先在[单据编码]菜单维护规则)
        /// 使用方式：var order = new DemoOrder();
        ///              order.CreateCode();
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static T CreateCode<T>(this T entity) where T : class
        {
            try
            {
                Console.WriteLine($"[CreateCode] 开始为实体 {typeof(T).Name} 生成编码");
                CreateCodeWithLock(new List<T>() { entity });
                Console.WriteLine($"[CreateCode] 完成为实体 {typeof(T).Name} 生成编码");
                return entity;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CreateCode] 生成单据编码失败，实体类型：{typeof(T).Name}, 异常: {ex.Message}");
                Logger.Error(LoggerType.System, $"生成单据编码失败，实体类型：{typeof(T).Name}", null, ex.ToString());
                throw;
            }
        }

        /// <summary>
        /// 诊断CreateCode方法不生成编码的原因
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="entity"></param>
        /// <returns>诊断信息</returns>
        public static string DiagnoseCreateCode<T>(this T entity) where T : class
        {
            var diagnosticInfo = new StringBuilder();
            diagnosticInfo.AppendLine($"=== CreateCode 诊断信息 ===");
            diagnosticInfo.AppendLine($"实体类型: {typeof(T).Name}");
            
            try
            {
                // 1. 检查编码规则是否存在
                diagnosticInfo.AppendLine($"1. 检查编码规则...");
                List<Sys_CodeRule> allRules = null;
                try
                {
                    allRules = CodeRules;
                }
                catch (Exception ex)
                {
                    diagnosticInfo.AppendLine($"   ❌ 获取编码规则时出错: {ex.Message}");
                    return diagnosticInfo.ToString();
                }
                
                diagnosticInfo.AppendLine($"   总编码规则数量: {allRules?.Count ?? 0}");
                
                if (allRules == null || allRules.Count == 0)
                {
                    diagnosticInfo.AppendLine($"   ❌ 未找到任何编码规则，请检查Sys_CodeRule表是否有数据");
                    return diagnosticInfo.ToString();
                }

                // 2. 检查是否有匹配的规则
                var matchingRules = allRules.Where(x => x.TableName == typeof(T).Name).ToList();
                diagnosticInfo.AppendLine($"   匹配表名 '{typeof(T).Name}' 的规则数量: {matchingRules.Count}");
                
                if (matchingRules.Count == 0)
                {
                    diagnosticInfo.AppendLine($"   ❌ 未找到表名为 '{typeof(T).Name}' 的编码规则");
                    diagnosticInfo.AppendLine($"   💡 请在Sys_CodeRule表中添加TableName='{typeof(T).Name}'的记录");
                    diagnosticInfo.AppendLine($"   💡 现有的表名规则: {string.Join(", ", allRules.Select(r => r.TableName).Distinct())}");
                    return diagnosticInfo.ToString();
                }

                // 3. 检查多租户过滤
                diagnosticInfo.AppendLine($"2. 检查多租户过滤...");
                diagnosticInfo.AppendLine($"   UseDynamicShareDB: {AppSetting.UseDynamicShareDB}");
                diagnosticInfo.AppendLine($"   CurrentServiceId: {UserContext.CurrentServiceId}");
                diagnosticInfo.AppendLine($"   TenancyField: {AppSetting.TenancyField}");

                var filteredRules = matchingRules.AsQueryable();
                
                if (AppSetting.UseDynamicShareDB)
                {
                    filteredRules = filteredRules.Where(x => x.DbServiceId == UserContext.CurrentServiceId);
                    diagnosticInfo.AppendLine($"   应用DbServiceId过滤后的规则数量: {filteredRules.Count()}");
                }
                else
                {
                    if (AppSetting.TenancyField != null)
                    {
                        var currentServiceIdStr = UserContext.CurrentServiceId.ToString();
                        filteredRules = filteredRules.Where(x => x.TenancyId == currentServiceIdStr);
                        diagnosticInfo.AppendLine($"   应用TenancyId过滤后的规则数量: {filteredRules.Count()}");
                    }
                }

                var finalRules = filteredRules.ToList();
                if (finalRules.Count == 0)
                {
                    diagnosticInfo.AppendLine($"   ❌ 应用多租户过滤后无匹配规则");
                    diagnosticInfo.AppendLine($"   💡 请检查编码规则的DbServiceId或TenancyId字段");
                    diagnosticInfo.AppendLine($"   💡 匹配规则的DbServiceId: {string.Join(", ", matchingRules.Select(r => r.DbServiceId?.ToString() ?? "null"))}");
                    diagnosticInfo.AppendLine($"   💡 匹配规则的TenancyId: {string.Join(", ", matchingRules.Select(r => r.TenancyId ?? "null"))}");
                    return diagnosticInfo.ToString();
                }

                // 4. 检查最终选择的规则
                var selectedRule = finalRules.OrderByDescending(x => x.CreateDate).FirstOrDefault();
                diagnosticInfo.AppendLine($"3. 最终选择的规则:");
                diagnosticInfo.AppendLine($"   规则ID: {selectedRule.CodeRuleId}");
                diagnosticInfo.AppendLine($"   规则名称: {selectedRule.Name}");
                diagnosticInfo.AppendLine($"   表名: {selectedRule.TableName}");
                diagnosticInfo.AppendLine($"   字段: {selectedRule.Field}");
                diagnosticInfo.AppendLine($"   前缀: {selectedRule.PrefixCode}");
                diagnosticInfo.AppendLine($"   规则类型: {selectedRule.RuleType}");
                diagnosticInfo.AppendLine($"   增长类型: {selectedRule.RuleIncremental}");
                diagnosticInfo.AppendLine($"   编号位数: {selectedRule.ValueLen}");
                diagnosticInfo.AppendLine($"   排序字段: {selectedRule.OrderFiled}");
                diagnosticInfo.AppendLine($"   连接符号: {selectedRule.ConcatenationSymbol}");

                // 5. 检查字段是否存在
                diagnosticInfo.AppendLine($"4. 检查实体字段...");
                var properties = typeof(T).GetProperties();
                var codeField = properties.FirstOrDefault(p => p.Name == selectedRule.Field);
                
                if (codeField == null)
                {
                    diagnosticInfo.AppendLine($"   ❌ 实体 {typeof(T).Name} 中未找到字段 '{selectedRule.Field}'");
                    diagnosticInfo.AppendLine($"   💡 可用字段: {string.Join(", ", properties.Select(p => p.Name))}");
                    return diagnosticInfo.ToString();
                }
                
                diagnosticInfo.AppendLine($"   ✅ 找到编码字段: {selectedRule.Field} ({codeField.PropertyType.Name})");

                // 6. 检查排序字段
                if (!string.IsNullOrEmpty(selectedRule.OrderFiled))
                {
                    var orderField = properties.FirstOrDefault(p => p.Name == selectedRule.OrderFiled);
                    if (orderField == null)
                    {
                        diagnosticInfo.AppendLine($"   ❌ 实体 {typeof(T).Name} 中未找到排序字段 '{selectedRule.OrderFiled}'");
                        return diagnosticInfo.ToString();
                    }
                    diagnosticInfo.AppendLine($"   ✅ 找到排序字段: {selectedRule.OrderFiled} ({orderField.PropertyType.Name})");
                }

                // 7. 检查当前字段值
                var currentValue = codeField.GetValue(entity);
                diagnosticInfo.AppendLine($"   当前字段值: {currentValue ?? "null"}");

                diagnosticInfo.AppendLine($"✅ 诊断完成，配置看起来正常");
                diagnosticInfo.AppendLine($"💡 如果仍然不生成编码，请检查Create方法中的数据库查询是否正常");

            }
            catch (Exception ex)
            {
                diagnosticInfo.AppendLine($"❌ 诊断过程中发生异常: {ex.Message}");
                diagnosticInfo.AppendLine($"   详细信息: {ex.ToString()}");
            }

            return diagnosticInfo.ToString();
        }

        /// <summary>
        /// 批量生成单据号(先在[单据编码]菜单维护规则)
        /// 使用方式：var list =new List<DemoOrder>(){};
        ///              list.CreateCode();
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static List<T> CreateCode<T>(this List<T> entity) where T : class
        {
            return CreateCodeWithLock(entity);
        }

        /// <summary>
        /// 使用分布式锁的编码生成方法
        /// </summary>
        private static List<T> CreateCodeWithLock<T>(List<T> entity) where T : class
        {
            try
            {
                Console.WriteLine($"[CreateCodeWithLock] 开始为 {entity?.Count ?? 0} 个 {typeof(T).Name} 实体生成编码");
                
                // 获取编码规则
                var query = CodeRules.Where(x => x.TableName == typeof(T).Name);
                Console.WriteLine($"[CreateCodeWithLock] 找到 {query.Count()} 条匹配表名 '{typeof(T).Name}' 的编码规则");

                if (AppSetting.UseDynamicShareDB)
                {
                    query = query.Where(x => x.DbServiceId == UserContext.CurrentServiceId);
                }
                else
                {
                    if (AppSetting.TenancyField != null)
                    {
                        query = query.Where(x => x.TenancyId == UserContext.CurrentServiceId.ToString());
                    }
                }

                var rule = query.OrderByDescending(x => x.CreateDate).FirstOrDefault();
                if (rule == null)
                {
                    Logger.Error(LoggerType.System, $"未找到表 {typeof(T).Name} 的编码规则");
                    return entity;
                }

                Logger.Info(LoggerType.System, $"开始生成编码，使用规则：{rule.TableName}-{rule.PrefixCode}");

                // 构建锁的键值，确保同一规则的编码生成是互斥的
                string lockKey = $"{typeof(T).Name}_{rule.PrefixCode}_{rule.RuleIncremental}_{UserContext.CurrentServiceId}";
                
                // 获取或创建该规则对应的锁对象
                SemaphoreSlim lockObject;
                lock (_lockObjectsLock)
                {
                    if (!_lockObjects.TryGetValue(lockKey, out lockObject))
                    {
                        lockObject = new SemaphoreSlim(1, 1);
                        _lockObjects[lockKey] = lockObject;
                    }
                }

                // 使用锁保证原子性操作
                lockObject.Wait();
                try
                {
                    string generatedCode = entity.CreateWithTransaction(
                        rule.Field.GetExpression<T>(),
                        rule.PrefixCode,
                        rule.OrderFiled.GetExpression<T>(),
                        filter: null,
                        rule.RuleIncremental,
                        dateFormat: rule.RuleType,
                        len: rule.ValueLen,
                        concatenationSymbol: rule.ConcatenationSymbol);

                    return entity;
                }
                finally
                {
                    lockObject.Release();
                }
            }
            catch (Exception ex)
            {
                Logger.Error(LoggerType.System, $"批量生成单据编码失败，实体类型：{typeof(T).Name}", null, ex.ToString());
                throw;
            }
        }

        /// <summary>
        /// 创建自增单据号
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="entity">实体对象</param>
        /// <param name="codeField">要设置单据号的字段</param>
        /// <param name="preCode">单据号前缀,如：{TC}{2023}{0001}</param>
        /// <param name="dateFieldExpression">排序字段，每天都从第1个号码开始</param>
        /// <param name="filter">过滤条件</param>
        /// <param name="startingDay">是否每天都从第1个号码开始</param>
        /// <param name="dateFormat">是否生成日期流水号</param>
        /// <param name="len">数字长度</param>
        /// 使用示例：
        ///   Sys_User user=  new Sys_User();
        ///   user.Create(x => x.UserName, "U", x => x.CreateDate);
        /// <returns></returns>
        public static string Create<T>(this T entity,
            Expression<Func<T, object>> codeField,
            string preCode = "Code",
            Expression<Func<T, object>> dateFieldExpression = null,
            Expression<Func<T, bool>> filter = null,
            string RuleIncremental = "none",
            string dateFormat = "年月日",
            int len = 4,
            string concatenationSymbol = null
            ) where T : class
        {
            try
            {
                return new List<T>() { entity }.CreateWithTransaction(codeField, preCode, dateFieldExpression, filter,
                    RuleIncremental,
                    dateFormat, len, concatenationSymbol);
            }
            catch (Exception ex)
            {
                Logger.Error(LoggerType.System, $"生成单据编码失败，实体类型：{typeof(T).Name}",
                    $"前缀：{preCode}，日期格式：{dateFormat}，长度：{len}", ex.ToString());
                throw;
            }
        }

        /// <summary>
        /// 使用数据库事务的原子性编码生成方法
        /// </summary>
        public static string CreateWithTransaction<T>(this List<T> list,
         Expression<Func<T, object>> codeField,
         string preCode = "Code",
         Expression<Func<T, object>> dateFieldExpression = null,
         Expression<Func<T, bool>> filter = null,
         string RuleIncremental = null,
         string dateFormat = "年月日",
         int len = 4,
         string concatenationSymbol = null
         ) where T : class
        {
            try
            {
                if (codeField == null)
                {
                    throw new ArgumentNullException(nameof(codeField), "编码字段表达式不能为空");
                }

                if (list == null || !list.Any())
                {
                    throw new ArgumentException("实体列表不能为空", nameof(list));
                }

                Logger.Info(LoggerType.System, "开始原子性生成编码",
                    $"实体类型：{typeof(T).Name}，前缀：{preCode}，规则：{RuleIncremental}，日期格式：{dateFormat}");

                concatenationSymbol = concatenationSymbol ?? "";

                string dateRangeField = dateFieldExpression?.GetExpressionPropertyFirst() ?? AppSetting.CreateMember.DateField;
                var field = codeField.GetExpressionPropertyFirst();

                string rulePrefix;
                if (dateFormat != null)
                {
                    string actualDateFormat = dateFormat.ToLower() switch
                    {
                        "年" => "yyyy",
                        "年月" => "yyyyMM",
                        "年月日" => "yyyyMMdd",
                        _ => dateFormat
                    };
                    rulePrefix = $"{preCode}{concatenationSymbol}{DateTime.Now.ToString(actualDateFormat)}";
                }
                else
                {
                    rulePrefix = preCode;
                }

                // 使用数据库事务确保原子性操作
                var dbContext = DBServerProvider.GetEFDbContext<T>();
                using var transaction = dbContext.Database.BeginTransaction(IsolationLevel.Serializable);
                try
                {
                    // 根据RuleIncremental规则确定查询范围
                    DateTime startDate = DateTime.Now;
                    Expression<Func<T, bool>> dateRangeCondition = null;

                    if (!string.IsNullOrEmpty(RuleIncremental))
                    {
                        switch (RuleIncremental.ToLower())
                        {
                            case "day":
                                // 当天的起始时间
                                startDate = DateTime.Now.Date;
                                dateRangeCondition = dateRangeField.CreateExpression<T>(startDate, Enums.LinqExpressionType.ThanOrEqual);
                                Logger.Info(LoggerType.System, "使用日增长规则", $"起始日期：{startDate:yyyy-MM-dd}");
                                break;

                            case "month":
                                // 当月的起始时间
                                startDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                                dateRangeCondition = dateRangeField.CreateExpression<T>(startDate, Enums.LinqExpressionType.ThanOrEqual);
                                Logger.Info(LoggerType.System, "使用月增长规则", $"起始日期：{startDate:yyyy-MM-dd}");
                                break;

                            case "year":
                                // 当年的起始时间
                                startDate = new DateTime(DateTime.Now.Year, 1, 1);
                                dateRangeCondition = dateRangeField.CreateExpression<T>(startDate, Enums.LinqExpressionType.ThanOrEqual);
                                Logger.Info(LoggerType.System, "使用年增长规则", $"起始日期：{startDate:yyyy-MM-dd}");
                                break;

                            case "none":
                            default:
                                // 不限制日期范围
                                dateRangeCondition = null;
                                Logger.Info(LoggerType.System, "使用无限制规则");
                                break;
                        }
                    }

                    // 构建查询，使用当前事务内的上下文
                    var dbSet = dbContext.Set<T>();
                    IQueryable<T> query = dbSet;

                    // 应用过滤条件
                    if (filter != null)
                    {
                        query = query.Where(filter);
                    }

                    // 应用前缀条件（使用基础前缀，确保统计相同前缀的记录）
                    Expression<Func<T, bool>> prefixCondition = null;
                    if (!string.IsNullOrEmpty(preCode))
                    {
                        prefixCondition = field.CreateExpression<T>(preCode, Enums.LinqExpressionType.LikeStart);
                        query = query.Where(prefixCondition);
                    }

                    // 应用日期范围条件（基于日期字段，不是编码字段）
                    if (dateRangeCondition != null)
                    {
                        query = query.Where(dateRangeCondition);
                    }

                    // 在事务内原子性地查询当前记录数量
                    int recordCount = 0;
                    try
                    {
                        // 在 Serializable 隔离级别下，确保读取到的数据在事务提交前不会被其他事务修改
                        recordCount = query.Count();
                        Logger.Info(LoggerType.System, "事务内查询记录数量",
                            $"表：{typeof(T).Name}，前缀：{preCode}，规则：{RuleIncremental}，起始日期：{startDate:yyyy-MM-dd}，数量：{recordCount}");
                        
                        // 详细日志：显示查询条件
                        string queryConditions = "查询条件: ";
                        if (!string.IsNullOrEmpty(preCode))
                        {
                            queryConditions += $"前缀匹配({field} LIKE '{preCode}%')";
                        }
                        if (dateRangeCondition != null)
                        {
                            queryConditions += $", 日期范围({dateRangeField} >= '{startDate:yyyy-MM-dd}')";
                        }
                        if (filter != null)
                        {
                            queryConditions += ", 自定义过滤条件";
                        }
                        Logger.Info(LoggerType.System, queryConditions);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error(LoggerType.System, "事务内查询记录数量失败",
                            $"规则：{RuleIncremental}", ex.ToString());
                        recordCount = 0;
                    }

                    // 生成新的编号
                    int startNumber = recordCount + 1;
                    Logger.Info(LoggerType.System, "事务内新编号起始值",
                        $"起始值：{startNumber}，规则：{rulePrefix}");

                    var property = typeof(T).GetProperty(field);
                    if (property == null)
                    {
                        throw new InvalidOperationException($"未找到属性：{field}");
                    }

                    string code = null;
                    foreach (var entity in list)
                    {
                        code = $"{rulePrefix}{concatenationSymbol}{startNumber.ToString("D" + len)}";
                        property.SetValue(entity, code);
                        startNumber++;
                    }

                    Logger.Info(LoggerType.System, "事务内编码生成完成",
                        $"最终编码：{code}，生成数量：{list.Count}");

                    // 提交事务
                    transaction.Commit();
                    Logger.Info(LoggerType.System, "编码生成事务提交成功", $"最终编码：{code}");

                    // 在事务外更新规则中的Code字段为最新生成的编码（预览编码）
                    if (!string.IsNullOrEmpty(code))
                    {
                        try
                        {
                            // 查找对应的编码规则
                            var query2 = CodeRules.Where(x => x.TableName == typeof(T).Name);

                            if (AppSetting.UseDynamicShareDB)
                            {
                                query2 = query2.Where(x => x.DbServiceId == UserContext.CurrentServiceId);
                            }
                            else
                            {
                                if (AppSetting.TenancyField != null)
                                {
                                    query2 = query2.Where(x => x.TenancyId == UserContext.CurrentServiceId.ToString());
                                }
                            }

                            var rule = query2.OrderByDescending(x => x.CreateDate).FirstOrDefault();

                            if (rule != null)
                            {
                                // 使用全局的 DbContext 来更新编码规则
                                var globalDbContext = DBServerProvider.DbContext;
                                rule.Code = code;
                                globalDbContext.Entry(rule).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                                globalDbContext.SaveChanges();
                                Logger.Info(LoggerType.System, $"已更新编码规则 {rule.TableName} 的预览编码为：{code}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Error(LoggerType.System, "更新编码规则的预览编码失败", null, ex.ToString());
                            // 预览编码更新失败不应该影响主要的编码生成流程，所以这里只记录错误但不抛出异常
                        }
                    }
                    
                    return code;
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    transaction.Rollback();
                    Logger.Error(LoggerType.System, "编码生成事务回滚", $"实体类型：{typeof(T).Name}", ex.ToString());
                    throw;
                }
            }
            catch (Exception ex)
            {
                Logger.Error(LoggerType.System, "生成编码过程中发生错误",
                    $"实体类型：{typeof(T).Name}，前缀：{preCode}，规则：{RuleIncremental}", ex.ToString());
                throw;
            }
        }

        /// <summary>
        /// 原有的Create方法，保持向后兼容性（内部调用新的事务方法）
        /// </summary>
        public static string Create<T>(this List<T> list,
         Expression<Func<T, object>> codeField,
         string preCode = "Code",
         Expression<Func<T, object>> dateFieldExpression = null,
         Expression<Func<T, bool>> filter = null,
         string RuleIncremental = null,
         string dateFormat = "年月日",
         int len = 4,
         string concatenationSymbol = null
         ) where T : class
        {
            return list.CreateWithTransaction(codeField, preCode, dateFieldExpression, filter, RuleIncremental, dateFormat, len, concatenationSymbol);
        }
    }
}