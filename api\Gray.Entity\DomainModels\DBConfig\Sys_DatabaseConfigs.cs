/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "数据库配置表",TableName = "Sys_DatabaseConfigs",DBServer = "SysDbContext")]
    public partial class Sys_DatabaseConfigs:SysEntity
    {
        /// <summary>
       ///唯一 id
       /// </summary>
       [Key]
       [Display(Name ="唯一 id")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int Id { get; set; }

       /// <summary>
       ///数据库名称
       /// </summary>
       [Display(Name ="数据库名称")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string Name { get; set; }

       /// <summary>
       ///服务器地址
       /// </summary>
       [Display(Name ="服务器地址")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string Server { get; set; }

       /// <summary>
       ///端口
       /// </summary>
       [Display(Name ="端口")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? Port { get; set; }

       /// <summary>
       ///数据库实例名
       /// </summary>
       [Display(Name ="数据库实例名")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string DatabaseName { get; set; }

       /// <summary>
       ///数据库版本
       /// </summary>
       [Display(Name ="数据库版本")]
       [MaxLength(20)]
       [Column(TypeName="nvarchar(20)")]
       [Editable(true)]
       public string Version { get; set; }

       /// <summary>
       ///验证方式
       /// </summary>
       [Display(Name ="验证方式")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int AuthenticationType { get; set; }

       /// <summary>
       ///用户名
       /// </summary>
       [Display(Name ="用户名")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       public string UserName { get; set; }

       /// <summary>
       ///连接密码(已加密)
       /// </summary>
       [Display(Name ="连接密码(已加密)")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string EncryptedPassword { get; set; }

       /// <summary>
       ///是否激活
       /// </summary>
       [Display(Name ="是否激活")]
       [Column(TypeName="bit")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public bool IsActive { get; set; }

       /// <summary>
       ///数据库类型
       /// </summary>
       [Display(Name ="数据库类型")]
       [MaxLength(20)]
       [Column(TypeName="nvarchar(20)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string DbType { get; set; }

       /// <summary>
       ///自定义设置
       /// </summary>
       [Display(Name ="自定义设置")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string AdditionalSettings { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? CreateID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Creator")]
       [MaxLength(30)]
       [Column(TypeName="nvarchar(30)")]
       [Editable(true)]
       public string Creator { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? CreateDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ModifyID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Modifier")]
       [MaxLength(30)]
       [Column(TypeName="nvarchar(30)")]
       [Editable(true)]
       public string Modifier { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? ModifyDate { get; set; }

       
    }
}