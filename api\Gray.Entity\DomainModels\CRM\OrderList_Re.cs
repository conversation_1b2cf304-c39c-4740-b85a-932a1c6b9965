/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "订单详情拒绝",TableName = "OrderList_Re",DBServer = "JL_CRMDbContext")]
    public partial class OrderList_Re:JL_CRMEntity
    {
        /// <summary>
       ///
       /// </summary>
       [Display(Name ="OrderListId")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid OrderListId { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="OrderId")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       public Guid? OrderId { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="GoodsId")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       public Guid? GoodsId { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="GoodsCode")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string GoodsCode { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="GoodsName")]
       [MaxLength(400)]
       [Column(TypeName="nvarchar(400)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string GoodsName { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Img")]
       [MaxLength(1000)]
       [Column(TypeName="nvarchar(1000)")]
       [Editable(true)]
       public string Img { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Specs")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       public string Specs { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Price")]
       [Column(TypeName="float")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public float Price { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Qty")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int Qty { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Remark")]
       [MaxLength(1000)]
       [Column(TypeName="nvarchar(1000)")]
       [Editable(true)]
       public string Remark { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? CreateID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Creator")]
       [MaxLength(60)]
       [Column(TypeName="nvarchar(60)")]
       [Editable(true)]
       public string Creator { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? CreateDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ModifyID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Modifier")]
       [MaxLength(60)]
       [Column(TypeName="nvarchar(60)")]
       [Editable(true)]
       public string Modifier { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? ModifyDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="GoodsBrand")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string GoodsBrand { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="WeightSingle")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? WeightSingle { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="WeightCount")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? WeightCount { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="PreOrdeQty")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? PreOrdeQty { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="PriceCount")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? PriceCount { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Rebate")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Rebate { get; set; }

       /// <summary>
       ///价格类型
       /// </summary>
       [Display(Name ="价格类型")]
       [MaxLength(40)]
       [Column(TypeName="nvarchar(40)")]
       [Editable(true)]
       public string PriceType { get; set; }

       /// <summary>
       ///应收账款
       /// </summary>
       [Display(Name ="应收账款")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Receivables { get; set; }

       /// <summary>
       ///实际单价
       /// </summary>
       [Display(Name ="实际单价")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? RealPrice { get; set; }

       /// <summary>
       ///备注外
       /// </summary>
       [Display(Name ="备注外")]
       [MaxLength(1000)]
       [Column(TypeName="nvarchar(1000)")]
       [Editable(true)]
       public string RemarkW { get; set; }

       /// <summary>
       ///3c认证情况
       /// </summary>
       [Display(Name ="3c认证情况")]
       [Column(TypeName="tinyint")]
       [Editable(true)]
       public byte? CCCStatus { get; set; }

       /// <summary>
       ///本区今年
       /// </summary>
       [Display(Name ="本区今年")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? SaleCurrentYear { get; set; }

       /// <summary>
       ///本区上月
       /// </summary>
       [Display(Name ="本区上月")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? SaleLastMonth { get; set; }

       /// <summary>
       ///系统单价
       /// </summary>
       [Display(Name ="系统单价")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? PriceSystem { get; set; }

       /// <summary>
       ///拒绝备份Id
       /// </summary>
       [Key]
       [Display(Name ="拒绝备份Id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid OrderList_ReID { get; set; }

       
    }
}