/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Repository提供数据库操作，如果要增加数据库操作请在当前目录下Partial文件夹IDemo_GoodsRepository编写接口
 */
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Core.BaseProvider;
using Gray.Entity.DomainModels;
using Gray.Core.Extensions.AutofacManager;
namespace Gray.DbTest.IRepositories
{
    public partial interface IDemo_GoodsRepository : IDependency,IRepository<Demo_Goods>
    {
    }
}
