using Gray.Core.DBManager;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Gray.Core.Examples
{
    /// <summary>
    /// 展示如何在应用程序启动时使用实体加载功能的示例类
    /// </summary>
    public static class EntityLoadingExample
    {
        /// <summary>
        /// 在应用程序启动时调用此方法，以加载所有实体类型
        /// </summary>
        public static void ConfigureEntityLoading(IServiceCollection services)
        {
            Console.WriteLine("开始配置实体加载...");
            
            try
            {
                // 1. 首先通过EntityTypeCache加载基本实体
                // 这一步在EntityTypeCache的静态构造函数中已经完成
                
                // 2. 加载额外的实体类型
                LoadAdditionalEntities();
                
                Console.WriteLine("实体加载配置完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"配置实体加载时出错: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 加载额外的实体类型
        /// </summary>
        private static void LoadAdditionalEntities()
        {
            // 获取所有可能包含实体的程序集
            var entityAssemblies = AppDomain.CurrentDomain.GetAssemblies()
                .Where(a => a.GetName().Name.EndsWith(".Entity") || 
                           a.GetName().Name.Contains(".Entity.") ||
                           a.GetName().Name.EndsWith(".Models"))
                .Select(a => a.GetName().Name)
                .ToList();
                
            Console.WriteLine($"找到 {entityAssemblies.Count} 个可能包含实体的程序集");
            
            // 示例：加载实现了特定接口的实体
            LoadEntitiesWithInterface(entityAssemblies, typeof(IEntity));
            
            // 示例：加载带有特定特性的实体
            LoadEntitiesWithAttribute(entityAssemblies, typeof(EntityAttribute));
            
            // 示例：加载特定命名空间下的实体
            LoadEntitiesInNamespace(entityAssemblies, "Gray.Entity.CustomEntities");
        }
        
        /// <summary>
        /// 加载实现了特定接口的实体
        /// </summary>
        private static void LoadEntitiesWithInterface(List<string> assemblyNames, Type interfaceType)
        {
            if (interfaceType == null || !interfaceType.IsInterface)
            {
                throw new ArgumentException("提供的类型必须是接口", nameof(interfaceType));
            }
            
            Console.WriteLine($"开始加载实现了 {interfaceType.Name} 接口的实体...");
            
            EntityTypeCache.LoadCustomTypesFromAssemblies(
                assemblyNames,
                type => type.IsClass && 
                       !type.IsAbstract && 
                       interfaceType.IsAssignableFrom(type)
            );
        }
        
        /// <summary>
        /// 加载带有特定特性的实体
        /// </summary>
        private static void LoadEntitiesWithAttribute(List<string> assemblyNames, Type attributeType)
        {
            if (attributeType == null || !attributeType.IsSubclassOf(typeof(Attribute)))
            {
                throw new ArgumentException("提供的类型必须是特性", nameof(attributeType));
            }
            
            Console.WriteLine($"开始加载带有 {attributeType.Name} 特性的实体...");
            
            EntityTypeCache.LoadCustomTypesFromAssemblies(
                assemblyNames,
                type => type.IsClass && 
                       !type.IsAbstract && 
                       type.GetCustomAttributes(attributeType, true).Length > 0
            );
        }
        
        /// <summary>
        /// 加载特定命名空间下的实体
        /// </summary>
        private static void LoadEntitiesInNamespace(List<string> assemblyNames, string namespaceName)
        {
            if (string.IsNullOrEmpty(namespaceName))
            {
                throw new ArgumentException("命名空间不能为空", nameof(namespaceName));
            }
            
            Console.WriteLine($"开始加载 {namespaceName} 命名空间下的实体...");
            
            EntityTypeCache.LoadCustomTypesFromAssemblies(
                assemblyNames,
                type => type.IsClass && 
                       !type.IsAbstract && 
                       type.Namespace == namespaceName
            );
        }
    }
    
    /// <summary>
    /// 示例实体接口
    /// </summary>
    public interface IEntity
    {
        int Id { get; set; }
    }
    
    /// <summary>
    /// 示例实体特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Class)]
    public class EntityAttribute : Attribute
    {
    }
} 