using Refit;
using Gray.Core.GrayHttpClient.Models;

namespace Gray.Core.GrayHttpClient.Interfaces
{
    /// <summary>
    /// 用户 API 客户端接口
    /// 用于与 Gray.WebApi 用户相关 API 通信
    /// </summary>
    public interface IUserApiClient
    {
        /// <summary>
        /// 用户登录（无验证码）
        /// </summary>
        /// <param name="loginInfo">登录信息</param>
        /// <returns>登录响应</returns>
        [Post("/api/Login/GetJwtToken3")]
        Task<ApiResponse<LoginResponse>> LoginWithoutCodeAsync([Body] LoginWithoutCodeInfo loginInfo);

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <returns>用户信息</returns>
        [Get("/api/User/GetUserInfo")]
        Task<ApiResponse<UserInfo>> GetUserInfoAsync();

        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="size">每页大小</param>
        /// <param name="search">搜索关键词</param>
        /// <returns>用户列表</returns>
        [Get("/api/User/GetUsers")]
        Task<ApiResponse<PagedResult<UserInfo>>> GetUsersAsync(
            [Query] int page = 1,
            [Query] int size = 20,
            [Query] string? search = null);

        /// <summary>
        /// 创建用户
        /// </summary>
        /// <param name="userInfo">用户信息</param>
        /// <returns>创建结果</returns>
        [Post("/api/User/CreateUser")]
        Task<ApiResponse<UserInfo>> CreateUserAsync([Body] UserInfo userInfo);

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="userInfo">用户信息</param>
        /// <returns>更新结果</returns>
        [Put("/api/User/{userId}")]
        Task<ApiResponse<UserInfo>> UpdateUserAsync(string userId, [Body] UserInfo userInfo);

        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>删除结果</returns>
        [Delete("/api/User/{userId}")]
        Task<ApiResponse<bool>> DeleteUserAsync(string userId);

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="changePasswordInfo">修改密码信息</param>
        /// <returns>修改结果</returns>
        [Post("/api/User/ChangePassword")]
        Task<ApiResponse<bool>> ChangePasswordAsync([Body] ChangePasswordInfo changePasswordInfo);

        /// <summary>
        /// 刷新 Token
        /// </summary>
        /// <param name="refreshToken">刷新Token</param>
        /// <returns>新的Token信息</returns>
        [Post("/api/Auth/RefreshToken")]
        Task<ApiResponse<TokenInfo>> RefreshTokenAsync([Body] RefreshTokenRequest refreshToken);

        /// <summary>
        /// 注销登录
        /// </summary>
        /// <returns>注销结果</returns>
        [Post("/api/Auth/Logout")]
        Task<ApiResponse<bool>> LogoutAsync();
    }

    /// <summary>
    /// 分页结果模型
    /// </summary>
    public class PagedResult<T>
    {
        public List<T> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int Size { get; set; }
        public int TotalPages { get; set; }
    }

    /// <summary>
    /// 修改密码信息模型
    /// </summary>
    public class ChangePasswordInfo
    {
        public string OldPassword { get; set; } = string.Empty;
        public string NewPassword { get; set; } = string.Empty;
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// 刷新Token请求模型
    /// </summary>
    public class RefreshTokenRequest
    {
        public string RefreshToken { get; set; } = string.Empty;
    }
} 