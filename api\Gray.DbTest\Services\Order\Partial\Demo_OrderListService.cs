/*
 *所有关于Demo_OrderList类的业务代码应在此处编写
*可使用repository.调用常用方法，获取EF/Dapper等信息
*如果需要事务请使用repository.DbContextBeginTransaction
*也可使用DBServerProvider.手动获取数据库相关信息
*用户信息、权限、角色等使用UserContext.Current操作
*Demo_OrderListService对增、删、改查、导入、导出、审核业务代码扩展参照ServiceFunFilter
*/
using Gray.Core.BaseProvider;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.DomainModels;
using System.Linq;
using Gray.Core.Utilities;
using System.Linq.Expressions;
using Gray.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using Gray.DbTest.IRepositories;

namespace Gray.DbTest.Services
{
    public partial class Demo_OrderListService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IDemo_OrderListRepository _repository;//访问数据库

        [ActivatorUtilitiesConstructor]
        public Demo_OrderListService(
            IDemo_OrderListRepository dbRepository,
            IHttpContextAccessor httpContextAccessor
            )
        : base(dbRepository)
        {
            _httpContextAccessor = httpContextAccessor;
            _repository = dbRepository;
            //多租户会用到这init代码，其他情况可以不用
            //base.Init(dbRepository);
        }
  }
}
