/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "理赔账户金额",TableName = "v_K3LPYE",DBServer = "K3DataTransferDbContext")]
    public partial class v_K3LPYE:K3DataTransferEntity
    {
        /// <summary>
       ///客户代码
       /// </summary>
       [Key]
       [Display(Name ="客户代码")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string 客户代码 { get; set; }

       /// <summary>
       ///客户名称
       /// </summary>
       [Display(Name ="客户名称")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       public string 客户名称 { get; set; }

       /// <summary>
       ///即时总金额
       /// </summary>
       [Display(Name ="即时总金额")]
       [Column(TypeName="numeric")]
       [Editable(true)]
       public decimal? 即时总金额 { get; set; }

       
    }
}