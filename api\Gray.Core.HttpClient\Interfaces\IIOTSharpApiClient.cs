using Refit;
using Gray.Core.GrayHttpClient.Models;

namespace Gray.Core.GrayHttpClient.Interfaces
{
    /// <summary>
    /// IOTSharp 物联网平台 API 客户端接口
    /// 使用 Refit 定义声明式 API 调用
    /// </summary>
    public interface IIOTSharpApiClient
    {
        /// <summary>
        /// 获取设备最新遥测数据
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>IOTSharp API 响应</returns>
        [Get("/api/Devices/{deviceId}/TelemetryLatest")]
        Task<ApiResponse<IOTSharpApiResponse<List<TelemetryData>>>> GetDeviceTelemetryLatestAsync(string deviceId);

        /// <summary>
        /// 获取用户账户信息
        /// </summary>
        /// <returns>用户信息</returns>
        [Get("/api/Account/MyInfo")]
        Task<ApiResponse<IOTSharpApiResponse<IOTSharpUserInfo>>> GetAccountMyInfoAsync();

        /// <summary>
        /// 获取设备信息
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>设备信息</returns>
        [Get("/api/Devices/{deviceId}")]
        Task<ApiResponse<IOTSharpApiResponse<object>>> GetDeviceInfoAsync(string deviceId);

        /// <summary>
        /// 获取设备历史遥测数据
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="keyName">遥测键名</param>
        /// <returns>历史遥测数据</returns>
        [Get("/api/Devices/{deviceId}/TelemetryData")]
        Task<ApiResponse<IOTSharpApiResponse<List<TelemetryData>>>> GetDeviceTelemetryHistoryAsync(
            string deviceId,
            [Query] DateTime? startTime = null,
            [Query] DateTime? endTime = null,
            [Query] string? keyName = null);

        /// <summary>
        /// 发送设备命令
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="command">命令内容</param>
        /// <returns>命令执行结果</returns>
        [Post("/api/Devices/{deviceId}/SendCommand")]
        Task<ApiResponse<IOTSharpApiResponse<object>>> SendDeviceCommandAsync(
            string deviceId, 
            [Body] object command);

        /// <summary>
        /// 设置设备属性
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="attributes">属性数据</param>
        /// <returns>设置结果</returns>
        [Post("/api/Devices/{deviceId}/Attributes")]
        Task<ApiResponse<IOTSharpApiResponse<object>>> SetDeviceAttributesAsync(
            string deviceId, 
            [Body] Dictionary<string, object> attributes);

        /// <summary>
        /// 获取设备特定时间范围内的遥测数据
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="keys">遥测数据键名（多个键用逗号分隔）</param>
        /// <param name="begin">开始时间</param>
        /// <param name="end">结束时间</param>
        /// <returns>遥测数据详细列表</returns>
        [Get("/api/Devices/{deviceId}/TelemetryData/{keys}/{begin}/{end}")]
        Task<ApiResponse<IOTSharpApiResponse<List<TelemetryDataDetail>>>> GetDeviceTelemetryDataRangeAsync(
            string deviceId,
            string keys,
            string begin,
            string end);
    }
} 