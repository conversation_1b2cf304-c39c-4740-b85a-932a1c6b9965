/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "发货数据查询",TableName = "v_k3so",DBServer = "K3DataTransferDbContext")]
    public partial class v_k3so:K3DataTransferEntity
    {
        /// <summary>
       ///发货单编号
       /// </summary>
       [Display(Name ="发货单编号")]
       [MaxLength(255)]
       [Column(TypeName="nvarchar(255)")]
       [Editable(true)]
       public string 发货单编号 { get; set; }

       /// <summary>
       ///发货审核
       /// </summary>
       [Display(Name ="发货审核")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? 发货审核 { get; set; }

       /// <summary>
       ///一审时间
       /// </summary>
       [Display(Name ="一审时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? 一审时间 { get; set; }

       /// <summary>
       ///二审时间
       /// </summary>
       [Display(Name ="二审时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? 二审时间 { get; set; }

       /// <summary>
       ///三审时间
       /// </summary>
       [Display(Name ="三审时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? 三审时间 { get; set; }

       /// <summary>
       ///客户代码
       /// </summary>
       [Display(Name ="客户代码")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string 客户代码 { get; set; }

       /// <summary>
       ///客户名称
       /// </summary>
       [Display(Name ="客户名称")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       public string 客户名称 { get; set; }

       /// <summary>
       ///区域
       /// </summary>
       [Display(Name ="区域")]
       [MaxLength(255)]
       [Column(TypeName="nvarchar(255)")]
       [Editable(true)]
       public string 区域 { get; set; }

       /// <summary>
       ///发货仓库
       /// </summary>
       [Display(Name ="发货仓库")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       public string 发货仓库 { get; set; }

       /// <summary>
       ///物料代码
       /// </summary>
       [Display(Name ="物料代码")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       public string 物料代码 { get; set; }

       /// <summary>
       ///物料名称
       /// </summary>
       [Display(Name ="物料名称")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       public string 物料名称 { get; set; }

       /// <summary>
       ///规格型号
       /// </summary>
       [Display(Name ="规格型号")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string 规格型号 { get; set; }

       /// <summary>
       ///发货类型
       /// </summary>
       [Display(Name ="发货类型")]
       [MaxLength(255)]
       [Column(TypeName="nvarchar(255)")]
       [Editable(true)]
       public string 发货类型 { get; set; }

       /// <summary>
       ///发货数量
       /// </summary>
       [Display(Name ="发货数量")]
       [Column(TypeName="numeric")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public double 发货数量 { get; set; }

       /// <summary>
       ///合同号
       /// </summary>
       [Display(Name ="合同号")]
       [MaxLength(20)]
       [Column(TypeName="varchar(20)")]
       [Editable(true)]
       public string 合同号 { get; set; }

       /// <summary>
       ///行号
       /// </summary>
       [Display(Name ="行号")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int 行号 { get; set; }

       /// <summary>
       ///车牌
       /// </summary>
       [Display(Name ="车牌")]
       [MaxLength(20)]
       [Column(TypeName="varchar(20)")]
       [Editable(true)]
       public string 车牌 { get; set; }

       /// <summary>
       ///通知单内部行号
       /// </summary>
       [Display(Name ="通知单内部行号")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int 通知单内部行号 { get; set; }

       /// <summary>
       ///详单内部行号
       /// </summary>
       [Key]
       [Display(Name ="详单内部行号")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int 详单内部行号 { get; set; }

       /// <summary>
       ///系统订单号
       /// </summary>
       [Display(Name ="系统订单号")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       [Editable(true)]
       public string 订单号 { get; set; }

       
    }
}