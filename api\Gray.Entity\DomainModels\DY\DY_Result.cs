/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "动平衡检测结果",TableName = "DY_Result",DBServer = "JL_DYDbContext")]
    public partial class DY_Result:JL_DYEntity
    {
        /// <summary>
       ///Ro_Total_Rank
       /// </summary>
       [Display(Name ="Ro_Total_Rank")]
       [Column(TypeName="float")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public float Ro_Total_Rank { get; set; }

       /// <summary>
       ///Balance_Total_Rank
       /// </summary>
       [Display(Name ="Balance_Total_Rank")]
       [Column(TypeName="float")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public float Balance_Total_Rank { get; set; }

       /// <summary>
       ///条码
       /// </summary>
       [Display(Name ="条码")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string TyreBarcode { get; set; }

       /// <summary>
       ///代码
       /// </summary>
       [Display(Name ="代码")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string TyreCode { get; set; }

       /// <summary>
       ///规格
       /// </summary>
       [Display(Name ="规格")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       public string TyreSPEC { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Key]
       [Display(Name ="DYResultId")]
       [Column(TypeName="bigint")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public long DYResultId { get; set; }

       /// <summary>
       ///记录时间
       /// </summary>
       [Display(Name ="记录时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public DateTime RecordTime { get; set; }

       /// <summary>
       ///文件路径
       /// </summary>
       [Display(Name ="文件路径")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       public string FilePath { get; set; }

       /// <summary>
       ///文件名
       /// </summary>
       [Display(Name ="文件名")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       public string FileName { get; set; }

       /// <summary>
       ///机台id
       /// </summary>
       [Display(Name ="机台id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid DBMachineId { get; set; }

       /// <summary>
       ///机台
       /// </summary>
       [Display(Name ="机台")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string DBMachine { get; set; }

       /// <summary>
       ///轮毂大小
       /// </summary>
       [Display(Name ="轮毂大小")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string RimSize { get; set; }

       /// <summary>
       ///配方
       /// </summary>
       [Display(Name ="配方")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string DYFormula { get; set; }

       
    }
}