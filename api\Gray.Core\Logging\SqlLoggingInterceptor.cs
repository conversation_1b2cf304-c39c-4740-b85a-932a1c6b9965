using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using System;
using System.Data.Common;
using System.Threading;
using System.Threading.Tasks;
using Gray.Core.Extensions;

namespace Gray.Core.Logging
{
    /// <summary>
    /// SQL日志拦截器 - EF Core官方推荐的SQL日志记录方式
    /// </summary>
    public class SqlLoggingInterceptor : DbCommandInterceptor
    {
        private readonly ILogger<SqlLoggingInterceptor> _logger;
        private readonly SqlLoggingOptions _options;

        public SqlLoggingInterceptor(ILogger<SqlLoggingInterceptor> logger, SqlLoggingOptions options = null)
        {
            _logger = logger;
            _options = options ?? new SqlLoggingOptions();
        }

        #region 同步方法拦截

        public override InterceptionResult<DbDataReader> ReaderExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<DbDataReader> result)
        {
            LogCommand(command, "ReaderExecuting");
            return base.ReaderExecuting(command, eventData, result);
        }

        public override DbDataReader ReaderExecuted(
            DbCommand command,
            CommandExecutedEventData eventData,
            DbDataReader result)
        {
            LogCommandExecuted(command, eventData, "ReaderExecuted");
            return base.ReaderExecuted(command, eventData, result);
        }

        public override InterceptionResult<int> NonQueryExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<int> result)
        {
            LogCommand(command, "NonQueryExecuting");
            return base.NonQueryExecuting(command, eventData, result);
        }

        public override int NonQueryExecuted(
            DbCommand command,
            CommandExecutedEventData eventData,
            int result)
        {
            LogCommandExecuted(command, eventData, "NonQueryExecuted", result);
            return base.NonQueryExecuted(command, eventData, result);
        }

        public override InterceptionResult<object> ScalarExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<object> result)
        {
            LogCommand(command, "ScalarExecuting");
            return base.ScalarExecuting(command, eventData, result);
        }

        public override object ScalarExecuted(
            DbCommand command,
            CommandExecutedEventData eventData,
            object result)
        {
            LogCommandExecuted(command, eventData, "ScalarExecuted", result);
            return base.ScalarExecuted(command, eventData, result);
        }

        #endregion

        #region 异步方法拦截

        public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<DbDataReader> result,
            CancellationToken cancellationToken = default)
        {
            LogCommand(command, "ReaderExecutingAsync");
            return base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
        }

        public override ValueTask<DbDataReader> ReaderExecutedAsync(
            DbCommand command,
            CommandExecutedEventData eventData,
            DbDataReader result,
            CancellationToken cancellationToken = default)
        {
            LogCommandExecuted(command, eventData, "ReaderExecutedAsync");
            return base.ReaderExecutedAsync(command, eventData, result, cancellationToken);
        }

        public override ValueTask<InterceptionResult<int>> NonQueryExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<int> result,
            CancellationToken cancellationToken = default)
        {
            LogCommand(command, "NonQueryExecutingAsync");
            return base.NonQueryExecutingAsync(command, eventData, result, cancellationToken);
        }

        public override ValueTask<int> NonQueryExecutedAsync(
            DbCommand command,
            CommandExecutedEventData eventData,
            int result,
            CancellationToken cancellationToken = default)
        {
            LogCommandExecuted(command, eventData, "NonQueryExecutedAsync", result);
            return base.NonQueryExecutedAsync(command, eventData, result, cancellationToken);
        }

        public override ValueTask<InterceptionResult<object>> ScalarExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<object> result,
            CancellationToken cancellationToken = default)
        {
            LogCommand(command, "ScalarExecutingAsync");
            return base.ScalarExecutingAsync(command, eventData, result, cancellationToken);
        }

        public override ValueTask<object> ScalarExecutedAsync(
            DbCommand command,
            CommandExecutedEventData eventData,
            object result,
            CancellationToken cancellationToken = default)
        {
            LogCommandExecuted(command, eventData, "ScalarExecutedAsync", result);
            return base.ScalarExecutedAsync(command, eventData, result, cancellationToken);
        }

        #endregion

        #region 错误处理

        public override void CommandFailed(DbCommand command, CommandErrorEventData eventData)
        {
            LogCommandError(command, eventData);
            base.CommandFailed(command, eventData);
        }

        public override Task CommandFailedAsync(
            DbCommand command,
            CommandErrorEventData eventData,
            CancellationToken cancellationToken = default)
        {
            LogCommandError(command, eventData);
            return base.CommandFailedAsync(command, eventData, cancellationToken);
        }

        #endregion

        #region 私有日志方法

        private void LogCommand(DbCommand command, string operation)
        {
            if (!_options.LogExecuting) return;

            try
            {
                var logData = new
                {
                    Operation = operation,
                    Timestamp = DateTime.Now,
                    CommandType = command.CommandType,
                    CommandText = _options.IncludeCommandText ? command.CommandText : "[CommandText Hidden]",
                    Parameters = _options.IncludeParameters ? GetParametersInfo(command) : "[Parameters Hidden]",
                    ConnectionId = command.Connection?.GetHashCode()
                };

                if (_options.UseStructuredLogging)
                {
                    _logger.LogInformation("SQL {Operation} - {CommandType}: {CommandText} | Parameters: {Parameters}",
                        operation, command.CommandType, logData.CommandText, logData.Parameters);
                }
                else
                {
                    _logger.LogInformation("SQL执行: {LogData}", logData.Serialize());
                }

                // 写入文件日志
                if (_options.WriteToFile)
                {
                    SqlFileLogger.WriteLog(logData, _options.LogFilePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "SQL日志记录失败");
            }
        }

        private void LogCommandExecuted(DbCommand command, CommandExecutedEventData eventData, string operation, object result = null)
        {
            if (!_options.LogExecuted) return;

            try
            {
                var logData = new
                {
                    Operation = operation,
                    Timestamp = DateTime.Now,
                    Duration = eventData.Duration,
                    CommandType = command.CommandType,
                    CommandText = _options.IncludeCommandText ? command.CommandText : "[CommandText Hidden]",
                    Parameters = _options.IncludeParameters ? GetParametersInfo(command) : "[Parameters Hidden]",
                    Result = _options.IncludeResult ? result?.ToString() : "[Result Hidden]",
                    ConnectionId = command.Connection?.GetHashCode()
                };

                if (_options.UseStructuredLogging)
                {
                    _logger.LogInformation("SQL {Operation} 完成 - 耗时: {Duration}ms | 结果: {Result}",
                        operation, eventData.Duration.TotalMilliseconds, logData.Result);
                }
                else
                {
                    _logger.LogInformation("SQL执行完成: {LogData}", logData.Serialize());
                }

                // 写入文件日志
                if (_options.WriteToFile)
                {
                    SqlFileLogger.WriteLog(logData, _options.LogFilePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "SQL日志记录失败");
            }
        }

        private void LogCommandError(DbCommand command, CommandErrorEventData eventData)
        {
            if (!_options.LogErrors) return;

            try
            {
                var logData = new
                {
                    Operation = "CommandError",
                    Timestamp = DateTime.Now,
                    Duration = eventData.Duration,
                    CommandType = command.CommandType,
                    CommandText = _options.IncludeCommandText ? command.CommandText : "[CommandText Hidden]",
                    Parameters = _options.IncludeParameters ? GetParametersInfo(command) : "[Parameters Hidden]",
                    Error = eventData.Exception?.Message,
                    ConnectionId = command.Connection?.GetHashCode()
                };

                _logger.LogError(eventData.Exception, "SQL执行错误: {LogData}", logData.Serialize());

                // 写入文件日志
                if (_options.WriteToFile)
                {
                    SqlFileLogger.WriteLog(logData, _options.LogFilePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "SQL错误日志记录失败");
            }
        }

        private string GetParametersInfo(DbCommand command)
        {
            if (command.Parameters.Count == 0) return "无参数";

            var parameters = new System.Text.StringBuilder();
            foreach (DbParameter parameter in command.Parameters)
            {
                parameters.AppendLine($"  {parameter.ParameterName}: {parameter.Value} ({parameter.DbType})");
            }
            return parameters.ToString();
        }

        #endregion
    }
}
