/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "调拨单",TableName = "v_k3DBD",DBServer = "K3DataTransferDbContext")]
    public partial class v_k3DBD:K3DataTransferEntity
    {
        /// <summary>
       ///掉入日期
       /// </summary>
       [Display(Name ="掉入日期")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? 调入库日期 { get; set; }

       /// <summary>
       ///代码
       /// </summary>
       [Key]
       [Display(Name ="代码")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string K3code { get; set; }

       /// <summary>
       ///规格
       /// </summary>
       [Display(Name ="规格")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string K3model { get; set; }

       /// <summary>
       ///数量
       /// </summary>
       [Display(Name ="数量")]
       [Column(TypeName="numeric")]
       [Editable(true)]
       public decimal? 数量 { get; set; }

       /// <summary>
       ///调出仓
       /// </summary>
       [Display(Name ="调出仓")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       public string 调出仓库名称 { get; set; }

       /// <summary>
       ///掉入仓
       /// </summary>
       [Display(Name ="掉入仓")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       public string 调入仓库名称 { get; set; }

       
    }
}