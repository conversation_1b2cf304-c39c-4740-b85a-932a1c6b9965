﻿using Gray.Core.Configuration;
using Gray.Core.Const;
using Gray.Core.DBManager;
using Gray.Core.Enums;
using Gray.Core.Extensions;
using Gray.Core.Logging;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Microsoft.Extensions.DependencyModel;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.Loader;
using System.Threading;
using System.Threading.Tasks;

namespace Gray.Core.EFDbContext
{
    /// <summary>
    /// 非泛型基类，主要用于类型检查和反射操作
    /// </summary>
    public abstract class BaseDbContext : DbContext
    {
        public BaseDbContext() : base() { }
        public BaseDbContext(DbContextOptions options) : base(options) { }

        /// <summary>
        /// 获取实体集合，支持逻辑删除过滤
        /// </summary>
        /// <typeparam name="TEntity">实体类型</typeparam>
        /// <param name="filterDeleted">是否过滤逻辑删除的数据</param>
        /// <returns>实体查询集合</returns>
        public virtual IQueryable<TEntity> Set<TEntity>(bool filterDeleted) where TEntity : class
        {
            if (filterDeleted)
            {
                return base.Set<TEntity>().FilterLogicDel();
            }
            return base.Set<TEntity>();
        }
    }

    /// <summary>
    /// 泛型基类，提供类型安全的 DbContext 实现
    /// </summary>
    /// <typeparam name="TContext">具体的 DbContext 类型</typeparam>
    public abstract class BaseDbContext<TContext> : BaseDbContext
        where TContext : BaseDbContext<TContext>
    {
        protected virtual string ConnectionString     
        {
            get
            {
                return DBServerProvider.GetConnection(this.GetType().Name);
            }
        }

        protected DBConnConfig GetDBConnConfig(string dbContextName = null)
        {
            try
            {
                // 直接使用 DBServerProvider 的连接池获取配置
                var c = DBServerProvider.GetConnConfig(dbContextName);

                return c;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取数据库配置失败: {ex.Message}");
                // 出错时返回主库配置
                return DBServerProvider.GetConnConfig(null); // 返回默认配置
            }
        }
        
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // 如果已经配置过，则不再重复配置
            if (optionsBuilder.IsConfigured)
            {
                return;
            }
            
            // 获取当前上下文的类名作为配置键
            string dbContextName = this.GetType().Name;
            
            // 使用配置
            UseDbTypeFromConfig(optionsBuilder, dbContextName);
            
            base.OnConfiguring(optionsBuilder);
        }
        
        protected void UseDbTypeFromConfig(DbContextOptionsBuilder optionsBuilder, string dbContextName = null)
        {
            if (optionsBuilder == null)
            {
                throw new ArgumentNullException(nameof(optionsBuilder));
            }

            var dbConfig = GetDBConnConfig(dbContextName);
            if (dbConfig == null)
            {
                throw new InvalidOperationException($"无法获取数据库配置: {dbContextName ?? "默认配置"}");
            }

            // 使用连接字符串和数据库类型
            UseDbType(optionsBuilder, dbConfig.Connection, dbConfig.DbType, dbConfig.Version);
            
            // 添加性能优化配置
            optionsBuilder
                .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking) // 默认不跟踪实体
                .EnableSensitiveDataLogging(false) // 不记录敏感数据
                .EnableDetailedErrors(true); // 生产环境不需要详细错误

            // 配置SQL日志记录
            ConfigureSqlLogging(optionsBuilder, dbContextName);
        }
        
        public BaseDbContext() : base()
        {
        }

        public BaseDbContext(DbContextOptions<TContext> options) : base(options)
        {
        }

        // 保留向后兼容的非泛型构造函数（过渡期使用）
        public BaseDbContext(DbContextOptions options) : base(options)
        {
        }

        /// <summary>
        /// 获取实体集合，支持逻辑删除过滤
        /// </summary>
        /// <typeparam name="TEntity">实体类型</typeparam>
        /// <param name="filterDeleted">是否过滤逻辑删除的数据</param>
        /// <returns>实体查询集合</returns>
        public new IQueryable<TEntity> Set<TEntity>(bool filterDeleted) where TEntity : class
        {
            if (filterDeleted)
            {
                return base.Set<TEntity>().FilterLogicDel();
            }
            return base.Set<TEntity>();
        }



        protected void UseDbType(DbContextOptionsBuilder optionsBuilder, string connectionString, string dbType = null, string dbversion = "")
        {
            DbTypeConfigurer.ConfigureDbType(
                optionsBuilder,
                connectionString,
                dbType,
                dbversion,
                enableDetailedLogging: false);  // 默认不启用详细日志，可以在派生类中覆盖
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            if (modelBuilder == null)
            {
                throw new ArgumentNullException(nameof(modelBuilder));
            }

            try
            {
                // 获取当前上下文的基础实体类型
                Type baseEntityType = GetBaseEntityType();
                
                if (baseEntityType != null)
                {
                    // 1. 从程序集加载实体类型
                    LoadEntitiesFromAssemblies(modelBuilder, baseEntityType);
                    
                    // 2. 从缓存中加载插件实体
                    LoadEntitiesFromCache(modelBuilder, baseEntityType);
                }
                
                // 3. 配置Oracle数据库特定设置
                ConfigureOracleSpecificSettings(modelBuilder);

                // 4. 配置SQL Server特定设置，修复Bool到Bit映射问题
                ConfigureSqlServerSpecificSettings(modelBuilder);

                base.OnModelCreating(modelBuilder);
            }
            catch (Exception ex)
            {
                string mapPath = ($"Log/").MapPath();
                Utilities.FileHelper.WriteFile(
                    mapPath, 
                    $"syslog_{DateTime.Now:yyyyMMddHHmmss}.txt", 
                    ex.Message + ex.StackTrace + ex.Source
                );
                
                // 重新抛出异常以便上层处理
                throw new InvalidOperationException("创建数据模型时出错", ex);
            }
        }

        /// <summary>
        /// 获取当前上下文使用的基础实体类型
        /// 派生类可以覆盖此方法以提供特定的实体基类
        /// </summary>
        protected virtual Type GetBaseEntityType()
        {
            // 默认返回null，由派生类实现
            return null;
        }

        private void LoadEntitiesFromAssemblies(ModelBuilder modelBuilder, Type baseType)
        {
            if (baseType == null) return;
            
            Console.WriteLine($"开始从程序集加载实体类型，基类: {baseType.FullName}");
            
            try
            {
                // 1. 获取所有已加载的程序集，但只选择可能包含实体的程序集
                var loadedAssemblies = AppDomain.CurrentDomain.GetAssemblies()
                    .Where(a => !a.IsDynamic && 
                               !a.GetName().Name.StartsWith("System.") && 
                               !a.GetName().Name.StartsWith("Microsoft.") &&
                               !a.GetName().Name.StartsWith("Newtonsoft.") &&
                               !a.GetName().Name.StartsWith("PluginCore") &&
                               (a.GetName().Name.Contains("Entity") || 
                                a.GetName().Name.StartsWith("Gray.") || 
                                a.GetName().Name.StartsWith("JL.")))
                    .ToList();
                
                Console.WriteLine($"找到 {loadedAssemblies.Count} 个可能包含实体的程序集进行扫描");
                
                // 2. 从所有程序集中查找实体类型
                var entityTypes = new List<Type>();
                foreach (var assembly in loadedAssemblies)
                {
                    try
                    {
                        // 获取程序集中的所有类型，但只选择可能是实体的类型
                        // 通过命名空间和基类名称进行初步筛选，减少需要深入检查的类型数量
                        var typesInAssembly = assembly.GetTypes()
                            .Where(t => t.IsClass && 
                                       !t.IsAbstract && 
                                       (t.Namespace?.Contains("Entity") == true || 
                                        t.Namespace?.Contains("Models") == true) &&
                                       t.BaseType != null &&
                                       (t.BaseType.Name.EndsWith("Entity") || 
                                        (t.BaseType.BaseType != null && t.BaseType.BaseType.Name.EndsWith("Entity"))))
                            .ToList();
                        
                        // 查找直接或间接继承自 baseType 的类型
                        foreach (var type in typesInAssembly)
                        {
                            if (IsEntityType(type, baseType))
                            {
                                entityTypes.Add(type);
                                Console.WriteLine($"找到实体类型: {type.FullName}, 程序集: {assembly.GetName().Name}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"扫描程序集 {assembly.GetName().Name} 时出错: {ex.Message}");
                        // 继续处理其他程序集
                    }
                }
                
                // 3. 从 DependencyContext 中加载项目引用的程序集中的实体
                // 但只处理可能包含实体的程序集
                try
                {
                    var compilationLibrary = DependencyContext
                        .Default
                        ?.RuntimeLibraries
                        .Where(x => !x.Serviceable && 
                                   x.Type == "project" && 
                                   (x.Name.Contains("Entity") || 
                                    x.Name.StartsWith("Gray.") || 
                                    x.Name.StartsWith("JL.")));
                    
                    if (compilationLibrary != null)
                    {
                        foreach (var compilation in compilationLibrary)
                        {
                            try
                            {
                                var assembly = AssemblyLoadContext.Default.LoadFromAssemblyName(new AssemblyName(compilation.Name));
                                
                                // 获取程序集中的所有类型，但只选择可能是实体的类型
                                var typesInAssembly = assembly.GetTypes()
                                    .Where(t => t.IsClass && 
                                               !t.IsAbstract && 
                                               (t.Namespace?.Contains("Entity") == true || 
                                                t.Namespace?.Contains("Models") == true) &&
                                               t.BaseType != null &&
                                               (t.BaseType.Name.EndsWith("Entity") || 
                                                (t.BaseType.BaseType != null && t.BaseType.BaseType.Name.EndsWith("Entity"))))
                                    .ToList();
                                
                                // 查找直接或间接继承自 baseType 的类型
                                foreach (var type in typesInAssembly)
                                {
                                    if (IsEntityType(type, baseType) && !entityTypes.Contains(type))
                                    {
                                        entityTypes.Add(type);
                                        Console.WriteLine($"从项目引用中找到实体类型: {type.FullName}, 程序集: {assembly.GetName().Name}");
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"加载项目引用程序集 {compilation.Name} 时出错: {ex.Message}");
                                // 继续处理其他程序集
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"处理 DependencyContext 时出错: {ex.Message}");
                    // 继续处理
                }
                
                // 4. 注册找到的所有实体类型
                Console.WriteLine($"共找到 {entityTypes.Count} 个实体类型，开始注册到 ModelBuilder");
                foreach (var entityType in entityTypes)
                {
                    try
                    {
                        // 使用反射调用 modelBuilder.Entity<T>()
                        var entityMethod = typeof(ModelBuilder).GetMethod("Entity", Type.EmptyTypes);
                        var genericMethod = entityMethod.MakeGenericMethod(entityType);
                        genericMethod.Invoke(modelBuilder, null);
                        
                        // 同时注册到 EntityTypeCache
                        EntityTypeCache.UpdateEntityType(entityType.Name, entityType);
                        Console.WriteLine($"已注册实体: {entityType.Name}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"注册实体 {entityType.Name} 时出错: {ex.Message}");
                        // 继续处理其他实体
                    }
                }
                
                Console.WriteLine("实体类型加载完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载实体类型时出现未处理的异常: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                // 重新抛出异常以便上层处理
                throw;
            }
        }
        
        /// <summary>
        /// 检查类型是否是实体类型（直接或间接继承自指定的基类）
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <param name="baseType">基类类型</param>
        /// <returns>如果是实体类型则返回 true，否则返回 false</returns>
        private bool IsEntityType(Type type, Type baseType)
        {
            if (type == null || baseType == null)
                return false;
                
            // 检查直接继承
            if (type.BaseType == baseType)
                return true;
                
            // 检查类型名称（处理跨程序集加载的情况）
            if (type.BaseType?.Name == baseType.Name && 
                type.BaseType?.Namespace == baseType.Namespace)
                return true;
                
            // 递归检查间接继承
            var currentBaseType = type.BaseType;
            while (currentBaseType != null && currentBaseType != typeof(object))
            {
                // 检查类型引用相等
                if (currentBaseType == baseType)
                    return true;
                    
                // 检查类型名称相等（处理跨程序集加载的情况）
                if (currentBaseType.Name == baseType.Name && 
                    currentBaseType.Namespace == baseType.Namespace)
                    return true;
                    
                // 继续检查上一级基类
                currentBaseType = currentBaseType.BaseType;
            }
            
            return false;
        }

        private void LoadEntitiesFromCache(ModelBuilder modelBuilder, Type baseType)
        {
            if (baseType == null) return;
            
            // 从缓存中加载基本实体类型，但只加载那些尚未注册的实体
            var registeredTypes = modelBuilder.Model.GetEntityTypes().Select(e => e.ClrType).ToHashSet();
            
            foreach (var entityType in EntityTypeCache.GetEntityTypes(baseType))
            {
                // 只有当实体类型尚未注册时才进行注册
                if (!registeredTypes.Contains(entityType))
                {
                    modelBuilder.Entity(entityType);
                    Console.WriteLine($"从缓存注册新的插件实体: {entityType.Name}");
                }
            }
            
            // 加载额外的实体类型（例如通过接口标记的实体）
            LoadAdditionalEntitiesFromCache(modelBuilder);
        }
        
        /// <summary>
        /// 从缓存中加载额外的实体类型
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        protected virtual void LoadAdditionalEntitiesFromCache(ModelBuilder modelBuilder)
        {
            // 默认实现为空，派生类可以覆盖此方法以加载特定的额外实体
            // 例如，可以通过接口或特性标记的实体
        }

        private void ConfigureOracleSpecificSettings(ModelBuilder modelBuilder)
        {
            if (DBType.Name == DbCurrentType.Oracle.ToString())
            {
                foreach (var entity in modelBuilder.Model.GetEntityTypes())
                {
                    string tableName = entity.GetTableName().ToUpper();
                    entity.SetTableName(tableName);

                    foreach (var property in entity.GetProperties())
                    {
                        property.SetColumnName(property.Name.ToUpper());

                        if (property.ClrType == typeof(Guid))
                        {
                            property.SetValueConverter(new ValueConverter<Guid, string>(
                                v => v.ToString(),
                                v => new Guid(v)
                            ));
                        }
                        else if (property.ClrType == typeof(Guid?))
                        {
                            property.SetValueConverter(new ValueConverter<Guid?, string>(
                                v => v.ToString(),
                                v => new Guid(v)
                            ));
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 配置SQL Server特定设置，修复Bool到Bit映射问题
        /// </summary>
        private void ConfigureSqlServerSpecificSettings(ModelBuilder modelBuilder)
        {
            if (DBType.Name == DbCurrentType.MsSql.ToString())
            {
                foreach (var entity in modelBuilder.Model.GetEntityTypes())
                {
                    foreach (var property in entity.GetProperties())
                    {
                        // 确保bool类型正确映射到SQL Server的bit类型
                        if (property.ClrType == typeof(bool) || property.ClrType == typeof(bool?))
                        {
                            // 显式配置bool到bit的映射，确保false=0, true=1
                            property.SetColumnType("bit");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 配置SQL日志记录
        /// </summary>
        private void ConfigureSqlLogging(DbContextOptionsBuilder optionsBuilder, string dbContextName)
        {
            try
            {
                // 从配置文件读取SQL日志配置
                var sqlLoggingEnabled = AppSetting.Configuration["SqlLogging:Enabled"]?.ToLower() == "true";
                if (!sqlLoggingEnabled) return;

                // 根据环境配置不同的日志选项
                var environment = AppSetting.Configuration["Environment"] ?? "Development";
                SqlLoggingOptions options = environment.ToLower() switch
                {
                    "development" => SqlLoggingOptions.Development,
                    "production" => SqlLoggingOptions.Production,
                    "staging" => SqlLoggingOptions.Performance,
                    _ => new SqlLoggingOptions { Enabled = true }
                };

                // 从配置文件覆盖特定设置
                if (bool.TryParse(AppSetting.Configuration["SqlLogging:IncludeParameters"], out bool includeParams))
                    options.IncludeParameters = includeParams;

                if (bool.TryParse(AppSetting.Configuration["SqlLogging:WriteToFile"], out bool writeToFile))
                    options.WriteToFile = writeToFile;

                if (int.TryParse(AppSetting.Configuration["SqlLogging:SlowQueryThresholdMs"], out int threshold))
                    options.SlowQueryThresholdMs = threshold;

                // 应用SQL日志配置
                optionsBuilder.AddSqlLogging(options);
            }
            catch (Exception ex)
            {
                // SQL日志配置失败不应影响主业务
                Console.WriteLine($"SQL日志配置失败: {ex.Message}");
            }
        }


    }
}