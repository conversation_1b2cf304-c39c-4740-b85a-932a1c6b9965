/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "动平衡PLC设置",TableName = "DY_PLCAddress",DBServer = "JL_DYDbContext")]
    public partial class DY_PLCAddress:JL_DYEntity
    {
        /// <summary>
       ///机台名
       /// </summary>
       [Display(Name ="机台名")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       public string DBMachine { get; set; }

       /// <summary>
       ///机台id
       /// </summary>
       [Key]
       [Display(Name ="机台id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid DBMachineId { get; set; }

       /// <summary>
       ///PLC地址
       /// </summary>
       [Display(Name ="PLC地址")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       public string PLCIPAddress { get; set; }

       /// <summary>
       ///PLC端口
       /// </summary>
       [Display(Name ="PLC端口")]
       [MaxLength(22)]
       [Column(TypeName="nvarchar(22)")]
       [Editable(true)]
       public string PLCPort { get; set; }

       
    }
}