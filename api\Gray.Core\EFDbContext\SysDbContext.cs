﻿using Microsoft.EntityFrameworkCore;
using Gray.Core.DBManager;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.SystemModels;
using System;

namespace Gray.Core.EFDbContext
{
    public class SysDbContext : BaseDbContext<SysDbContext>, IDependency
    {
        protected override string ConnectionString
        {
            get
            {
                return DBServerProvider.SysConnectingString;
            }
        }
        public SysDbContext() : base()
        {
        }

        public SysDbContext(DbContextOptions<SysDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.UseDbTypeFromConfig(optionsBuilder);
            //默认禁用实体跟踪
            optionsBuilder = optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            base.OnConfiguring(optionsBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            //foreach (var entity in modelBuilder.Model.GetEntityTypes())
            //{
            //    //重置系统表名小写
            //    if (entity.GetTableName().StartsWith("Sys_"))
            //    {
            //        entity.SetTableName(entity.GetTableName().ToLower());
            //    }
            //    //// 重置所有列名
            //    //foreach (var property in entity.GetProperties())
            //    //{
            //    //    //StoreObjectIdentifier
            //    //    property.SetColumnName(property.Name);
            //    //}
            //}

            base.OnModelCreating(modelBuilder);
        }

        protected override Type GetBaseEntityType()
        {
            // 默认返回null，由派生类实现
            return typeof(SysEntity);
        }
    }
}