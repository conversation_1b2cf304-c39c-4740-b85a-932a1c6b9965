using Gray.Core.GrayHttpClient.Models;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.IdentityModel.Tokens.Jwt;
using System.Text;
using System.Text.Json;

namespace Gray.Core.GrayHttpClient.Services
{
    /// <summary>
    /// 通用Token管理器实现
    /// 支持多系统JWT认证的统一管理
    /// </summary>
    public class UniversalTokenManager : IUniversalTokenManager
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IMemoryCache _cache;
        private readonly ILogger<UniversalTokenManager> _logger;
        private readonly HttpClientOptions _options;
        private readonly SemaphoreSlim _refreshSemaphore = new(1, 1);

        public UniversalTokenManager(
            IHttpClientFactory httpClientFactory,
            IMemoryCache cache,
            ILogger<UniversalTokenManager> logger,
            IOptions<HttpClientOptions> options)
        {
            _httpClientFactory = httpClientFactory;
            _cache = cache;
            _logger = logger;
            _options = options.Value;
        }

        /// <summary>
        /// 获取指定系统的访问Token
        /// </summary>
        public async Task<string?> GetAccessTokenAsync(string systemName)
        {
            if (!IsSystemConfigured(systemName))
            {
                _logger.LogWarning("系统 {SystemName} 未配置", systemName);
                return null;
            }

            var cacheKey = GetTokenCacheKey(systemName);
            var token = _cache.Get<string>(cacheKey);

            // 如果Token不存在或即将过期，尝试获取新Token
            if (string.IsNullOrEmpty(token) || await IsTokenExpiringSoonAsync(systemName))
            {
                await _refreshSemaphore.WaitAsync();
                try
                {
                    // 双重检查，防止并发获取
                    token = _cache.Get<string>(cacheKey);
                    if (string.IsNullOrEmpty(token) || await IsTokenExpiringSoonAsync(systemName))
                    {
                        var success = await AcquireTokenAsync(systemName);
                        if (success)
                        {
                            token = _cache.Get<string>(cacheKey);
                        }
                    }
                }
                finally
                {
                    _refreshSemaphore.Release();
                }
            }

            return token;
        }

        /// <summary>
        /// 检查指定系统的Token是否有效
        /// </summary>
        public async Task<bool> IsTokenValidAsync(string systemName)
        {
            if (!IsSystemConfigured(systemName))
            {
                return false;
            }

            var token = _cache.Get<string>(GetTokenCacheKey(systemName));
            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            var expiryTime = _cache.Get<DateTime?>(GetExpiryTimeCacheKey(systemName));
            if (expiryTime.HasValue && expiryTime.Value <= DateTime.UtcNow)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 刷新指定系统的Token
        /// </summary>
        public async Task<bool> RefreshTokenAsync(string systemName)
        {
            if (!IsSystemConfigured(systemName))
            {
                _logger.LogWarning("系统 {SystemName} 未配置", systemName);
                return false;
            }

            var systemConfig = _options.AuthenticationSystems[systemName];
            if (!systemConfig.AutoRefreshToken)
            {
                _logger.LogDebug("系统 {SystemName} 未启用自动刷新Token", systemName);
                return false;
            }

            var refreshToken = _cache.Get<string>(GetRefreshTokenCacheKey(systemName));
            if (string.IsNullOrEmpty(refreshToken))
            {
                _logger.LogWarning("系统 {SystemName} 的刷新Token为空，尝试重新获取Token", systemName);
                return await AcquireTokenAsync(systemName);
            }

            try
            {
                var httpClient = GetHttpClientForSystem(systemName);
                var refreshRequest = BuildRefreshTokenRequest(systemConfig, refreshToken);
                
                var response = await httpClient.SendAsync(refreshRequest);
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return ProcessTokenResponse(systemName, systemConfig, responseContent);
                }
                else
                {
                    _logger.LogWarning("系统 {SystemName} Token刷新失败，状态码: {StatusCode}", systemName, response.StatusCode);
                    // 刷新失败，尝试重新获取Token
                    return await AcquireTokenAsync(systemName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "系统 {SystemName} Token刷新时发生异常", systemName);
                return false;
            }
        }

        /// <summary>
        /// 清除指定系统的Token
        /// </summary>
        public async Task ClearTokenAsync(string systemName)
        {
            _cache.Remove(GetTokenCacheKey(systemName));
            _cache.Remove(GetRefreshTokenCacheKey(systemName));
            _cache.Remove(GetExpiryTimeCacheKey(systemName));
            
            _logger.LogDebug("已清除系统 {SystemName} 的Token", systemName);
            await Task.CompletedTask;
        }

        /// <summary>
        /// 清除所有系统的Token
        /// </summary>
        public async Task ClearAllTokensAsync()
        {
            foreach (var systemName in GetConfiguredSystems())
            {
                await ClearTokenAsync(systemName);
            }
            _logger.LogInformation("已清除所有系统的Token");
        }

        /// <summary>
        /// 获取所有已配置的系统名称
        /// </summary>
        public IEnumerable<string> GetConfiguredSystems()
        {
            return _options.AuthenticationSystems.Keys.Where(key => 
                _options.AuthenticationSystems[key].Enabled);
        }

        /// <summary>
        /// 检查指定系统是否已配置
        /// </summary>
        public bool IsSystemConfigured(string systemName)
        {
            return _options.AuthenticationSystems.ContainsKey(systemName) && 
                   _options.AuthenticationSystems[systemName].Enabled;
        }

        #region 私有方法

        /// <summary>
        /// 获取Token
        /// </summary>
        private async Task<bool> AcquireTokenAsync(string systemName)
        {
            try
            {
                var systemConfig = _options.AuthenticationSystems[systemName];
                var httpClient = GetHttpClientForSystem(systemName);
                var loginRequest = BuildLoginRequest(systemConfig);

                var response = await httpClient.SendAsync(loginRequest);
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var success = ProcessTokenResponse(systemName, systemConfig, responseContent);
                    
                    if (success)
                    {
                        _logger.LogInformation("系统 {SystemName} Token获取成功", systemName);
                    }
                    else
                    {
                        _logger.LogWarning("系统 {SystemName} Token获取失败：响应解析错误", systemName);
                    }
                    
                    return success;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning("系统 {SystemName} Token获取失败，状态码: {StatusCode}, 响应: {Response}", 
                        systemName, response.StatusCode, errorContent);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "系统 {SystemName} Token获取时发生异常", systemName);
                return false;
            }
        }

        /// <summary>
        /// 构建登录请求
        /// </summary>
        private HttpRequestMessage BuildLoginRequest(AuthenticationSystemOptions systemConfig)
        {
            var request = new HttpRequestMessage(
                new HttpMethod(systemConfig.RequestFormat.Method),
                systemConfig.LoginEndpoint);

            // 设置Content-Type
            request.Headers.Add("Accept", "application/json");

            // 构建请求体
            var body = BuildRequestBody(systemConfig.RequestFormat.BodyTemplate, systemConfig.Credentials);
            
            if (systemConfig.RequestFormat.ContentType == "application/json")
            {
                request.Content = new StringContent(
                    JsonSerializer.Serialize(body), 
                    Encoding.UTF8, 
                    "application/json");
            }
            else if (systemConfig.RequestFormat.ContentType == "application/x-www-form-urlencoded")
            {
                var formData = body.Select(kv => new KeyValuePair<string, string>(kv.Key, kv.Value?.ToString() ?? ""));
                request.Content = new FormUrlEncodedContent(formData);
            }

            // 添加自定义请求头
            foreach (var header in systemConfig.RequestFormat.HeaderTemplate)
            {
                request.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            return request;
        }

        /// <summary>
        /// 构建刷新Token请求
        /// </summary>
        private HttpRequestMessage BuildRefreshTokenRequest(AuthenticationSystemOptions systemConfig, string refreshToken)
        {
            var request = new HttpRequestMessage(
                new HttpMethod(systemConfig.RequestFormat.Method),
                systemConfig.RefreshEndpoint);

            request.Headers.Add("Accept", "application/json");

            // 构建刷新Token的请求体
            var body = new Dictionary<string, object>
            {
                ["refresh_token"] = refreshToken,
                ["grant_type"] = "refresh_token"
            };

            if (systemConfig.RequestFormat.ContentType == "application/json")
            {
                request.Content = new StringContent(
                    JsonSerializer.Serialize(body), 
                    Encoding.UTF8, 
                    "application/json");
            }
            else
            {
                var formData = body.Select(kv => new KeyValuePair<string, string>(kv.Key, kv.Value?.ToString() ?? ""));
                request.Content = new FormUrlEncodedContent(formData);
            }

            return request;
        }

        /// <summary>
        /// 构建请求体
        /// </summary>
        private Dictionary<string, object> BuildRequestBody(
            Dictionary<string, object> template, 
            AuthenticationCredentials credentials)
        {
            var body = new Dictionary<string, object>();

            foreach (var item in template)
            {
                var value = item.Value?.ToString() ?? "";
                
                // 替换占位符
                value = value.Replace("{Username}", credentials.Username ?? "");
                value = value.Replace("{Password}", credentials.Password ?? "");
                value = value.Replace("{ClientId}", credentials.ClientId ?? "");
                value = value.Replace("{ClientSecret}", credentials.ClientSecret ?? "");
                value = value.Replace("{ApiKey}", credentials.ApiKey ?? "");

                // 替换自定义参数
                foreach (var customParam in credentials.CustomParameters)
                {
                    value = value.Replace($"{{{customParam.Key}}}", customParam.Value);
                }

                body[item.Key] = value;
            }

            return body;
        }

        /// <summary>
        /// 处理Token响应
        /// </summary>
        private bool ProcessTokenResponse(string systemName, AuthenticationSystemOptions systemConfig, string responseContent)
        {
            try
            {
                using var document = JsonDocument.Parse(responseContent);
                var root = document.RootElement;

                // 提取访问Token（支持嵌套字段）
                var accessToken = GetNestedJsonValue(root, systemConfig.TokenMapping.AccessTokenField);
                if (!string.IsNullOrEmpty(accessToken))
                {
                    _cache.Set(GetTokenCacheKey(systemName), accessToken, TimeSpan.FromHours(24));

                    // 提取刷新Token（支持嵌套字段）
                    var refreshToken = GetNestedJsonValue(root, systemConfig.TokenMapping.RefreshTokenField);
                    if (!string.IsNullOrEmpty(refreshToken))
                    {
                        _cache.Set(GetRefreshTokenCacheKey(systemName), refreshToken, TimeSpan.FromDays(30));
                    }

                    // 提取过期时间（支持嵌套字段）
                    DateTime expiryTime = DateTime.UtcNow.AddHours(1); // 默认1小时
                    var expiresInStr = GetNestedJsonValue(root, systemConfig.TokenMapping.ExpiresInField);
                    if (!string.IsNullOrEmpty(expiresInStr) && int.TryParse(expiresInStr, out var expiresIn))
                    {
                        expiryTime = DateTime.UtcNow.AddSeconds(expiresIn);
                    }
                    else
                    {
                        // 尝试从JWT Token中解析过期时间
                        try
                        {
                            var handler = new JwtSecurityTokenHandler();
                            var jsonToken = handler.ReadJwtToken(accessToken);
                            expiryTime = jsonToken.ValidTo;
                        }
                        catch
                        {
                            // 忽略JWT解析错误，使用默认过期时间
                        }
                    }

                    _cache.Set(GetExpiryTimeCacheKey(systemName), expiryTime, TimeSpan.FromDays(1));

                    _logger.LogInformation("系统 {SystemName} Token解析成功，过期时间: {ExpiryTime}", systemName, expiryTime);
                    return true;
                }

                _logger.LogWarning("系统 {SystemName} 响应中未找到访问Token字段: {Field}",
                    systemName, systemConfig.TokenMapping.AccessTokenField);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "系统 {SystemName} Token响应解析失败，响应内容: {Response}", systemName, responseContent);
                return false;
            }
        }

        /// <summary>
        /// 获取嵌套JSON字段值
        /// 支持 "data.token.access_token" 这样的嵌套路径
        /// </summary>
        private string? GetNestedJsonValue(JsonElement root, string fieldPath)
        {
            try
            {
                var parts = fieldPath.Split('.');
                var current = root;

                foreach (var part in parts)
                {
                    if (current.TryGetProperty(part, out var nextElement))
                    {
                        current = nextElement;
                    }
                    else
                    {
                        return null;
                    }
                }

                return current.ValueKind == JsonValueKind.String ? current.GetString() :
                       current.ValueKind == JsonValueKind.Number ? current.ToString() :
                       current.ValueKind == JsonValueKind.True ? "true" :
                       current.ValueKind == JsonValueKind.False ? "false" : null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 检查Token是否即将过期
        /// </summary>
        private async Task<bool> IsTokenExpiringSoonAsync(string systemName)
        {
            var systemConfig = _options.AuthenticationSystems[systemName];
            var expiryTime = _cache.Get<DateTime?>(GetExpiryTimeCacheKey(systemName));
            
            if (!expiryTime.HasValue)
            {
                return true;
            }

            var thresholdTime = DateTime.UtcNow.AddMinutes(systemConfig.TokenRefreshThresholdMinutes);
            return expiryTime.Value <= thresholdTime;
        }

        /// <summary>
        /// 获取系统对应的HttpClient
        /// 注意：这里创建的HttpClient不应该包含认证处理器，避免循环依赖
        /// </summary>
        private HttpClient GetHttpClientForSystem(string systemName)
        {
            // 查找对应的API端点配置
            var endpointKv = _options.ApiEndpoints.FirstOrDefault(kv => kv.Value.AuthenticationSystem == systemName);
            if (endpointKv.Value != null)
            {
                // 为登录请求创建一个简单的HttpClient，不包含认证处理器
                var httpClient = _httpClientFactory.CreateClient();
                var endpoint = endpointKv.Value;
                
                // 设置基本配置
                httpClient.BaseAddress = new Uri(endpoint.BaseUrl);
                httpClient.Timeout = TimeSpan.FromSeconds(endpoint.Timeout);
                
                // 添加基本请求头
                foreach (var header in endpoint.Headers)
                {
                    httpClient.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
                }
                
                return httpClient;
            }

            // 如果没有找到对应的端点，创建一个基础的HttpClient
            var basicHttpClient = _httpClientFactory.CreateClient();
            
            // 为基础HttpClient设置合理的超时时间
            basicHttpClient.Timeout = TimeSpan.FromSeconds(30);
            
            return basicHttpClient;
        }

        private string GetTokenCacheKey(string systemName) => $"UniversalToken:{systemName}:AccessToken";
        private string GetRefreshTokenCacheKey(string systemName) => $"UniversalToken:{systemName}:RefreshToken";
        private string GetExpiryTimeCacheKey(string systemName) => $"UniversalToken:{systemName}:ExpiryTime";

        #endregion

        public void Dispose()
        {
            _refreshSemaphore?.Dispose();
        }
    }
}
