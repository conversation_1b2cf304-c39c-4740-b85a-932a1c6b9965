﻿using Gray.Builder.IRepositories;
using Gray.Core.BaseProvider;
using Gray.Core.EFDbContext;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.DomainModels;
using Gray.Core.UnitOfWorkMange;
namespace Gray.Builder.Repositories
{
    public partial class Sys_TableInfoRepository : RepositoryBase<Sys_TableInfo>, ISys_TableInfoRepository
    {
        public Sys_TableInfoRepository(IUnitOfWork dbContext)
        : base(dbContext)
        {

        }
        public static ISys_TableInfoRepository GetService
        {
            get { return AutofacContainerModule.GetService<ISys_TableInfoRepository>(); }
        }
    }
}

