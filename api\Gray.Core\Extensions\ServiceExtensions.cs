using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using System;
using System.Linq;
using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Gray.Core.EFDbContext;
using System.Runtime.Loader;
using Microsoft.Extensions.DependencyModel;
using Gray.Core.Configuration;
using Gray.Core.DBManager;
using Gray.Core.UnitOfWorkMange;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.EntityFrameworkCore.Internal;

namespace Gray.Core.Extensions
{
    public static class ServiceExtensions
    {
        public static IServiceCollection AddAllDbContexts(
            this IServiceCollection services,
        IConfiguration configuration)

        {
            services.AddScoped<IDbContextFactory, DbContextFactory>();
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            try
            {
                // 1. 注册 SysDbContext
                services.AddDbContext<SysDbContext>((provider, options) =>
                {
                    var config = AppSetting._connection.MainDb;
                    DbTypeConfigurer.ConfigureDbType(
                        options,
                        config.Connection,
                        config.DbType,
                        config.Version,
                        enableDetailedLogging: true
                    );
                }, ServiceLifetime.Scoped);

                // 2. 注册 ServiceDbContext
                //services.AddDbContext<ServiceDbContext>((provider, options) =>
                //{
                //    var config = AppSetting._connection.MainDb;
                //    DbTypeConfigurer.ConfigureDbType(
                //        options,
                //        config.Connection,
                //        config.DbType,
                //        config.Version,
                //        enableDetailedLogging: true
                //    );
                //}, ServiceLifetime.Scoped);

                // 3. 获取并注册其他 DbContext
               

                return services;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"注册数据库上下文时发生错误: {ex.Message}");
                throw;
            }
        }


        
    }
}