﻿using Microsoft.EntityFrameworkCore;
using Gray.Core.DBManager;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.SystemModels;
using System;

namespace Gray.Core.EFDbContext
{
    public class JL_ITDbContext : BaseDbContext<JL_ITDbContext>, IDependency
    {
        protected override string ConnectionString
        {
            get
            {
                return DBServerProvider.GetConnection(this.GetType().Name);
            }
        }

        public JL_ITDbContext() : base()
        {
        }

        public JL_ITDbContext(DbContextOptions<JL_ITDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
           base.UseDbTypeFromConfig(optionsBuilder, this.GetType().Name);
          

            base.OnConfiguring(optionsBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
        }

        protected override Type GetBaseEntityType()
        {
            // 默认返回null，由派生类实现
            return typeof(JL_ITEntity);
        }
    }
}