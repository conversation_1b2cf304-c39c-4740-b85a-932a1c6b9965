/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "硫化日计划",TableName = "CurePlanDaily",DBServer = "JL_APSDbContext")]
    public partial class CurePlanDaily:JL_APSEntity
    {
        /// <summary>
       ///机台
       /// </summary>
       [Display(Name ="机台")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string 机台 { get; set; }

       /// <summary>
       ///位置
       /// </summary>
       [Display(Name ="位置")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string 位置 { get; set; }

       /// <summary>
       ///K3代码
       /// </summary>
       [Display(Name ="K3代码")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string K3代码 { get; set; }

       /// <summary>
       ///日期
       /// </summary>
       [Display(Name ="日期")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? 日期 { get; set; }

       /// <summary>
       ///施工编号
       /// </summary>
       [Display(Name ="施工编号")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string 施工编号 { get; set; }

       /// <summary>
       ///K3规格
       /// </summary>
       [Display(Name ="K3规格")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string K3规格 { get; set; }

       /// <summary>
       ///硫化时间
       /// </summary>
       [Display(Name ="硫化时间")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? 硫化时间 { get; set; }

       /// <summary>
       ///日计划数量
       /// </summary>
       [Display(Name ="日计划数量")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? 日计划数量 { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Key]
       [Display(Name ="Id")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int Id { get; set; }

       
    }
}