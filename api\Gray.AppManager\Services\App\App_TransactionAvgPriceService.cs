/*
Tel:15662337743
 *所有业务编写全部应在Partial文件夹下App_TransactionAvgPriceService与IApp_TransactionAvgPriceService中编写
 */
using Gray.AppManager.IRepositories;
using Gray.AppManager.IServices;
using Gray.Core.BaseProvider;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.DomainModels;

namespace Gray.AppManager.Services
{
    public partial class App_TransactionAvgPriceService : ServiceBase<App_TransactionAvgPrice, IApp_TransactionAvgPriceRepository>, IApp_TransactionAvgPriceService, IDependency
    {
        public App_TransactionAvgPriceService(IApp_TransactionAvgPriceRepository repository)
             : base(repository) 
        { 
           Init(repository);
        }
        public static IApp_TransactionAvgPriceService Instance
        {
           get { return AutofacContainerModule.GetService<IApp_TransactionAvgPriceService>(); }
        }
    }
}
