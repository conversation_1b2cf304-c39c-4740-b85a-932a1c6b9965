using Gray.Core.UnitOfWorkMange;
using Gray.Core.EFDbContext;
using Gray.Entity.SystemModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Data;
using System.Threading;
using System.Threading.Tasks;

namespace Gray.Core.Transaction
{
    /// <summary>
    /// 基于 Scoped 的异步事务上下文实现（完全解耦模式）
    /// 不依赖具体的 UnitOfWork 实例，而是通过服务提供者获取
    /// </summary>
    public class AsyncTransactionContext : IAsyncTransactionContext, IDisposable
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<AsyncTransactionContext> _logger;

        private IDbContextTransaction _currentTransaction;
        private BaseDbContext _transactionContext;
        private int _transactionLevel = 0;
        private bool _disposed = false;

        public AsyncTransactionContext(IServiceProvider serviceProvider, ILogger<AsyncTransactionContext> logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        public IDbContextTransaction CurrentTransaction => _currentTransaction;
        
        public bool HasActiveTransaction => _currentTransaction != null;
        
        public int TransactionLevel => _transactionLevel;
        
        public async Task<T> ExecuteInTransactionAsync<TDbContext, T>(
            Func<IUnitOfWork, Task<T>> operation,
            IsolationLevel isolationLevel = IsolationLevel.ReadUncommitted,
            CancellationToken cancellationToken = default)
            where TDbContext : BaseDbContext<TDbContext>
        {
            if (operation == null)
                throw new ArgumentNullException(nameof(operation));

            if (_disposed)
                throw new ObjectDisposedException(nameof(AsyncTransactionContext));

            var isNewTransaction = !HasActiveTransaction;

            if (isNewTransaction)
            {
                _logger.LogDebug("开始新的异步事务，隔离级别: {IsolationLevel}", isolationLevel);

                // 从服务提供者获取 UnitOfWork 实例
                using var scope = _serviceProvider.CreateScope();
                var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

                // 直接使用指定的 DbContext 管理事务
                _transactionContext = unitOfWork.GetDbContext<TDbContext>();

                // 设置数据库超时
                _transactionContext.Database.SetCommandTimeout(120);

                _currentTransaction = await _transactionContext.Database.BeginTransactionAsync(isolationLevel, cancellationToken);
                _transactionLevel = 1;

                try
                {
                    // 将 UnitOfWork 传递给业务操作
                    var result = await operation(unitOfWork);

                    _logger.LogDebug("异步事务执行成功，准备提交");

                    // 先保存所有更改
                    await unitOfWork.SaveChangesAsync();

                    // 然后提交事务
                    await _currentTransaction.CommitAsync(cancellationToken);

                    return result;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "异步事务执行失败，准备回滚");
                    await _currentTransaction.RollbackAsync(cancellationToken);
                    throw;
                }
                finally
                {
                    await _currentTransaction.DisposeAsync();
                    _currentTransaction = null;
                    _transactionContext = null;
                    _transactionLevel = 0;
                }
            }
            else
            {
                // 嵌套事务 - 重用现有事务
                _transactionLevel++;
                _logger.LogDebug("执行嵌套事务，当前级别: {Level}", _transactionLevel);

                // 获取当前作用域的 UnitOfWork
                var unitOfWork = _serviceProvider.GetRequiredService<IUnitOfWork>();

                try
                {
                    return await operation(unitOfWork);
                }
                finally
                {
                    _transactionLevel--;
                }
            }
        }
        
        public async Task ExecuteInTransactionAsync<TDbContext>(
            Func<IUnitOfWork, Task> operation,
            IsolationLevel isolationLevel = IsolationLevel.ReadUncommitted,
            CancellationToken cancellationToken = default)
            where TDbContext : BaseDbContext<TDbContext>
        {
            await ExecuteInTransactionAsync<TDbContext, int>(async (unitOfWork) =>
            {
                await operation(unitOfWork);
                return 0; // 返回虚拟值
            }, isolationLevel, cancellationToken);
        }

        #region 向后兼容方法（默认使用 SysDbContext）

        /// <summary>
        /// 在事务中执行异步操作（使用默认 SysDbContext，向后兼容）
        /// </summary>
        public async Task<T> ExecuteInTransactionAsync<T>(
            Func<IUnitOfWork, Task<T>> operation,
            IsolationLevel isolationLevel = IsolationLevel.ReadUncommitted,
            CancellationToken cancellationToken = default)
        {
            return await ExecuteInTransactionAsync<SysDbContext, T>(operation, isolationLevel, cancellationToken);
        }

        /// <summary>
        /// 在事务中执行异步操作（无返回值，使用默认 SysDbContext，向后兼容）
        /// </summary>
        public async Task ExecuteInTransactionAsync(
            Func<IUnitOfWork, Task> operation,
            IsolationLevel isolationLevel = IsolationLevel.ReadUncommitted,
            CancellationToken cancellationToken = default)
        {
            await ExecuteInTransactionAsync<SysDbContext>(operation, isolationLevel, cancellationToken);
        }

        #endregion

        public void Dispose()
        {
            if (!_disposed)
            {
                if (HasActiveTransaction)
                {
                    _logger.LogWarning("事务上下文被释放时仍有活动事务，执行回滚");
                    try
                    {
                        // 在 Dispose 时，我们不需要回滚，因为事务已经在 finally 中处理了
                        // 这里只是清理资源
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "释放时回滚事务失败");
                    }
                }
                
                _disposed = true;
            }
        }
    }
}
