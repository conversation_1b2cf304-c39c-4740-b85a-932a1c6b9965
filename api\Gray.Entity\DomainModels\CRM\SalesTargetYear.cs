/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "销售年度目标",TableName = "SalesTargetYear",DBServer = "JL_CRMDbContext")]
    public partial class SalesTargetYear:JL_CRMEntity
    {
        /// <summary>
       ///销售目标id
       /// </summary>
       [Key]
       [Display(Name ="销售目标id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid SalesTargetId { get; set; }

       /// <summary>
       ///业务员
       /// </summary>
       [Display(Name ="业务员")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? PersonId { get; set; }

       /// <summary>
       ///客户代码
       /// </summary>
       [Display(Name ="客户代码")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       public string CustomerCode { get; set; }

       /// <summary>
       ///客户名称
       /// </summary>
       [Display(Name ="客户名称")]
       [MaxLength(500)]
       [Column(TypeName="nvarchar(500)")]
       [Editable(true)]
       public string CustomerName { get; set; }

       /// <summary>
       ///1月
       /// </summary>
       [Display(Name ="1月")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Jan { get; set; }

       /// <summary>
       ///2月
       /// </summary>
       [Display(Name ="2月")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Feb { get; set; }

       /// <summary>
       ///3月
       /// </summary>
       [Display(Name ="3月")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Mar { get; set; }

       /// <summary>
       ///4月
       /// </summary>
       [Display(Name ="4月")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Apr { get; set; }

       /// <summary>
       ///5月
       /// </summary>
       [Display(Name ="5月")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? May { get; set; }

       /// <summary>
       ///6月
       /// </summary>
       [Display(Name ="6月")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Jun { get; set; }

       /// <summary>
       ///7月
       /// </summary>
       [Display(Name ="7月")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Jul { get; set; }

       /// <summary>
       ///8月
       /// </summary>
       [Display(Name ="8月")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Aug { get; set; }

       /// <summary>
       ///9月
       /// </summary>
       [Display(Name ="9月")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Sep { get; set; }

       /// <summary>
       ///10月
       /// </summary>
       [Display(Name ="10月")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Oct { get; set; }

       /// <summary>
       ///11月
       /// </summary>
       [Display(Name ="11月")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Nov { get; set; }

       /// <summary>
       ///12月
       /// </summary>
       [Display(Name ="12月")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Dec { get; set; }

       /// <summary>
       ///年份
       /// </summary>
       [Display(Name ="年份")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? Year { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? CreateID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Creator")]
       [MaxLength(30)]
       [Column(TypeName="nvarchar(30)")]
       [Editable(true)]
       public string Creator { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? CreateDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ModifyID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Modifier")]
       [MaxLength(30)]
       [Column(TypeName="nvarchar(30)")]
       [Editable(true)]
       public string Modifier { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? ModifyDate { get; set; }

       
    }
}