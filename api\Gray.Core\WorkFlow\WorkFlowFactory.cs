
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Gray.Core.WorkFlow
{
    public class WorkFlowFactory
    {
        private static IServiceProvider _serviceProvider;
        private static readonly object _lock = new object();
        
        public static void Initialize(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public static IWorkFlowService GetWorkFlowService()
        {
            if(_serviceProvider == null)
            {
                throw new InvalidOperationException("WorkFlowFactory 未初始化");
            }
            return _serviceProvider.GetRequiredService<IWorkFlowService>();
        }
    }
}
