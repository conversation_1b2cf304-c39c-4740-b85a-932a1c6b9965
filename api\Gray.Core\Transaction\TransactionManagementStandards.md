# 事务管理统一规范

## 概述

本文档定义了项目中事务管理的统一标准和最佳实践，确保所有开发人员遵循一致的事务管理方式。

## 核心原则

### 1. 单一事务管理入口
- **统一使用 `AsyncTransactionContext`** 进行所有事务管理
- **禁止直接使用 `UnitOfWork` 的事务方法**（BeginTransaction、Commit、Rollback）
- **禁止在 Repository 层管理事务**

### 2. 分层职责明确
- **Controller层**：接收请求，调用Service层
- **Service层**：业务逻辑 + 声明事务需求
- **Repository层**：纯数据访问，不涉及事务管理
- **AsyncTransactionContext**：统一事务管理

### 3. 架构优势
- **现代异步设计**：专门为异步应用设计
- **支持多 DbContext**：可以指定不同的 DbContext 类型
- **完全解耦设计**：通过 ServiceProvider 获取依赖
- **业务操作接收 IUnitOfWork 参数**：更灵活的数据访问
- **嵌套事务支持**：自动处理事务嵌套

## 事务使用规范

### 场景1：简单CRUD操作

**规则**：依赖Repository的自动事务，无需显式事务管理

```csharp
public class UserService : ServiceBase<User, IUserRepository>
{
    // ✅ 正确：简单CRUD操作，依赖基类自动事务
    public override async Task<WebResponseContent> Add(SaveModel saveDataModel)
    {
        // 业务验证
        AddOnExecuting = (User user, object list) =>
        {
            user.SetCreateDefaultVal();
            return Response.OK();
        };
        
        return await base.Add(saveDataModel);
    }
}
```

### 场景2：复杂业务逻辑（异步推荐）

**规则**：使用 `AsyncTransactionContext.ExecuteInTransactionAsync`

```csharp
public class OrderService : ServiceBase<Order, IOrderRepository>
{
    // ✅ 正确：复杂业务逻辑使用AsyncTransactionContext
    public async Task<WebResponseContent> ProcessOrderAsync(Order order, List<OrderItem> items)
    {
        return await AsyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
        {
            // 1. 验证库存
            foreach (var item in items)
            {
                if (!await CheckInventoryAsync(item.ProductId, item.Quantity))
                {
                    throw new BusinessException("库存不足");
                }
            }

            // 2. 创建订单
            await repository.AddAsync(order);

            // 3. 扣减库存
            foreach (var item in items)
            {
                await UpdateInventoryAsync(item.ProductId, -item.Quantity);
            }

            // 4. 保存更改
            await repository.SaveChangesAsync();

            return Response.OK("订单处理成功");
        });
    }
}
```

### 场景3：指定 DbContext 类型

**规则**：使用 `AsyncTransactionContext.ExecuteInTransactionAsync<TDbContext>`

```csharp
public class OrderService : ServiceBase<Order, IOrderRepository>
{
    // ✅ 正确：指定特定的 DbContext 类型
    public async Task<WebResponseContent> ProcessOrderWithSpecificContextAsync(Order order)
    {
        return await AsyncTransactionContext.ExecuteInTransactionAsync<OrderDbContext>(async (unitOfWork) =>
        {
            // 使用指定的 OrderDbContext 进行操作
            var orderRepo = unitOfWork.GetRepository<Order>();
            await orderRepo.AddAsync(order);

            // 其他业务逻辑
            await ProcessOrderItems(order.Items);

            return Response.OK("订单处理成功");
        });
    }
}
```

### 场景4：工作流/审批流程

**规则**：使用 `AsyncTransactionContext.ExecuteInTransactionAsync` + 适当的隔离级别

```csharp
public class WorkFlowService
{
    // ✅ 正确：工作流使用AsyncTransactionContext
    public async Task<WebResponseContent> AuditAsync<T>(T entity, AuditStatus status, string reason)
        where T : BaseEntity
    {
        return await _asyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
        {
            // 1. 更新实体状态
            entity.AuditStatus = (int)status;
            entity.AuditReason = reason;
            entity.AuditTime = DateTime.Now;

            // 2. 添加审核日志
            await AddAuditLogAsync(entity.Id, status, reason);

            // 3. 触发工作流
            await ProcessWorkFlowAsync(entity, status);

            // 4. 保存更改
            await unitOfWork.SaveChangesAsync();

            return Response.OK("审核成功");
        }, IsolationLevel.ReadCommitted);
    }
}
```

### 场景5：声明式事务（推荐用于新开发）

**规则**：使用 `[Transactional]` 特性

```csharp
public class UserService : ServiceBase<User, IUserRepository>
{
    // ✅ 正确：声明式事务，代码更简洁
    [Transactional(IsolationLevel = IsolationLevel.ReadCommitted)]
    public async Task<WebResponseContent> CreateUserWithRoleAsync(User user, List<int> roleIds)
    {
        // 1. 创建用户
        user.SetCreateDefaultVal();
        await repository.AddAsync(user);
        await repository.SaveChangesAsync();
        
        // 2. 分配角色
        foreach (var roleId in roleIds)
        {
            await AssignRoleAsync(user.Id, roleId);
        }
        
        return Response.OK("用户创建成功");
    }
}
```

## 禁止的做法

### ❌ 错误做法1：直接使用UnitOfWork事务

```csharp
// ❌ 禁止：直接使用UnitOfWork管理事务
public async Task<WebResponseContent> BadExample()
{
    using var transaction = await _unitOfWork.BeginTransactionAsync();
    try
    {
        // 业务逻辑
        await _unitOfWork.CommitAsync();
        return Response.OK();
    }
    catch
    {
        await _unitOfWork.RollbackAsync();
        throw;
    }
}
```

### ❌ 错误做法2：在Repository层管理事务

```csharp
// ❌ 禁止：Repository层不应该管理事务
public class UserRepository : RepositoryBase<User>
{
    public async Task<bool> CreateUserWithTransaction(User user)
    {
        using var transaction = await DbContext.Database.BeginTransactionAsync();
        try
        {
            DbContext.Users.Add(user);
            await DbContext.SaveChangesAsync();
            await transaction.CommitAsync();
            return true;
        }
        catch
        {
            await transaction.RollbackAsync();
            return false;
        }
    }
}
```

### ❌ 错误做法3：混合使用多种事务管理方式

```csharp
// ❌ 禁止：在同一个方法中混合使用不同的事务管理方式
public async Task<WebResponseContent> BadMixedExample()
{
    // 错误：同时使用TransactionService和直接UnitOfWork
    return await TransactionService.ExecuteInTransactionAsync(async () =>
    {
        using var transaction = await _unitOfWork.BeginTransactionAsync();
        // 这会导致嵌套事务问题
        return Response.OK();
    });
}
```

## 事务隔离级别选择

### 默认隔离级别
- **ReadUncommitted**：用于读取操作较多的场景
- **ReadCommitted**：默认选择，适用于大多数业务场景
- **RepeatableRead**：需要避免不可重复读的场景
- **Serializable**：最高隔离级别，用于关键业务操作

### 选择指南
```csharp
// 一般业务操作
[Transactional(IsolationLevel = IsolationLevel.ReadCommitted)]

// 财务相关操作
[Transactional(IsolationLevel = IsolationLevel.Serializable)]

// 报表查询操作
[Transactional(IsolationLevel = IsolationLevel.ReadUncommitted)]
```

## 异常处理规范

### 事务中的异常处理
```csharp
public async Task<WebResponseContent> ExampleWithExceptionHandling()
{
    return await TransactionService.ExecuteInTransactionAsync(async () =>
    {
        try
        {
            // 业务逻辑
            await SomeBusinessOperation();
            return Response.OK();
        }
        catch (BusinessException ex)
        {
            // 业务异常：记录日志但不重新抛出，让事务回滚
            Logger.LogWarning($"业务异常：{ex.Message}");
            return Response.Error(ex.Message);
        }
        catch (Exception ex)
        {
            // 系统异常：记录日志并重新抛出，让上层处理
            Logger.LogError(ex, "系统异常");
            throw;
        }
    });
}
```

## 性能优化建议

### 1. 事务范围最小化
```csharp
// ✅ 正确：事务范围最小
public async Task<WebResponseContent> OptimizedExample()
{
    // 准备数据（在事务外）
    var validatedData = await ValidateDataAsync();
    
    // 最小事务范围
    return await TransactionService.ExecuteInTransactionAsync(async () =>
    {
        await SaveDataAsync(validatedData);
        return Response.OK();
    });
}
```

### 2. 避免长时间事务
```csharp
// ❌ 避免：长时间事务
public async Task<WebResponseContent> BadLongTransaction()
{
    return await TransactionService.ExecuteInTransactionAsync(async () =>
    {
        await SomeTimeConsumingOperation(); // 耗时操作
        await AnotherTimeConsumingOperation(); // 另一个耗时操作
        return Response.OK();
    });
}

// ✅ 正确：分解为多个短事务
public async Task<WebResponseContent> GoodShortTransactions()
{
    var result1 = await AsyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
    {
        await QuickOperation1();
        return Response.OK();
    });

    if (!result1.Status) return result1;

    var result2 = await AsyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
    {
        await QuickOperation2();
        return Response.OK();
    });

    return result2;
}
```

## 迁移指南

### 现有代码迁移步骤

1. **识别现有事务代码**
   - 搜索 `BeginTransaction`、`Commit`、`Rollback`
   - 搜索 `DbContextBeginTransaction`
   - 搜索 `ExecuteTransactionAsync`

2. **替换为标准方式**
   ```csharp
   // 旧代码
   Response = repository.DbContextBeginTransaction(() => {
       // 业务逻辑
       return Response.OK();
   });

   // 新代码
   Response = await AsyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) => {
       // 业务逻辑
       return Response.OK();
   });
   ```

3. **验证功能正确性**
   - 确保事务边界正确
   - 验证异常处理逻辑
   - 测试回滚机制

## 总结

遵循本规范可以确保：
- **一致性**：所有事务管理使用统一方式
- **可维护性**：代码结构清晰，易于理解和维护
- **可靠性**：减少事务管理错误，提高系统稳定性
- **性能**：优化事务使用，提高系统性能

所有新开发的代码必须遵循本规范，现有代码应逐步迁移到标准方式。
