﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName></SccProjectName>
    <SccProvider></SccProvider>
    <SccAuxPath></SccAuxPath>
    <SccLocalPath></SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ApplicationIcon />
    <OutputType>Library</OutputType>
    <StartupObject />
    <AssemblyName>Gray.Builder</AssemblyName>
    <RootNamespace>Gray.Builder</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Autofac" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Npgsql" />
    <PackageReference Include="System.Net.Security" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Gray.Core\Gray.Core.csproj" />
    <ProjectReference Include="..\Gray.Entity\Gray.Entity.csproj" />
  </ItemGroup>

</Project>
