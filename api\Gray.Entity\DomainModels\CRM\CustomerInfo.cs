/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "经销商详情",TableName = "CustomerInfo",DBServer = "JL_CRMDbContext")]
    public partial class CustomerInfo:JL_CRMEntity
    {
        /// <summary>
       ///
       /// </summary>
       [Key]
       [Display(Name ="CustomerId")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int CustomerId { get; set; }

       /// <summary>
       ///客户
       /// </summary>
       [Display(Name ="客户")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string CustomerCode { get; set; }

       /// <summary>
       ///固定资产
       /// </summary>
       [Display(Name ="固定资产")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string fixed_assets { get; set; }

       /// <summary>
       ///法定代表人
       /// </summary>
       [Display(Name ="法定代表人")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string legal_rep { get; set; }

       /// <summary>
       ///实际控制人姓名
       /// </summary>
       [Display(Name ="实际控制人姓名")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string controller_name { get; set; }

       /// <summary>
       ///实际控制人年龄
       /// </summary>
       [Display(Name ="实际控制人年龄")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? controller_age { get; set; }

       /// <summary>
       ///实际控制人健康状况
       /// </summary>
       [Display(Name ="实际控制人健康状况")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string controller_health { get; set; }

       /// <summary>
       ///子女是否在公司上班或准备接班
       /// </summary>
       [Display(Name ="子女是否在公司上班或准备接班")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string heir_ready { get; set; }

       /// <summary>
       ///我公司产品经销区域
       /// </summary>
       [Display(Name ="我公司产品经销区域")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string sales_region { get; set; }

       /// <summary>
       ///我公司品牌
       /// </summary>
       [Display(Name ="我公司品牌")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string our_brand { get; set; }

       /// <summary>
       ///是否办理担保
       /// </summary>
       [Display(Name ="是否办理担保")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string guarantee { get; set; }

       /// <summary>
       ///担保额度
       /// </summary>
       [Display(Name ="担保额度")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? guarantee_limit { get; set; }

       /// <summary>
       ///经销区域内总地级市数量
       /// </summary>
       [Display(Name ="经销区域内总地级市数量")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string total_cities { get; set; }

       /// <summary>
       ///有效覆盖的地级市数量
       /// </summary>
       [Display(Name ="有效覆盖的地级市数量")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string covered_cities { get; set; }

       /// <summary>
       ///经销区域内总县级区域数量
       /// </summary>
       [Display(Name ="经销区域内总县级区域数量")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string total_counties { get; set; }

       /// <summary>
       ///有效覆盖的县级区域数量
       /// </summary>
       [Display(Name ="有效覆盖的县级区域数量")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string covered_counties { get; set; }

       /// <summary>
       ///工厂直发客户数量及名称
       /// </summary>
       [Display(Name ="工厂直发客户数量及名称")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string direct_clients { get; set; }

       /// <summary>
       ///有效合作的车队明细
       /// </summary>
       [Display(Name ="有效合作的车队明细")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string fleet_details { get; set; }

       /// <summary>
       ///运作的其他品牌及区域
       /// </summary>
       [Display(Name ="运作的其他品牌及区域")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string other_brands { get; set; }

       /// <summary>
       ///业务人员总数
       /// </summary>
       [Display(Name ="业务人员总数")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string staff_count { get; set; }

       /// <summary>
       ///业务员与品牌的对应关系
       /// </summary>
       [Display(Name ="业务员与品牌的对应关系")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string staff_brand_map { get; set; }

       /// <summary>
       ///有无独立的品牌销售团队
       /// </summary>
       [Display(Name ="有无独立的品牌销售团队")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string indep_team { get; set; }

       /// <summary>
       ///各品牌平均月销量
       /// </summary>
       [Display(Name ="各品牌平均月销量")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string avg_month_sales { get; set; }

       /// <summary>
       ///各品牌销售提成
       /// </summary>
       [Display(Name ="各品牌销售提成")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string sales_commission { get; set; }

       /// <summary>
       ///各品牌的资金分配
       /// </summary>
       [Display(Name ="各品牌的资金分配")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string fund_allocation { get; set; }

       /// <summary>
       ///其他品牌给予的铺垫
       /// </summary>
       [Display(Name ="其他品牌给予的铺垫")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string brand_support { get; set; }

       /// <summary>
       ///经销商对外各品牌赊欠情况
       /// </summary>
       [Display(Name ="经销商对外各品牌赊欠情况")]
       [MaxLength(444)]
       [Column(TypeName="nvarchar(444)")]
       [Editable(true)]
       public string credit_status { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? CreateID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Creator")]
       [MaxLength(60)]
       [Column(TypeName="nvarchar(60)")]
       [Editable(true)]
       public string Creator { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? CreateDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ModifyID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Modifier")]
       [MaxLength(60)]
       [Column(TypeName="nvarchar(60)")]
       [Editable(true)]
       public string Modifier { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? ModifyDate { get; set; }

       
    }
}