/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "发货订单明细",TableName = "OrderList",DBServer = "JL_CRMDbContext")]
    public partial class OrderList:JL_CRMEntity
    {
        /// <summary>
       ///订单列Id
       /// </summary>
       [Key]
       [Display(Name ="订单列Id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid OrderListId { get; set; }

       /// <summary>
       ///商品id
       /// </summary>
       [Display(Name ="商品id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       public Guid? GoodsId { get; set; }

       /// <summary>
       ///商品代码
       /// </summary>
       [Display(Name ="商品代码")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string GoodsCode { get; set; }

       /// <summary>
       ///商品规格
       /// </summary>
       [Display(Name ="商品规格")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       public string Specs { get; set; }

       /// <summary>
       ///3c认证情况
       /// </summary>
       [Display(Name ="3c认证情况")]
       [Column(TypeName="tinyint")]
       [Editable(true)]
       public byte? CCCStatus { get; set; }

       /// <summary>
       ///品牌
       /// </summary>
       [Display(Name ="品牌")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string GoodsBrand { get; set; }

       /// <summary>
       ///商品名
       /// </summary>
       [Display(Name ="商品名")]
       [MaxLength(400)]
       [Column(TypeName="nvarchar(400)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string GoodsName { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Img")]
       [MaxLength(1000)]
       [Column(TypeName="nvarchar(1000)")]
       [Editable(true)]
       public string Img { get; set; }

       /// <summary>
       ///月计划数量
       /// </summary>
       [Display(Name ="月计划数量")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? PreOrdeQty { get; set; }

       /// <summary>
       ///价格类型
       /// </summary>
       [Display(Name ="价格类型")]
       [MaxLength(40)]
       [Column(TypeName="nvarchar(40)")]
       [Editable(true)]
       public string PriceType { get; set; }

       /// <summary>
       ///单价
       /// </summary>
       [Display(Name ="单价")]
       [Column(TypeName="float")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public float Price { get; set; }

       /// <summary>
       ///系统单价
       /// </summary>
       [Display(Name ="系统单价")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? PriceSystem { get; set; }

       /// <summary>
       ///本区上月
       /// </summary>
       [Display(Name ="本区上月")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? SaleLastMonth { get; set; }

       /// <summary>
       ///本区今年
       /// </summary>
       [Display(Name ="本区今年")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? SaleCurrentYear { get; set; }

       /// <summary>
       ///数量
       /// </summary>
       [Display(Name ="数量")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int Qty { get; set; }

       /// <summary>
       ///合计价格
       /// </summary>
       [Display(Name ="合计价格")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? PriceCount { get; set; }

       /// <summary>
       ///冲返利
       /// </summary>
       [Display(Name ="冲返利")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Rebate { get; set; }

       /// <summary>
       ///应收账款
       /// </summary>
       [Display(Name ="应收账款")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? Receivables { get; set; }

       /// <summary>
       ///单重
       /// </summary>
       [Display(Name ="单重")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? WeightSingle { get; set; }

       /// <summary>
       ///实际单价
       /// </summary>
       [Display(Name ="实际单价")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? RealPrice { get; set; }

       /// <summary>
       ///备注
       /// </summary>
       [Display(Name ="备注")]
       [MaxLength(1000)]
       [Column(TypeName="nvarchar(1000)")]
       [Editable(true)]
       public string Remark { get; set; }

       /// <summary>
       ///备注外
       /// </summary>
       [Display(Name ="备注外")]
       [MaxLength(1000)]
       [Column(TypeName="nvarchar(1000)")]
       [Editable(true)]
       public string RemarkW { get; set; }

       /// <summary>
       ///合计重量
       /// </summary>
       [Display(Name ="合计重量")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? WeightCount { get; set; }

       /// <summary>
       ///订单id
       /// </summary>
       [Display(Name ="订单id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       public Guid OrderId { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? CreateID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Creator")]
       [MaxLength(60)]
       [Column(TypeName="nvarchar(60)")]
       [Editable(true)]
       public string Creator { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? CreateDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ModifyID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Modifier")]
       [MaxLength(60)]
       [Column(TypeName="nvarchar(60)")]
       [Editable(true)]
       public string Modifier { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? ModifyDate { get; set; }

       /// <summary>
       ///是否改动
       /// </summary>
       [Display(Name ="是否改动")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? Changed { get; set; }

       
    }
}