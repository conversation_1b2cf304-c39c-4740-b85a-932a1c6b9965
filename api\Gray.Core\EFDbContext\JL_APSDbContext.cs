﻿using Microsoft.EntityFrameworkCore;
using Gray.Core.DBManager;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.SystemModels;
using System;

namespace Gray.Core.EFDbContext
{
    public class JL_APSDbContext : BaseDbContext<JL_APSDbContext>, IDependency
    {
        protected override string ConnectionString
        {
            get
            {
                return DBServerProvider.GetConnection("JL_APSConn");
            }
        }

        public JL_APSDbContext() : base()
        {
        }

        public JL_APSDbContext(DbContextOptions<JL_APSDbContext> options) : base(options)
        {
        }

      

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.UseDbTypeFromConfig(optionsBuilder, this.GetType().Name);
            //默认禁用实体跟踪
            optionsBuilder = optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            base.OnConfiguring(optionsBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
        }

        protected override Type GetBaseEntityType()
        {
            return typeof(JL_APSEntity);
        }
    }
}