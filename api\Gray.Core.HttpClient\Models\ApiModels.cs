namespace Gray.Core.GrayHttpClient.Models
{
    /// <summary>
    /// IOTSharp API响应模型
    /// </summary>
    public class IOTSharpApiResponse<T>
    {
        public T? Data { get; set; }
        public int Code { get; set; }
        public string Msg { get; set; } = string.Empty;
    }

    /// <summary>
    /// 遥测数据模型
    /// </summary>
    public class TelemetryData
    {
        public string KeyName { get; set; } = string.Empty;
        public DateTime DateTime { get; set; }
        public string DataType { get; set; } = string.Empty;
        public object? Value { get; set; }
    }

    /// <summary>
    /// 温湿度数据模型
    /// </summary>
    public class TemperatureHumidityData
    {
        public string DeviceId { get; set; } = string.Empty;
        public float? Temperature { get; set; }
        public float? Humidity { get; set; }
        public DateTime? TemperatureTime { get; set; }
        public DateTime? HumidityTime { get; set; }
        public DateTime? DeviceTimestamp { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 用户登录信息模型
    /// </summary>
    public class LoginWithoutCodeInfo
    {
        public string UserName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string? Domain { get; set; }
    }

    /// <summary>
    /// 登录响应模型
    /// </summary>
    public class LoginResponse
    {
        public bool Status { get; set; }
        public string Message { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public TokenInfo? TokenInfo { get; set; }
        public UserInfo? UserInfo { get; set; }
    }

    /// <summary>
    /// Token 信息模型
    /// </summary>
    public class TokenInfo
    {
        public string TokenValue { get; set; } = string.Empty;
        public int TokenTimeout { get; set; }
        public DateTime ExpiryTime { get; set; }
    }

    /// <summary>
    /// 用户信息模型
    /// </summary>
    public class UserInfo
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public List<string> Roles { get; set; } = new();
    }

    /// <summary>
    /// IOTSharp用户详细信息模型 (用于 /api/Account/MyInfo)
    /// </summary>
    public class IOTSharpUserInfo
    {
        public int Code { get; set; }
        public string Roles { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Avatar { get; set; } = string.Empty;
        public string Introduction { get; set; } = string.Empty;
        public CustomerInfo? Customer { get; set; }
        public TenantInfo? Tenant { get; set; }
        public string Email { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
    }

    /// <summary>
    /// 客户信息模型
    /// </summary>
    public class CustomerInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public string Province { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string Street { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public int ZipCode { get; set; }
        public bool Deleted { get; set; }
    }

    /// <summary>
    /// 租户信息模型
    /// </summary>
    public class TenantInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public string Province { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string Street { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public int ZipCode { get; set; }
        public bool Deleted { get; set; }
    }

    /// <summary>
    /// 设备命令模型
    /// </summary>
    public class DeviceCommand
    {
        public string CommandName { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 设备属性模型
    /// </summary>
    public class DeviceAttributes
    {
        public Dictionary<string, object> Attributes { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 遥测数据详细模型（用于特定时间范围查询）
    /// </summary>
    public class TelemetryDataDetail
    {
        public string KeyName { get; set; } = string.Empty;
        public DateTime DateTime { get; set; }
        public string DataType { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
    }

    /// <summary>
    /// API错误响应模型
    /// </summary>
    public class ApiErrorResponse
    {
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public int Status { get; set; }
        public string Detail { get; set; } = string.Empty;
        public string Instance { get; set; } = string.Empty;
        public Dictionary<string, object> Extensions { get; set; } = new();
        
        // 支持额外的动态属性
        public Dictionary<string, object> AdditionalProperties { get; set; } = new();
    }
} 