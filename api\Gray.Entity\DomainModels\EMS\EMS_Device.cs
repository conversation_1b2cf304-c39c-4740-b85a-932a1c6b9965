/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "IOT设备表",TableName = "EMS_Device",DBServer = "JL_EMSDbContext")]
    public partial class EMS_Device:JL_EMSEntity
    {
        /// <summary>
       ///设备Id
       /// </summary>
       [Key]
       [Display(Name ="设备Id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid DeviceID { get; set; }

       /// <summary>
       ///设备名称
       /// </summary>
       [Display(Name ="设备名称")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string Name { get; set; }

       /// <summary>
       ///备注
       /// </summary>
       [Display(Name ="备注")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string Desc { get; set; }

       /// <summary>
       ///平台Id
       /// </summary>
       [Display(Name ="平台Id")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string IOTSharpID { get; set; }

       /// <summary>
       ///设备种类
       /// </summary>
       [Display(Name ="设备种类")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string DeviceType { get; set; }

       /// <summary>
       ///是否启用
       /// </summary>
       [Display(Name ="是否启用")]
       [Column(TypeName="bit")]
       [Editable(true)]
       public bool? IsEnable { get; set; }

       /// <summary>
       ///IPv4
       /// </summary>
       [Display(Name ="IPv4")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       public string IPv4 { get; set; }

       
    }
}