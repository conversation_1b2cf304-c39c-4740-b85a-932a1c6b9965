# 🚀 通用JWT认证 - 零代码对接任何系统

现在您只需要**配置**就能对接任何基于JWT认证的外部系统，无需编写任何认证代码！

## ✨ 核心优势

- 🎯 **零代码对接** - 只需配置，无需编程
- 🔄 **自动Token管理** - 自动获取、缓存、刷新Token
- 🛡️ **自动重试** - 401错误时自动刷新Token并重试
- 🏢 **多系统支持** - 同时对接多个不同系统
- ⚙️ **灵活配置** - 支持各种认证方式和请求格式

## 🎯 三步对接新系统

### 步骤1：注册服务（一次性）

```csharp
// Program.cs
builder.Services.AddHttpClientServices(builder.Configuration);
```

### 步骤2：配置新系统

```json
{
  "HttpClient": {
    "AuthenticationSystems": {
      "YourNewSystem": {
        "Enabled": true,
        "LoginEndpoint": "/api/auth/login",
        "Credentials": {
          "Username": "your_username",
          "Password": "your_password"
        },
        "TokenMapping": {
          "AccessTokenField": "access_token",
          "RefreshTokenField": "refresh_token",
          "ExpiresInField": "expires_in"
        },
        "RequestFormat": {
          "BodyTemplate": {
            "username": "{Username}",
            "password": "{Password}",
            "grant_type": "password"
          }
        }
      }
    },
    "ApiEndpoints": {
      "YourNewSystem": {
        "BaseUrl": "https://your-system.com",
        "AuthenticationSystem": "YourNewSystem"
      }
    }
  }
}
```

### 步骤3：直接使用

```csharp
public class YourService
{
    private readonly IHttpClientManager _clientManager;

    public async Task CallApi()
    {
        // 获取HttpClient，JWT认证自动处理
        var client = _clientManager.GetHttpClient("YourNewSystem");
        
        // 直接调用API，Token自动添加
        var response = await client.GetAsync("/api/data");
        var data = await response.Content.ReadAsStringAsync();
    }
}
```

## 🔧 支持的认证方式

### 用户名密码
```json
{
  "Credentials": {
    "Username": "admin",
    "Password": "password123"
  },
  "RequestFormat": {
    "BodyTemplate": {
      "username": "{Username}",
      "password": "{Password}"
    }
  }
}
```

### 客户端凭据
```json
{
  "Credentials": {
    "ClientId": "your_client_id",
    "ClientSecret": "your_client_secret"
  },
  "RequestFormat": {
    "BodyTemplate": {
      "grant_type": "client_credentials",
      "client_id": "{ClientId}",
      "client_secret": "{ClientSecret}"
    }
  }
}
```

### API密钥
```json
{
  "Credentials": {
    "ApiKey": "your_api_key"
  },
  "RequestFormat": {
    "HeaderTemplate": {
      "X-API-Key": "{ApiKey}"
    }
  }
}
```

## 🌟 实际案例

### 钉钉API对接
```json
{
  "AuthenticationSystems": {
    "DingTalk": {
      "LoginEndpoint": "/gettoken",
      "Credentials": {
        "ClientId": "your_app_key",
        "ClientSecret": "your_app_secret"
      },
      "TokenMapping": {
        "AccessTokenField": "access_token"
      },
      "RequestFormat": {
        "Method": "GET",
        "BodyTemplate": {
          "appkey": "{ClientId}",
          "appsecret": "{ClientSecret}"
        }
      }
    }
  },
  "ApiEndpoints": {
    "DingTalk": {
      "BaseUrl": "https://oapi.dingtalk.com",
      "AuthenticationSystem": "DingTalk"
    }
  }
}
```

### 企业微信API对接
```json
{
  "AuthenticationSystems": {
    "WeWork": {
      "LoginEndpoint": "/cgi-bin/gettoken",
      "Credentials": {
        "ClientId": "your_corpid",
        "ClientSecret": "your_corpsecret"
      },
      "TokenMapping": {
        "AccessTokenField": "access_token"
      }
    }
  }
}
```

## 🎛️ 高级配置

### 自定义Token字段映射
```json
{
  "TokenMapping": {
    "AccessTokenField": "token",        // 系统返回的Token字段名
    "RefreshTokenField": "refreshToken", // 刷新Token字段名
    "ExpiresInField": "expiresIn",      // 过期时间字段名
    "TokenTypeField": "tokenType"       // Token类型字段名
  }
}
```

### 自定义请求格式
```json
{
  "RequestFormat": {
    "Method": "POST",                           // HTTP方法
    "ContentType": "application/json",          // 内容类型
    "BodyTemplate": {                          // 请求体模板
      "username": "{Username}",
      "password": "{Password}",
      "custom_field": "custom_value"
    },
    "HeaderTemplate": {                        // 请求头模板
      "X-Custom-Header": "custom_value"
    }
  }
}
```

## 🔍 高级用法

### Token状态管理
```csharp
public class AdvancedService
{
    private readonly IUniversalTokenManager _tokenManager;

    public async Task ManageTokens()
    {
        // 检查Token是否有效
        var isValid = await _tokenManager.IsTokenValidAsync("YourSystem");
        
        // 手动刷新Token
        var refreshed = await _tokenManager.RefreshTokenAsync("YourSystem");
        
        // 清除Token（登出）
        await _tokenManager.ClearTokenAsync("YourSystem");
        
        // 获取所有配置的系统
        var systems = _tokenManager.GetConfiguredSystems();
    }
}
```

### 多系统调用
```csharp
public async Task CallMultipleSystems()
{
    var systems = new[] { "System1", "System2", "System3" };
    
    foreach (var system in systems)
    {
        var client = _clientManager.GetHttpClient(system);
        var response = await client.GetAsync("/api/health");
        // 每个系统都会自动使用对应的JWT认证
    }
}
```

## 🎉 总结

现在您对接新的JWT认证系统只需要：

1. ✅ **配置认证信息** - 在JSON配置文件中
2. ✅ **配置API端点** - 关联到认证系统
3. ✅ **直接使用HttpClient** - 系统自动处理所有认证逻辑

**无需编写任何认证代码，真正做到配置驱动的JWT认证集成！**

---

📖 更多示例请查看：`Examples/UniversalJwtExample.cs`  
📋 详细配置说明：`Examples/UniversalJwtIntegration.md`
