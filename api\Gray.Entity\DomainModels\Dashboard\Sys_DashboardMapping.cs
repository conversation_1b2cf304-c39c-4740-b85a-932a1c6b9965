/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "工作台映射",TableName = "Sys_DashboardMapping",DBServer = "SysDbContext")]
    public partial class Sys_DashboardMapping:SysEntity
    {
        /// <summary>
       ///
       /// </summary>
       [Key]
       [Display(Name ="MappingId")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid MappingId { get; set; }

       /// <summary>
       ///面板Id
       /// </summary>
       [Display(Name ="面板Id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       public Guid? DashboardId { get; set; }

       /// <summary>
       ///用户
       /// </summary>
       [Display(Name ="用户")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? UserId { get; set; }

       /// <summary>
       ///部门
       /// </summary>
       [Display(Name ="部门")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       public Guid? DepartmentId { get; set; }

       /// <summary>
       ///角色
       /// </summary>
       [Display(Name ="角色")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? RoleId { get; set; }

       
    }
}