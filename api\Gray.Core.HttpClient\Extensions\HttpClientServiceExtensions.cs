using Gray.Core.GrayHttpClient.Handlers;

using Gray.Core.GrayHttpClient.Models;
using Gray.Core.GrayHttpClient.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Extensions.Http;
using Refit;
using System.Net;

namespace Gray.Core.GrayHttpClient.Extensions
{
    /// <summary>
    /// HTTP客户端服务扩展方法
    /// 遵循Gray.Core架构模式，提供配置驱动的服务注册
    /// </summary>
    public static class HttpClientServiceExtensions
    {
        /// <summary>
        /// 添加HTTP客户端服务
        /// 主要入口方法，遵循Gray.Core的扩展方法命名约定
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddHttpClientServices(this IServiceCollection services, IConfiguration configuration)
        {
            // 1. 注册配置选项
            services.Configure<HttpClientOptions>(configuration.GetSection("GrayHttpClient"));

            // 2. 注册核心服务
            services.AddSingleton<IHttpClientManager, HttpClientManager>();
            services.AddSingleton<ITokenManager, TokenManager>();
            services.AddSingleton<IUniversalTokenManager, UniversalTokenManager>();
            services.AddSingleton<UniversalAuthenticationHandlerFactory>();

            // 3. 注册示例服务
         
            services.AddScoped<Examples.IOTSharpRefitExample>();

            // 4. 注册处理器
            services.AddTransient<AuthenticationHandler>();
            services.AddTransient<LoggingHandler>();
            services.AddTransient<RetryHandler>();

            // 5. 配置HTTP客户端
            ConfigureHttpClients(services, configuration);

            // 6. 注册Refit客户端
            RegisterRefitClients(services, configuration);

            return services;
        }

        /// <summary>
        /// 配置HTTP客户端
        /// </summary>
        private static void ConfigureHttpClients(IServiceCollection services, IConfiguration configuration)
        {
            var httpClientOptions = configuration.GetSection("GrayHttpClient").Get<HttpClientOptions>();
            if (httpClientOptions?.Enabled != true)
            {
                return;
            }

            // 配置每个API端点的HTTP客户端
            foreach (var endpoint in httpClientOptions.ApiEndpoints)
            {
                var clientName = endpoint.Key;
                var endpointOptions = endpoint.Value;

                services.AddHttpClient(clientName, client =>
                {
                    client.BaseAddress = new Uri(endpointOptions.BaseUrl);
                    client.Timeout = TimeSpan.FromSeconds(endpointOptions.Timeout);

                    // 添加默认请求头
                    foreach (var header in endpointOptions.Headers)
                    {
                        client.DefaultRequestHeaders.Add(header.Key, header.Value);
                    }
                })
                .AddHttpMessageHandler(provider =>
                {
                    // 检查是否有对应的认证系统配置
                    if (endpointOptions.AuthenticationSystem != null)
                    {
                        var factory = provider.GetRequiredService<UniversalAuthenticationHandlerFactory>();
                        return factory.CreateHandler(endpointOptions.AuthenticationSystem);
                    }
                    else
                    {
                        // 使用默认的认证处理器
                        return provider.GetRequiredService<AuthenticationHandler>();
                    }
                })
                .AddHttpMessageHandler<LoggingHandler>()
                .AddPolicyHandler(GetRetryPolicy(httpClientOptions.Polly.Retry))
                .AddPolicyHandler(GetCircuitBreakerPolicy(httpClientOptions.Polly.CircuitBreaker))
                .AddPolicyHandler(GetTimeoutPolicy(httpClientOptions.Polly.Timeout));
            }
        }

        /// <summary>
        /// 注册Refit客户端
        /// </summary>
        private static void RegisterRefitClients(IServiceCollection services, IConfiguration configuration)
        {
            var httpClientOptions = configuration.GetSection("GrayHttpClient").Get<HttpClientOptions>();
            if (httpClientOptions?.Enabled != true)
            {
                return;
            }

            var refitSettings = new RefitSettings
            {
                ContentSerializer = new NewtonsoftJsonContentSerializer()
            };

            // 注册各个API客户端接口
            foreach (var endpoint in httpClientOptions.ApiEndpoints)
            {
                var clientName = endpoint.Key;
                var endpointOptions = endpoint.Value;

                // 根据端点名称注册对应的Refit客户端
                switch (clientName.ToLower())
                {
                    case "iotsharp":
                        services.AddRefitClient<Interfaces.IIOTSharpApiClient>(refitSettings)
                            .ConfigureHttpClient(client => client.BaseAddress = new Uri(endpointOptions.BaseUrl))
                            .AddHttpMessageHandler(provider =>
                            {
                                if (endpointOptions.AuthenticationSystem != null)
                                {
                                    var factory = provider.GetRequiredService<UniversalAuthenticationHandlerFactory>();
                                    return factory.CreateHandler(endpointOptions.AuthenticationSystem);
                                }
                                else
                                {
                                    return provider.GetRequiredService<AuthenticationHandler>();
                                }
                            })
                            .AddHttpMessageHandler<LoggingHandler>()
                            .AddPolicyHandler(GetRetryPolicy(httpClientOptions.Polly.Retry))
                            .AddPolicyHandler(GetCircuitBreakerPolicy(httpClientOptions.Polly.CircuitBreaker))
                            .AddPolicyHandler(GetTimeoutPolicy(httpClientOptions.Polly.Timeout));
                        break;

                    case "webapi":
                    case "gray.webapi":
                        services.AddRefitClient<Interfaces.IUserApiClient>(refitSettings)
                            .ConfigureHttpClient(client => client.BaseAddress = new Uri(endpointOptions.BaseUrl))
                            .AddHttpMessageHandler(provider =>
                            {
                                if (endpointOptions.AuthenticationSystem != null)
                                {
                                    var factory = provider.GetRequiredService<UniversalAuthenticationHandlerFactory>();
                                    return factory.CreateHandler(endpointOptions.AuthenticationSystem);
                                }
                                else
                                {
                                    return provider.GetRequiredService<AuthenticationHandler>();
                                }
                            })
                            .AddHttpMessageHandler<LoggingHandler>()
                            .AddPolicyHandler(GetRetryPolicy(httpClientOptions.Polly.Retry))
                            .AddPolicyHandler(GetCircuitBreakerPolicy(httpClientOptions.Polly.CircuitBreaker))
                            .AddPolicyHandler(GetTimeoutPolicy(httpClientOptions.Polly.Timeout));
                        break;

                    case "gateway":
                    case "gray.gateway":
                        services.AddRefitClient<Interfaces.IGatewayApiClient>(refitSettings)
                            .ConfigureHttpClient(client => client.BaseAddress = new Uri(endpointOptions.BaseUrl))
                            .AddHttpMessageHandler(provider =>
                            {
                                if (endpointOptions.AuthenticationSystem != null)
                                {
                                    var factory = provider.GetRequiredService<UniversalAuthenticationHandlerFactory>();
                                    return factory.CreateHandler(endpointOptions.AuthenticationSystem);
                                }
                                else
                                {
                                    return provider.GetRequiredService<AuthenticationHandler>();
                                }
                            })
                            .AddHttpMessageHandler<LoggingHandler>()
                            .AddPolicyHandler(GetRetryPolicy(httpClientOptions.Polly.Retry))
                            .AddPolicyHandler(GetCircuitBreakerPolicy(httpClientOptions.Polly.CircuitBreaker))
                            .AddPolicyHandler(GetTimeoutPolicy(httpClientOptions.Polly.Timeout));
                        break;

                    default:
                        // 对于其他端点，可以考虑注册一个通用的API客户端接口
                        // 或者通过配置文件指定具体的接口类型
                        break;
                }
            }
        }

        /// <summary>
        /// 获取重试策略
        /// </summary>
        private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy(RetryOptions retryOptions)
        {
            if (!retryOptions.Enabled)
            {
                return Policy.NoOpAsync<HttpResponseMessage>();
            }

            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .OrResult(msg => retryOptions.RetryOnHttpStatusCodes.Contains((int)msg.StatusCode))
                .WaitAndRetryAsync(
                    retryCount: retryOptions.MaxAttempts,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(
                        Math.Min(
                            retryOptions.DelaySeconds * Math.Pow(retryOptions.BackoffMultiplier, retryAttempt - 1),
                            retryOptions.MaxDelaySeconds
                        )
                    ),
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        var logger = context.GetLogger();
                        if (outcome.Exception != null)
                        {
                            logger?.LogWarning("HTTP请求重试 {RetryCount}/{MaxRetries}，延迟 {Delay}ms，异常: {Exception}",
                                retryCount, retryOptions.MaxAttempts, timespan.TotalMilliseconds, outcome.Exception.Message);
                        }
                        else
                        {
                            logger?.LogWarning("HTTP请求重试 {RetryCount}/{MaxRetries}，延迟 {Delay}ms，状态码: {StatusCode}",
                                retryCount, retryOptions.MaxAttempts, timespan.TotalMilliseconds, outcome.Result?.StatusCode);
                        }
                    });
        }

        /// <summary>
        /// 获取熔断器策略
        /// </summary>
        private static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy(CircuitBreakerOptions circuitBreakerOptions)
        {
            if (!circuitBreakerOptions.Enabled)
            {
                return Policy.NoOpAsync<HttpResponseMessage>();
            }

            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .CircuitBreakerAsync(
                    handledEventsAllowedBeforeBreaking: circuitBreakerOptions.FailureThreshold,
                    durationOfBreak: TimeSpan.FromSeconds(circuitBreakerOptions.DurationOfBreakSeconds),
                    onBreak: (exception, duration) =>
                    {
                        // 可以在这里添加日志记录
                    },
                    onReset: () =>
                    {
                        // 可以在这里添加日志记录
                    });
        }

        /// <summary>
        /// 获取超时策略
        /// </summary>
        private static IAsyncPolicy<HttpResponseMessage> GetTimeoutPolicy(TimeoutOptions timeoutOptions)
        {
            if (!timeoutOptions.Enabled)
            {
                return Policy.NoOpAsync<HttpResponseMessage>();
            }

            return Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(timeoutOptions.TimeoutSeconds));
        }
    }

    /// <summary>
    /// Polly上下文扩展方法
    /// </summary>
    internal static class PollyContextExtensions
    {
        private const string LoggerKey = "ILogger";

        public static Context WithLogger(this Context context, ILogger logger)
        {
            context[LoggerKey] = logger;
            return context;
        }

        public static ILogger? GetLogger(this Context context)
        {
            if (context.TryGetValue(LoggerKey, out var logger))
            {
                return logger as ILogger;
            }
            return null;
        }
    }
}
