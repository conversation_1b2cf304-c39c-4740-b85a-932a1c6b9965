/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Repository提供数据库操作，如果要增加数据库操作请在当前目录下Partial文件夹App_ExpertRepository编写代码
 */
using Gray.AppManager.IRepositories;
using Gray.Core.BaseProvider;
using Gray.Core.EFDbContext;
using Gray.Core.Extensions.AutofacManager;
using Gray.Core.UnitOfWorkMange;
using Gray.Entity.DomainModels;

namespace Gray.AppManager.Repositories
{
    public partial class App_ExpertRepository : RepositoryBase<App_Expert> , IApp_ExpertRepository
    {
    public App_ExpertRepository(IUnitOfWork dbContext)
    : base(dbContext)
    {

    }
    public static IApp_ExpertRepository Instance
    {
      get {  return AutofacContainerModule.GetService<IApp_ExpertRepository>(); } }
    }
}
