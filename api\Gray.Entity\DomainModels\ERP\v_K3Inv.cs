/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "即时库存表",TableName = "v_K3Inv",DBServer = "K3DataTransferDbContext")]
    public partial class v_K3Inv:K3DataTransferEntity
    {
        /// <summary>
       ///型号
       /// </summary>
       [Display(Name ="型号")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string 型号 { get; set; }

       /// <summary>
       ///轮胎编码
       /// </summary>
       [Key]
       [Display(Name ="轮胎编码")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string 轮胎编码 { get; set; }

       /// <summary>
       ///名称
       /// </summary>
       [Display(Name ="名称")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string 名称 { get; set; }

       /// <summary>
       ///数量
       /// </summary>
       [Display(Name ="数量")]
       [Column(TypeName="numeric")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public double 数量 { get; set; }

       /// <summary>
       ///库编码
       /// </summary>
       [Display(Name ="库编码")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       public string 库编码 { get; set; }

       /// <summary>
       ///库名
       /// </summary>
       [Display(Name ="库名")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       public string 库名 { get; set; }

       /// <summary>
       ///日期
       /// </summary>
       [Display(Name ="日期")]
       [MaxLength(30)]
       [Column(TypeName="nvarchar(30)")]
       [Editable(true)]
       public string 日期 { get; set; }

       /// <summary>
       ///品牌
       /// </summary>
       [Display(Name ="品牌")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       public string 品牌 { get; set; }

       
    }
}