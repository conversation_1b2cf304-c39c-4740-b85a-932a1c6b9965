﻿using Gray.Core.EFDbContext;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Gray.Core.DBManager
{
    public class DbContextScope : IDisposable
    {
        private readonly IServiceScope _serviceScope;
        public SysDbContext DbContext { get; }

        public DbContextScope(IServiceProvider serviceProvider)
        {
            if (serviceProvider == null)
                throw new ArgumentNullException(nameof(serviceProvider));

            _serviceScope = serviceProvider.CreateScope();
            DbContext = _serviceScope.ServiceProvider.GetRequiredService<SysDbContext>();
        }

        public void Dispose()
        {
            DbContext?.Dispose();
            _serviceScope?.Dispose();
        }
    }
}