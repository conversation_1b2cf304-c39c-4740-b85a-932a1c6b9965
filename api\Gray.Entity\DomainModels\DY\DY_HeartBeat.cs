/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "动平衡心跳检测",TableName = "DY_HeartBeat",DBServer = "JL_DYDbContext")]
    public partial class DY_HeartBeat:JL_DYEntity
    {
        /// <summary>
       ///检查时间
       /// </summary>
       [Display(Name ="检查时间")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public DateTime CheckTime { get; set; }

       /// <summary>
       ///机台id
       /// </summary>
       [Display(Name ="机台id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid MachineId { get; set; }

       /// <summary>
       ///机台名
       /// </summary>
       [Display(Name ="机台名")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string DBMachine { get; set; }

       /// <summary>
       ///是否在线
       /// </summary>
       [Display(Name ="是否在线")]
       [Column(TypeName="tinyint")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public byte IsOnline { get; set; }

       /// <summary>
       ///id
       /// </summary>
       [Key]
       [Display(Name ="id")]
       [Column(TypeName="bigint")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public long HeartBeatId { get; set; }

       
    }
}