﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using Gray.Core.Extensions;
using Gray.Core.Filters;
using Gray.Core.Middleware;
using Gray.Core.Services;
using Gray.Core.Utilities;
using Gray.Entity.DomainModels;
using System.Threading.Tasks;

namespace Gray.Core.Controllers.Basic
{
    [JWTAuthorize, ApiController]
    public class ApiBaseController<IServiceBase> : GrayController
    {
        protected IServiceBase Service;
        private WebResponseContent _baseWebResponseContent { get; set; }

        public ApiBaseController()
        {
        }

        public ApiBaseController(IServiceBase service)
        {
            Service = service;
        }

        public ApiBaseController(string projectName, string folder, string tablename, IServiceBase service)
        {
            Service = service;
        }

        [ActionLog("查询")]
        [ApiActionPermission(Enums.ActionPermissionOptions.Search)]
        [HttpPost, Route("GetPageData")]
        public virtual async Task<ActionResult> GetPageData([FromBody] PageDataOptions loadData)
        {
            return JsonNormal(await InvokeService("GetPageData", new object[] { loadData }));
        }

        /// <summary>
        /// 获取明细grid分页数据
        /// </summary>
        /// <param name="loadData"></param>
        /// <returns></returns>
        [ActionLog("明细查询")]
        [ApiActionPermission(Enums.ActionPermissionOptions.Search)]
        [HttpPost, Route("GetDetailPage")]
        [ApiExplorerSettings(IgnoreApi = true)]
        public virtual async Task<ActionResult> GetDetailPage([FromBody] PageDataOptions loadData)
        {
            return Content((await InvokeService("GetDetailPage", new object[] { loadData })).Serialize());
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="fileInput"></param>
        /// <returns></returns>
        [ActionLog("上传文件")]
        [HttpPost, Route("Upload")]
        [ApiActionPermission(Enums.ActionPermissionOptions.Upload)]
        [ApiExplorerSettings(IgnoreApi = true)]
        public virtual async Task<IActionResult> Upload(IEnumerable<IFormFile> fileInput)
        {
            return Json(await InvokeService("Upload", new object[] { fileInput }));
        }

        /// <summary>
        /// 下载导入Excel模板
        /// </summary>
        /// <returns></returns>
        [ActionLog("下载导入Excel模板")]
        [HttpGet, Route("DownLoadTemplate")]
        [ApiActionPermission(Enums.ActionPermissionOptions.Import)]
        [ApiExplorerSettings(IgnoreApi = true)]
        public virtual async Task<ActionResult> DownLoadTemplate()
        {
            _baseWebResponseContent =await InvokeService("DownLoadTemplate", new object[] { }) as WebResponseContent;
            if (!_baseWebResponseContent.Status) return Json(_baseWebResponseContent);
            byte[] fileBytes = System.IO.File.ReadAllBytes(_baseWebResponseContent.Data.ToString());
            return File(
                    fileBytes,
                    System.Net.Mime.MediaTypeNames.Application.Octet,
                    Path.GetFileName(_baseWebResponseContent.Data.ToString())
                );
        }

        /// <summary>
        /// 导入表数据Excel
        /// </summary>
        /// <param name="fileInput"></param>
        /// <returns></returns>
        [ActionLog("导入Excel")]
        [HttpPost, Route("Import")]
        [ApiActionPermission(Enums.ActionPermissionOptions.Import)]
        [ApiExplorerSettings(IgnoreApi = true)]
        public virtual ActionResult Import(List<IFormFile> fileInput)
        {
            return Json(InvokeService("Import", new object[] { fileInput }));
        }

        /// <summary>
        /// 导出文件，返回日期+文件名
        /// </summary>
        /// <param name="loadData"></param>
        /// <returns></returns>
        [ActionLog("导出Excel")]
        [ApiActionPermission(Enums.ActionPermissionOptions.Export)]
        [ApiExplorerSettings(IgnoreApi = true)]
        [HttpPost, Route("Export")]
        public virtual async Task<ActionResult> Export([FromBody] PageDataOptions loadData)
        {
            var result =await InvokeService("Export", new object[] { loadData }) as WebResponseContent;
            return File(
                   System.IO.File.ReadAllBytes(result.Data.ToString().MapPath()),
                   System.Net.Mime.MediaTypeNames.Application.Octet,
                   Path.GetFileName(result.Data.ToString())
               );
        }

        /// <summary>
        /// 通过key删除文件
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
       // [ActionLog("删除")]
        [ApiActionPermission(Enums.ActionPermissionOptions.Delete)]
        [HttpPost, Route("Del")]
        [ApiExplorerSettings(IgnoreApi = true)]
        public virtual async Task<ActionResult> Del([FromBody] object[] keys)
        {
            _baseWebResponseContent =await InvokeService("Del", new object[] { keys, true }) as WebResponseContent;
            Logger.Info(Enums.LoggerType.Del, keys.Serialize(), _baseWebResponseContent.Status ? "Ok" : _baseWebResponseContent.Message);
            return Json(_baseWebResponseContent);
        }

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        /// [ActionLog("审核")]
        [ApiActionPermission(Enums.ActionPermissionOptions.Audit)]
        [HttpPost, Route("Audit")]
        [ApiExplorerSettings(IgnoreApi = true)]
        public virtual async Task<ActionResult> Audit([FromBody] object[] id, int? auditStatus, string auditReason)
        {
            _baseWebResponseContent =await InvokeService("Audit", new object[] { id, auditStatus, auditReason }) as WebResponseContent;
            string msg = _baseWebResponseContent.Status ? ("Ok") : _baseWebResponseContent.Message;
            Logger.Info($"审核：{id?.Serialize() + "," + (auditStatus ?? -1) + "," + auditReason};{msg}");
            return Json(_baseWebResponseContent);
        }

        /// <summary>
        /// 反审核
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        /// [ActionLog("审核")]
        [ApiActionPermission(Enums.ActionPermissionOptions.Audit)]
        [HttpPost, Route("antiAudit")]
        [ApiExplorerSettings(IgnoreApi = true)]
        public virtual async Task<ActionResult> AntiAudit([FromBody] AntiData antiData)
        {
            _baseWebResponseContent =await InvokeService("AntiAudit", new object[] { antiData }) as WebResponseContent;
            string msg = _baseWebResponseContent.Status ? ("Ok") : _baseWebResponseContent.Message;
            Logger.Info($"反审核：{antiData.Serialize()};{msg}");
            return Json(_baseWebResponseContent);
        }

        /// <summary>
        /// 新增支持主子表
        /// </summary>
        /// <param name="saveDataModel"></param>
        /// <returns></returns>
        [ActionLog("新建")]
        [ApiActionPermission(Enums.ActionPermissionOptions.Add)]
        [HttpPost, Route("Add")]
        [ApiExplorerSettings(IgnoreApi = true)]
        public virtual async Task<ActionResult> Add([FromBody] SaveModel saveModel)
        {
            _baseWebResponseContent =await InvokeService("Add",
                new Type[] { typeof(SaveModel) },
                new object[] { saveModel }) as WebResponseContent;
            Logger.Info(Enums.LoggerType.Add, null, _baseWebResponseContent.Status ? "Ok" : _baseWebResponseContent.Message);
            _baseWebResponseContent.Data = _baseWebResponseContent.Data?.Serialize();
            return Json(_baseWebResponseContent);
        }

        /// <summary>
        /// 编辑支持主子表
        /// [ModelBinder(BinderType =(typeof(ModelBinder.BaseModelBinder)))]可指定绑定modelbinder
        /// </summary>
        /// <param name="saveDataModel"></param>
        /// <returns></returns>
        [ActionLog("编辑")]
        [ApiActionPermission(Enums.ActionPermissionOptions.Update)]
        [HttpPost, Route("Update")]
        [ApiExplorerSettings(IgnoreApi = true)]
        public virtual async Task<ActionResult> Update([FromBody] SaveModel saveModel)
        {
            _baseWebResponseContent =await InvokeService("Update", new object[] { saveModel }) as WebResponseContent;
            Logger.Info(Enums.LoggerType.Edit, null, _baseWebResponseContent.Status ? "Ok" : _baseWebResponseContent.Message);
            _baseWebResponseContent.Data = _baseWebResponseContent.Data?.Serialize();
            return Json(_baseWebResponseContent);
        }

      /// <summary>
/// 调用service方法,支持同步和异步方法
/// </summary>
/// <param name="methodName">方法名</param>
/// <param name="parameters">参数数组</param>
/// <returns>方法执行结果</returns>
private async Task<object> InvokeService(string methodName, object[] parameters)
{
    var method = Service.GetType().GetMethod(methodName);
    if (method == null)
    {
        throw new ArgumentException($"方法 {methodName} 未找到");
    }

    var result = method.Invoke(Service, parameters);
    
    // 处理异步方法
    if (result is Task task)
    {
        await task;
        // 如果是泛型Task,获取Result属性的值
        return task.GetType().GetProperty("Result")?.GetValue(task);
    }
    
    // 处理同步方法
    return result;
}

/// <summary>
/// 调用service重载方法,支持同步和异步方法
/// </summary>
/// <param name="methodName">方法名</param>
/// <param name="types">方法参数类型数组</param>
/// <param name="parameters">参数值数组</param>
/// <returns>方法执行结果</returns>
private async Task<object> InvokeService(string methodName, Type[] types, object[] parameters)
{
    var method = Service.GetType().GetMethod(methodName, types);
    if (method == null)
    {
        throw new ArgumentException($"方法 {methodName} 未找到");
    }

    var result = method.Invoke(Service, parameters);
    
    // 处理异步方法
    if (result is Task task)
    {
        await task;
        // 如果是泛型Task,获取Result属性的值
        return task.GetType().GetProperty("Result")?.GetValue(task);
    }
    
    // 处理同步方法
    return result;
}
    }
}