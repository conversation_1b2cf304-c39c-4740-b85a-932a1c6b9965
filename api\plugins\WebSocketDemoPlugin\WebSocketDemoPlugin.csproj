﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0</TargetFrameworks>
		<LangVersion>latest</LangVersion>
		<EnableDynamicLoading>true</EnableDynamicLoading>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.2.0">
			<ExcludeAssets>runtime</ExcludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.AspNetCore.WebSockets" Version="2.2.1" />
	</ItemGroup>

	<!-- 方便开发debug,与发布到nuget -->
	<ItemGroup Condition="'$(Configuration)' == 'Release'">
		<PackageReference Include="PluginCore.IPlugins.AspNetCore" Version="0.0.1">
			<ExcludeAssets>runtime</ExcludeAssets>
		</PackageReference>
	</ItemGroup>
	<ItemGroup Condition="'$(Configuration)' == 'Debug'">
		<ProjectReference Include="..\..\src\PluginCore.IPlugins.AspNetCore\PluginCore.IPlugins.AspNetCore.csproj">
			<Private>false</Private>
			<ExcludeAssets>runtime</ExcludeAssets>
		</ProjectReference>
	</ItemGroup>

	<!-- 发布插件相关文件 -->
	<ItemGroup>
		<Content Include="info.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="README.md">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="settings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<!-- 发布 wwwroot -->
	<ItemGroup>
		<Content Include="wwwroot\**\*">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

</Project>
