# AntDesign.Pro 集成示例

本文档展示如何在AntDesign.Pro项目中集成和使用Gray.Core.HttpClient模块。

## 1. 项目引用

在AntDesign.Pro项目中添加对Gray.Core.HttpClient的引用：

```xml
<!-- AntDesign.Pro.csproj -->
<ItemGroup>
  <ProjectReference Include="..\Gray.Core.HttpClient\Gray.Core.HttpClient.csproj" />
</ItemGroup>
```

## 2. 服务注册

在`Program.cs`中注册HTTP客户端服务：

```csharp
using Gray.Core.HttpClient.Extensions;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

// 注册HTTP客户端服务
builder.Services.AddHttpClientServices(builder.Configuration);

// 其他服务注册...

await builder.Build().RunAsync();
```

## 3. 配置文件

在`wwwroot`目录下创建`appsettings.json`：

```json
{
  "HttpClient": {
    "Enabled": true,
    "DefaultTimeout": 30,
    "EnableLogging": true,
    "LogLevel": "Information",
    
    "Authentication": {
      "Enabled": true,
      "Type": "JWT",
      "TokenRefreshThresholdMinutes": 5,
      "AutoRefreshToken": true,
      "LoginEndpoint": "/api/Login/GetJwtToken3",
      "RefreshEndpoint": "/api/Login/RefreshToken"
    },

    "ApiEndpoints": {
      "Gateway": {
        "Name": "Gray.Core.Gateway",
        "BaseUrl": "https://localhost:7001",
        "Timeout": 30,
        "EnableRetry": true,
        "MaxRetryAttempts": 3
      },
      "WebApi": {
        "Name": "Gray.WebApi",
        "BaseUrl": "https://localhost:7000",
        "Timeout": 30,
        "EnableRetry": true,
        "MaxRetryAttempts": 3
      }
    },

    "Polly": {
      "Retry": {
        "Enabled": true,
        "MaxAttempts": 3,
        "DelaySeconds": 2,
        "BackoffMultiplier": 2.0
      },
      "CircuitBreaker": {
        "Enabled": true,
        "FailureThreshold": 5,
        "DurationOfBreakSeconds": 30
      }
    },

    "Logging": {
      "LogRequests": true,
      "LogResponses": true,
      "LogHeaders": false,
      "LogBody": false,
      "LogSensitiveData": false
    }
  }
}
```

## 4. 创建登录服务

```csharp
// Services/AuthenticationService.cs
using Gray.Core.HttpClient.Interfaces;
using Gray.Core.HttpClient.Services;

namespace AntDesign.Pro.Services
{
    public interface IAuthenticationService
    {
        Task<bool> LoginAsync(string username, string password);
        Task<bool> LoginWithCodeAsync(string username, string password, string code, string uuid);
        Task LogoutAsync();
        Task<bool> IsAuthenticatedAsync();
        Task<UserInfo?> GetCurrentUserAsync();
    }

    public class AuthenticationService : IAuthenticationService
    {
        private readonly IUserApiClient _userApiClient;
        private readonly ITokenManager _tokenManager;
        private readonly ILogger<AuthenticationService> _logger;

        public AuthenticationService(
            IUserApiClient userApiClient,
            ITokenManager tokenManager,
            ILogger<AuthenticationService> logger)
        {
            _userApiClient = userApiClient;
            _tokenManager = tokenManager;
            _logger = logger;
        }

        public async Task<bool> LoginAsync(string username, string password)
        {
            try
            {
                var loginInfo = new LoginWithoutCodeInfo
                {
                    UserName = username,
                    Password = password
                };

                var response = await _userApiClient.LoginWithoutCodeAsync(loginInfo);
                
                if (response.IsSuccessStatusCode && response.Content?.Status == true)
                {
                    // 保存Token
                    await _tokenManager.SetTokenAsync(
                        response.Content.Token,
                        response.Content.TokenInfo?.TokenValue,
                        (int?)response.Content.TokenInfo?.TokenTimeout
                    );

                    _logger.LogInformation("用户 {Username} 登录成功", username);
                    return true;
                }

                _logger.LogWarning("用户 {Username} 登录失败: {Message}", username, response.Content?.Message);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登录过程中发生异常");
                return false;
            }
        }

        public async Task<bool> LoginWithCodeAsync(string username, string password, string code, string uuid)
        {
            try
            {
                var loginInfo = new LoginInfo
                {
                    UserName = username,
                    Password = password,
                    VerificationCode = code,
                    UUID = uuid
                };

                var response = await _userApiClient.LoginAsync(loginInfo);
                
                if (response.IsSuccessStatusCode && response.Content?.Status == true)
                {
                    await _tokenManager.SetTokenAsync(
                        response.Content.Token,
                        response.Content.TokenInfo?.TokenValue,
                        (int?)response.Content.TokenInfo?.TokenTimeout
                    );

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证码登录过程中发生异常");
                return false;
            }
        }

        public async Task LogoutAsync()
        {
            await _tokenManager.ClearTokenAsync();
            _logger.LogInformation("用户已登出");
        }

        public async Task<bool> IsAuthenticatedAsync()
        {
            return await _tokenManager.IsTokenValidAsync();
        }

        public async Task<UserInfo?> GetCurrentUserAsync()
        {
            try
            {
                if (!await IsAuthenticatedAsync())
                {
                    return null;
                }

                var response = await _userApiClient.GetCurrentUserInfoAsync();
                return response.IsSuccessStatusCode ? response.Content : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取当前用户信息时发生异常");
                return null;
            }
        }
    }
}
```

## 5. 创建用户管理服务

```csharp
// Services/UserManagementService.cs
using Gray.Core.HttpClient.Interfaces;

namespace AntDesign.Pro.Services
{
    public interface IUserManagementService
    {
        Task<PageGridData<UserInfo>> GetUsersAsync(int page = 1, int pageSize = 20);
        Task<bool> AddUserAsync(UserInfo user);
        Task<bool> UpdateUserAsync(UserInfo user);
        Task<bool> DeleteUserAsync(int userId);
    }

    public class UserManagementService : IUserManagementService
    {
        private readonly IUserApiClient _userApiClient;
        private readonly ILogger<UserManagementService> _logger;

        public UserManagementService(
            IUserApiClient userApiClient,
            ILogger<UserManagementService> logger)
        {
            _userApiClient = userApiClient;
            _logger = logger;
        }

        public async Task<PageGridData<UserInfo>> GetUsersAsync(int page = 1, int pageSize = 20)
        {
            try
            {
                var pageOptions = new PageDataOptions
                {
                    Page = page,
                    Rows = pageSize,
                    Sort = "User_Id",
                    Order = "asc"
                };

                var response = await _userApiClient.GetUserPageDataAsync(pageOptions);
                return response.IsSuccessStatusCode ? response.Content : new PageGridData<UserInfo>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户列表时发生异常");
                return new PageGridData<UserInfo>();
            }
        }

        public async Task<bool> AddUserAsync(UserInfo user)
        {
            try
            {
                var saveModel = new SaveModel
                {
                    MainData = user
                };

                var response = await _userApiClient.AddUserAsync(saveModel);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加用户时发生异常");
                return false;
            }
        }

        public async Task<bool> UpdateUserAsync(UserInfo user)
        {
            try
            {
                var saveModel = new SaveModel
                {
                    MainData = user
                };

                var response = await _userApiClient.UpdateUserAsync(saveModel);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户时发生异常");
                return false;
            }
        }

        public async Task<bool> DeleteUserAsync(int userId)
        {
            try
            {
                var deleteModel = new DeleteModel
                {
                    Keys = new List<object> { userId }
                };

                var response = await _userApiClient.DeleteUserAsync(deleteModel);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除用户时发生异常");
                return false;
            }
        }
    }
}
```

## 6. 在Blazor组件中使用

```razor
@* Pages/Login.razor *@
@page "/login"
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation
@inject IMessageService Message

<div class="login-container">
    <Form Model="loginModel" OnFinish="HandleLogin">
        <FormItem>
            <Input @bind-Value="loginModel.Username" Placeholder="用户名" />
        </FormItem>
        <FormItem>
            <InputPassword @bind-Value="loginModel.Password" Placeholder="密码" />
        </FormItem>
        <FormItem>
            <Button Type="primary" HtmlType="submit" Loading="isLoading" Block>
                登录
            </Button>
        </FormItem>
    </Form>
</div>

@code {
    private LoginModel loginModel = new();
    private bool isLoading = false;

    private async Task HandleLogin()
    {
        isLoading = true;
        try
        {
            var success = await AuthService.LoginAsync(loginModel.Username, loginModel.Password);
            if (success)
            {
                await Message.Success("登录成功");
                Navigation.NavigateTo("/");
            }
            else
            {
                await Message.Error("登录失败，请检查用户名和密码");
            }
        }
        finally
        {
            isLoading = false;
        }
    }

    public class LoginModel
    {
        public string Username { get; set; } = "";
        public string Password { get; set; } = "";
    }
}
```

## 7. 注册自定义服务

在`Program.cs`中注册自定义服务：

```csharp
// 注册自定义服务
builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();
builder.Services.AddScoped<IUserManagementService, UserManagementService>();
```

## 8. 健康检查组件

```razor
@* Components/ApiHealthCheck.razor *@
@inject IHttpClientManager ClientManager
@inject ILogger<ApiHealthCheck> Logger

<div class="health-check">
    <h4>API健康状态</h4>
    @foreach (var client in clientNames)
    {
        <div class="health-item">
            <span>@client:</span>
            <Tag Color="@(healthStatus.ContainsKey(client) && healthStatus[client].IsHealthy ? "green" : "red")">
                @(healthStatus.ContainsKey(client) ? (healthStatus[client].IsHealthy ? "健康" : "异常") : "检查中...")
            </Tag>
        </div>
    }
</div>

@code {
    private readonly string[] clientNames = { "Gateway", "WebApi" };
    private Dictionary<string, ClientHealthStatus> healthStatus = new();

    protected override async Task OnInitializedAsync()
    {
        await CheckHealthAsync();
    }

    private async Task CheckHealthAsync()
    {
        foreach (var clientName in clientNames)
        {
            try
            {
                var health = await ClientManager.GetClientHealthAsync(clientName);
                healthStatus[clientName] = health;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "检查 {ClientName} 健康状态时发生异常", clientName);
                healthStatus[clientName] = new ClientHealthStatus
                {
                    ClientName = clientName,
                    IsHealthy = false,
                    Description = "检查失败",
                    ErrorMessage = ex.Message
                };
            }
        }
        StateHasChanged();
    }
}
```

这个集成示例展示了如何在AntDesign.Pro项目中完整地使用Gray.Core.HttpClient模块，包括服务注册、配置、业务服务封装和UI组件使用。
