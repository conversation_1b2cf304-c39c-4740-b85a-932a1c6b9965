using Newtonsoft.Json;
/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "发货订单表",TableName = "[Order]",DetailTable =  new Type[] { typeof(OrderList)},DetailTableCnName = "发货订单详情",DBServer = "JL_CRMDbContext")]
    public partial class Order:JL_CRMEntity
    {
        /// <summary>
       ///订单Id
       /// </summary>
       [Key]
       [Display(Name ="订单Id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid OrderId { get; set; }

       /// <summary>
       ///客户大区
       /// </summary>
       [Display(Name ="客户大区")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string CustomerArea { get; set; }

       /// <summary>
       ///订单号
       /// </summary>
       [Display(Name ="订单号")]
       [MaxLength(100)]
       [Column(TypeName="varchar(100)")]
       [Editable(true)]
       public string OrderNo { get; set; }

       /// <summary>
       ///审核状态
       /// </summary>
       [Display(Name ="审核状态")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? AuditStatus { get; set; }

       /// <summary>
       ///订单种类
       /// </summary>
       [Display(Name ="订单种类")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string OrderType { get; set; }

       /// <summary>
       ///总数量
       /// </summary>
       [Display(Name ="总数量")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? TotalQty { get; set; }

       /// <summary>
       ///总价
       /// </summary>
       [Display(Name ="总价")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? TotalPrice { get; set; }

       /// <summary>
       ///总应收账款
       /// </summary>
       [Display(Name ="总应收账款")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? TotalReceivables { get; set; }

       /// <summary>
       ///使用普通理赔款
       /// </summary>
       [Display(Name ="使用普通理赔款")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? PTClaimAccounts { get; set; }

       /// <summary>
       ///使用商务理赔款
       /// </summary>
       [Display(Name ="使用商务理赔款")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? SWClaimAccounts { get; set; }

       /// <summary>
       ///总理赔账款
       /// </summary>
       [Display(Name ="总理赔账款")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? TotalClaimAccounts { get; set; }

       /// <summary>
       ///理赔账款可用
       /// </summary>
       [Display(Name ="理赔账款可用")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? ClaimAccountsMax { get; set; }

       /// <summary>
       ///冲返利类型
       /// </summary>
       [Display(Name ="冲返利类型")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       public string RebateType { get; set; }

       /// <summary>
       ///总返利上限
       /// </summary>
       [Display(Name ="总返利上限")]
       [JsonIgnore]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? RebateMax { get; set; }

       /// <summary>
       ///总冲返利
       /// </summary>
       [Display(Name ="总冲返利")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? TotalRebate { get; set; }

       /// <summary>
       ///使用月度返利
       /// </summary>
       [Display(Name ="使用月度返利")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? RebateMonth { get; set; }

       /// <summary>
       ///使用季度返利
       /// </summary>
       [Display(Name ="使用季度返利")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? RebateQuarter { get; set; }

       /// <summary>
       ///使用年度返利
       /// </summary>
       [Display(Name ="使用年度返利")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? ReabtYear { get; set; }

       /// <summary>
       ///可用月度返利
       /// </summary>
       [Display(Name ="可用月度返利")]
       [JsonIgnore]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? RebateMonthMax { get; set; }

       /// <summary>
       ///可用季度返利
       /// </summary>
       [Display(Name ="可用季度返利")]
       [JsonIgnore]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? RebateQuarterMax { get; set; }

       /// <summary>
       ///可用年度返利
       /// </summary>
       [Display(Name ="可用年度返利")]
       [JsonIgnore]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? ReabtYearMax { get; set; }

       /// <summary>
       ///总重
       /// </summary>
       [Display(Name ="总重")]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? TotalWeight { get; set; }

       /// <summary>
       ///订单日期
       /// </summary>
       [Display(Name ="订单日期")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public DateTime OrderDate { get; set; }

       /// <summary>
       ///客户代码
       /// </summary>
       [Display(Name ="客户代码")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string CustomerCode { get; set; }

       /// <summary>
       ///客户名称
       /// </summary>
       [Display(Name ="客户名称")]
       [MaxLength(500)]
       [Column(TypeName="nvarchar(500)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string CustomerName { get; set; }

       /// <summary>
       ///客户地址
       /// </summary>
       [Display(Name ="客户地址")]
       [MaxLength(600)]
       [Column(TypeName="nvarchar(600)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string CustomerAddress { get; set; }

       /// <summary>
       ///客户地址Id
       /// </summary>
       [Display(Name ="客户地址Id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       public Guid? CustomerAddressId { get; set; }

       /// <summary>
       ///客户姓名
       /// </summary>
       [Display(Name ="客户姓名")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string CustomerXM { get; set; }

       /// <summary>
       ///包装要求
       /// </summary>
       [Display(Name ="包装要求")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string PackingRequirment { get; set; }

       /// <summary>
       ///电话号码
       /// </summary>
       [Display(Name ="电话号码")]
       [MaxLength(50)]
       [Column(TypeName="nvarchar(50)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string PhoneNo { get; set; }

       /// <summary>
       ///订单状态
       /// </summary>
       [Display(Name ="订单状态")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? OrderStatus { get; set; }

       /// <summary>
       ///备注
       /// </summary>
       [Display(Name ="备注")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       public string Remark { get; set; }

       /// <summary>
       ///发货优先级
       /// </summary>
       [Display(Name ="发货优先级")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? Priority { get; set; }

       /// <summary>
       ///预订单Id
       /// </summary>
       [Display(Name ="预订单Id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       public Guid? PreOrderId { get; set; }

       /// <summary>
       ///客户账款余额
       /// </summary>
       [Display(Name ="客户账款余额")]
       [JsonIgnore]
       [Column(TypeName="float")]
       [Editable(true)]
       public float? KHZK { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="AuditId")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? AuditId { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Auditor")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       public string Auditor { get; set; }

       /// <summary>
       ///审核日期
       /// </summary>
       [Display(Name ="审核日期")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? AuditDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="AuditReason")]
       [MaxLength(500)]
       [Column(TypeName="nvarchar(500)")]
       [Editable(true)]
       public string AuditReason { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? CreateID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Creator")]
       [MaxLength(30)]
       [Column(TypeName="nvarchar(30)")]
       [Editable(true)]
       public string Creator { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="CreateDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? CreateDate { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ModifyID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="Modifier")]
       [MaxLength(30)]
       [Column(TypeName="nvarchar(30)")]
       [Editable(true)]
       public string Modifier { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="ModifyDate")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? ModifyDate { get; set; }

       /// <summary>
       ///ERP单号
       /// </summary>
       [Display(Name ="ERP单号")]
       [MaxLength(200)]
       [Column(TypeName="nvarchar(200)")]
       [Editable(true)]
       public string ERPNO { get; set; }

       [Display(Name ="发货订单详情")]
       [ForeignKey("OrderId")]
       public List<OrderList> OrderList { get; set; }


       
    }
}
