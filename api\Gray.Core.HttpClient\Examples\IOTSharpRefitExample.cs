using Gray.Core.GrayHttpClient.Interfaces;
using Gray.Core.GrayHttpClient.Models;
using Microsoft.Extensions.Logging;

namespace Gray.Core.GrayHttpClient.Examples
{
    /// <summary>
    /// IOTSharp物联网平台Refit集成示例
    /// 展示如何通过Refit接口对接IOTSharp平台
    /// 这是推荐的实现方式，符合Refit设计思想
    /// </summary>
    public class IOTSharpRefitExample 
    {
        private readonly IIOTSharpApiClient _iotSharpClient;
        private readonly ILogger<IOTSharpRefitExample> _logger;

        public IOTSharpRefitExample(
            IIOTSharpApiClient iotSharpClient,
            ILogger<IOTSharpRefitExample> logger)
        {
            _iotSharpClient = iotSharpClient;
            _logger = logger;
        }

        /// <summary>
        /// 获取设备最新遥测数据
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>遥测数据列表</returns>
        public async Task<List<TelemetryData>?> GetDeviceTelemetryLatestAsync(string deviceId)
        {
            try
            {
                _logger.LogInformation("开始获取设备 {DeviceId} 的遥测数据", deviceId);
                
                // 使用Refit客户端调用API
                var response = await _iotSharpClient.GetDeviceTelemetryLatestAsync(deviceId);
                
                if (response.IsSuccessStatusCode && response.Content != null)
                {
                    var apiResponse = response.Content;
                    
                    if (apiResponse.Code == 10000 && apiResponse.Data != null)
                    {
                        _logger.LogInformation("成功获取设备 {DeviceId} 的 {Count} 条遥测数据", 
                            deviceId, apiResponse.Data.Count);
                        return apiResponse.Data;
                    }
                    else
                    {
                        _logger.LogWarning("API返回错误：{Code} - {Message}", apiResponse.Code, apiResponse.Msg);
                        return null;
                    }
                }
                else
                {
                    _logger.LogWarning("获取遥测数据失败，状态码: {StatusCode}", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备 {DeviceId} 遥测数据时发生异常", deviceId);
                return null;
            }
        }

        /// <summary>
        /// 获取温湿度数据（从遥测数据中提取）
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>温湿度数据</returns>
        public async Task<TemperatureHumidityData?> GetTemperatureHumidityAsync(string deviceId)
        {
            try
            {
                var telemetryData = await GetDeviceTelemetryLatestAsync(deviceId);
                if (telemetryData == null || !telemetryData.Any())
                {
                    _logger.LogWarning("设备 {DeviceId} 没有遥测数据", deviceId);
                    return null;
                }

                var result = new TemperatureHumidityData
                {
                    DeviceId = deviceId,
                    Timestamp = DateTime.UtcNow
                };

                // 提取温度数据
                var temperatureItem = telemetryData.FirstOrDefault(t => 
                    t.KeyName.Equals("temperature", StringComparison.OrdinalIgnoreCase));
                if (temperatureItem != null && float.TryParse(temperatureItem.Value?.ToString(), out var temperature))
                {
                    result.Temperature = temperature;
                    result.TemperatureTime = temperatureItem.DateTime;
                }

                // 提取湿度数据
                var humidityItem = telemetryData.FirstOrDefault(t => 
                    t.KeyName.Equals("humidity", StringComparison.OrdinalIgnoreCase));
                if (humidityItem != null && float.TryParse(humidityItem.Value?.ToString(), out var humidity))
                {
                    result.Humidity = humidity;
                    result.HumidityTime = humidityItem.DateTime;
                }

                // 提取时间戳
                var timestampItem = telemetryData.FirstOrDefault(t => 
                    t.KeyName.Equals("timestamp", StringComparison.OrdinalIgnoreCase));
                if (timestampItem != null && DateTime.TryParse(timestampItem.Value?.ToString(), out var timestamp))
                {
                    result.DeviceTimestamp = timestamp;
                }

                _logger.LogInformation("设备 {DeviceId} 温度: {Temperature}°C, 湿度: {Humidity}%", 
                    deviceId, result.Temperature, result.Humidity);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备 {DeviceId} 温湿度数据时发生异常", deviceId);
                return null;
            }
        }

        /// <summary>
        /// 批量获取多个设备的遥测数据
        /// </summary>
        /// <param name="deviceIds">设备ID列表</param>
        /// <returns>设备遥测数据字典</returns>
        public async Task<Dictionary<string, List<TelemetryData>>> GetMultipleDevicesTelemetryAsync(List<string> deviceIds)
        {
            var results = new Dictionary<string, List<TelemetryData>>();

            // 并发获取多个设备的数据
            var tasks = deviceIds.Select(async deviceId =>
            {
                try
                {
                    var telemetryData = await GetDeviceTelemetryLatestAsync(deviceId);
                    return new { DeviceId = deviceId, Data = telemetryData };
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取设备 {DeviceId} 数据失败", deviceId);
                    return new { DeviceId = deviceId, Data = (List<TelemetryData>?)null };
                }
            });

            var taskResults = await Task.WhenAll(tasks);

            foreach (var result in taskResults)
            {
                if (result.Data != null)
                {
                    results[result.DeviceId] = result.Data;
                }
            }

            _logger.LogInformation("批量获取完成，成功获取 {SuccessCount}/{TotalCount} 个设备的数据", 
                results.Count, deviceIds.Count);

            return results;
        }

        /// <summary>
        /// 发送设备命令
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="commandName">命令名称</param>
        /// <param name="parameters">命令参数</param>
        /// <returns>命令执行结果</returns>
        public async Task<bool> SendDeviceCommandAsync(string deviceId, string commandName, Dictionary<string, object> parameters)
        {
            try
            {
                var command = new DeviceCommand
                {
                    CommandName = commandName,
                    Parameters = parameters
                };

                _logger.LogInformation("发送设备命令 {DeviceId}: {CommandName}", deviceId, commandName);
                
                var response = await _iotSharpClient.SendDeviceCommandAsync(deviceId, command);
                
                if (response.IsSuccessStatusCode && response.Content != null)
                {
                    var apiResponse = response.Content;
                    if (apiResponse.Code == 10000)
                    {
                        _logger.LogInformation("设备命令发送成功 {DeviceId}: {CommandName}", deviceId, commandName);
                        return true;
                    }
                    else
                    {
                        _logger.LogWarning("设备命令发送失败 {DeviceId}: {Code} - {Message}", 
                            deviceId, apiResponse.Code, apiResponse.Msg);
                        return false;
                    }
                }
                else
                {
                    _logger.LogWarning("设备命令发送失败，状态码: {StatusCode}", response.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送设备 {DeviceId} 命令时发生异常", deviceId);
                return false;
            }
        }

        /// <summary>
        /// 设置设备属性
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="attributes">属性数据</param>
        /// <returns>设置结果</returns>
        public async Task<bool> SetDeviceAttributesAsync(string deviceId, Dictionary<string, object> attributes)
        {
            try
            {
                _logger.LogInformation("设置设备属性 {DeviceId}", deviceId);
                
                var response = await _iotSharpClient.SetDeviceAttributesAsync(deviceId, attributes);
                
                if (response.IsSuccessStatusCode && response.Content != null)
                {
                    var apiResponse = response.Content;
                    if (apiResponse.Code == 10000)
                    {
                        _logger.LogInformation("设备属性设置成功 {DeviceId}", deviceId);
                        return true;
                    }
                    else
                    {
                        _logger.LogWarning("设备属性设置失败 {DeviceId}: {Code} - {Message}", 
                            deviceId, apiResponse.Code, apiResponse.Msg);
                        return false;
                    }
                }
                else
                {
                    _logger.LogWarning("设备属性设置失败，状态码: {StatusCode}", response.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置设备 {DeviceId} 属性时发生异常", deviceId);
                return false;
            }
        }

        /// <summary>
        /// 获取设备历史遥测数据
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="keyName">遥测键名</param>
        /// <returns>历史遥测数据</returns>
        public async Task<List<TelemetryData>?> GetDeviceTelemetryHistoryAsync(
            string deviceId, 
            DateTime? startTime = null, 
            DateTime? endTime = null, 
            string? keyName = null)
        {
            try
            {
                _logger.LogInformation("获取设备 {DeviceId} 历史遥测数据", deviceId);
                
                var response = await _iotSharpClient.GetDeviceTelemetryHistoryAsync(deviceId, startTime, endTime, keyName);
                
                if (response.IsSuccessStatusCode && response.Content != null)
                {
                    var apiResponse = response.Content;
                    
                    if (apiResponse.Code == 10000 && apiResponse.Data != null)
                    {
                        _logger.LogInformation("成功获取设备 {DeviceId} 的 {Count} 条历史遥测数据", 
                            deviceId, apiResponse.Data.Count);
                        return apiResponse.Data;
                    }
                    else
                    {
                        _logger.LogWarning("API返回错误：{Code} - {Message}", apiResponse.Code, apiResponse.Msg);
                        return null;
                    }
                }
                else
                {
                    _logger.LogWarning("获取历史遥测数据失败，状态码: {StatusCode}", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备 {DeviceId} 历史遥测数据时发生异常", deviceId);
                return null;
            }
        }

        /// <summary>
        /// 检查IOTSharp连接状态
        /// </summary>
        public async Task<bool> CheckIOTSharpConnectionAsync()
        {
            try
            {
                _logger.LogInformation("检查IOTSharp连接状态");
                
                // 使用账户信息API来验证连接和认证状态
                var response = await _iotSharpClient.GetAccountMyInfoAsync();
                
                var isConnected = response.IsSuccessStatusCode;
                
                if (isConnected)
                {
                    _logger.LogInformation("IOTSharp连接状态: 正常 (状态码: {StatusCode})", response.StatusCode);
                    
                    // 记录用户信息以便调试
                    if (response.Content?.Data != null)
                    {
                        var userInfo = response.Content.Data;
                        _logger.LogInformation("当前用户: {Name} ({Email}), 角色: {Roles}", 
                            userInfo.Name, userInfo.Email, userInfo.Roles);
                    }
                }
                else
                {
                    _logger.LogWarning("IOTSharp连接状态: 失败 (状态码: {StatusCode})", response.StatusCode);
                    
                    // 记录响应内容以便调试
                    if (response.Error != null)
                    {
                        _logger.LogWarning("连接测试失败详情: {Error}", response.Error.Content);
                    }
                }

                return isConnected;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查IOTSharp连接时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 获取用户账户信息
        /// </summary>
        /// <returns>用户账户信息</returns>
        public async Task<IOTSharpUserInfo?> GetUserInfoAsync()
        {
            try
            {
                _logger.LogInformation("获取用户账户信息");
                
                var response = await _iotSharpClient.GetAccountMyInfoAsync();
                
                if (response.IsSuccessStatusCode && response.Content != null)
                {
                    var apiResponse = response.Content;
                    
                    if (apiResponse.Code == 10000 && apiResponse.Data != null)
                    {
                        _logger.LogInformation("成功获取用户信息: {Name} ({Email})", 
                            apiResponse.Data.Name, apiResponse.Data.Email);
                        return apiResponse.Data;
                    }
                    else
                    {
                        _logger.LogWarning("获取用户信息失败：{Code} - {Message}", apiResponse.Code, apiResponse.Msg);
                        return null;
                    }
                }
                else
                {
                    _logger.LogWarning("获取用户信息失败，状态码: {StatusCode}", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息时发生异常");
                return null;
            }
        }

        /// <summary>
        /// 获取设备特定时间范围内的遥测数据
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="keys">遥测数据键名（多个键用逗号分隔）</param>
        /// <param name="beginTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>遥测数据详细列表</returns>
        public async Task<List<TelemetryDataDetail>?> GetDeviceTelemetryDataRangeAsync(
            string deviceId, 
            string keys, 
            DateTime beginTime, 
            DateTime endTime)
        {
            try
            {
                _logger.LogInformation("获取设备 {DeviceId} 时间范围 {BeginTime} - {EndTime} 的遥测数据，键: {Keys}", 
                    deviceId, beginTime, endTime, keys);
                
                // 将DateTime转换为IOTSharp期望的格式
                var begin = beginTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                var end = endTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                
                var response = await _iotSharpClient.GetDeviceTelemetryDataRangeAsync(deviceId, keys, begin, end);
                
                if (response.IsSuccessStatusCode && response.Content != null)
                {
                    var apiResponse = response.Content;
                    
                    if (apiResponse.Code == 10000 && apiResponse.Data != null)
                    {
                        _logger.LogInformation("成功获取设备 {DeviceId} 的 {Count} 条时间范围遥测数据", 
                            deviceId, apiResponse.Data.Count);
                        return apiResponse.Data;
                    }
                    else
                    {
                        _logger.LogWarning("获取时间范围遥测数据失败：{Code} - {Message}", apiResponse.Code, apiResponse.Msg);
                        return null;
                    }
                }
                else
                {
                    _logger.LogWarning("获取时间范围遥测数据失败，状态码: {StatusCode}", response.StatusCode);
                    
                    // 如果是错误响应，尝试记录错误详情
                    if (response.Error != null)
                    {
                        _logger.LogWarning("错误详情: {ErrorContent}", response.Error.Content);
                    }
                    
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备 {DeviceId} 时间范围遥测数据时发生异常", deviceId);
                return null;
            }
        }

        /// <summary>
        /// 获取设备最近指定小时数内的遥测数据
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <param name="keys">遥测数据键名（多个键用逗号分隔）</param>
        /// <param name="hours">最近小时数，默认24小时</param>
        /// <returns>遥测数据详细列表</returns>
        public async Task<List<TelemetryDataDetail>?> GetDeviceRecentTelemetryDataAsync(
            string deviceId, 
            string keys, 
            int hours = 24)
        {
            var endTime = DateTime.UtcNow;
            var beginTime = endTime.AddHours(-hours);
            
            _logger.LogInformation("获取设备 {DeviceId} 最近 {Hours} 小时的遥测数据，键: {Keys}", 
                deviceId, hours, keys);
            
            return await GetDeviceTelemetryDataRangeAsync(deviceId, keys, beginTime, endTime);
        }
    }
} 