﻿using Gray.Core.DBManager;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.SystemModels;
using Microsoft.EntityFrameworkCore;
using System;

namespace Gray.Core.EFDbContext
{
    public class JL_WMSDbContext : BaseDbContext<JL_WMSDbContext>, IDependency
    {
        protected override string ConnectionString
        {
            get
            {
                return DBServerProvider.GetConnection(this.GetType().Name);
            }
        }

        public JL_WMSDbContext() : base()
        {
        }

        public JL_WMSDbContext(DbContextOptions<JL_WMSDbContext> options) : base(options)
        {
        }

       

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
           base.UseDbTypeFromConfig(optionsBuilder, this.GetType().Name);
            //默认禁用实体跟踪
            optionsBuilder = optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            base.OnConfiguring(optionsBuilder);
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 为所有实体设置非默认架构
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                modelBuilder.Entity(entityType.ClrType).ToTable(entityType.ClrType.Name, "sewms");
            }

            // 或者为特定实体设置非默认架构
            //   builder.Entity<YourEntity>().ToTable("YourEntityTableName", "nonDefaultSchema");
        }

        protected override Type GetBaseEntityType()
        {
            // 默认返回null，由派生类实现
            return typeof(JL_WMSEntity);
        }
    }
}