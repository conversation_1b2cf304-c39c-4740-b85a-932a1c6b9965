﻿using EntityFrameworkCore.UseRowNumberForPaging;
using Gray.Core.Const;
using Gray.Core.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Debug;
using System;

namespace Gray.Core.DBManager
{
    /// <summary>
    /// 数据库类型配置器
    /// </summary>
    public static class DbTypeConfigurer
    {
        /// <summary>
        /// 配置数据库选项
        /// </summary>
        public static void ConfigureDbType(
            DbContextOptionsBuilder optionsBuilder,
            string connectionString,
            string dbType = null,
            string dbversion = "",
            bool enableDetailedLogging = false)
        {
            if (optionsBuilder == null)
            {
                throw new ArgumentNullException(nameof(optionsBuilder));
            }

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new ArgumentException("连接字符串不能为空", nameof(connectionString));
            }

            if (string.IsNullOrEmpty(dbType))
            {
                dbType = DBType.Name;
            }

            try
            {
                switch (dbType)
                {
                    case var t when t.Equals(DbCurrentType.MsSql.ToString(), StringComparison.OrdinalIgnoreCase):
                        ConfigureSqlServer(optionsBuilder, connectionString, dbversion, enableDetailedLogging);
                        break;

                    case var t when t.Equals(DbCurrentType.MySql.ToString(), StringComparison.OrdinalIgnoreCase):
                        ConfigureMySql(optionsBuilder, connectionString);
                        break;

                    case var t when t.Equals(DbCurrentType.PgSql.ToString(), StringComparison.OrdinalIgnoreCase):
                        ConfigurePostgreSQL(optionsBuilder, connectionString);
                        break;

                    case var t when t.Equals(DbCurrentType.Oracle.ToString(), StringComparison.OrdinalIgnoreCase):
                        ConfigureOracle(optionsBuilder, connectionString);
                        break;

                    default:
                        throw new ArgumentException($"不支持的数据库类型: {dbType}");
                }

                // 添加通用配置
                ConfigureCommonOptions(optionsBuilder, enableDetailedLogging);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"配置数据库类型 {dbType} 失败", ex);
            }
        }

        private static void ConfigureSqlServer(
            DbContextOptionsBuilder optionsBuilder,
            string connectionString,
            string version,
            bool enableDetailedLogging)
        {
            var builder = optionsBuilder.UseSqlServer(connectionString, options =>
            {
                // 配置SQL Server特定选项
                options.CommandTimeout(120); // 设置命令超时为120秒
                
                if (version == "2008")
                {
                    options.UseRowNumberForPaging();
                }
            });

            if (enableDetailedLogging)
            {
                ConfigureDetailedLogging(builder);
            }
        }

        private static void ConfigureMySql(DbContextOptionsBuilder optionsBuilder, string connectionString)
        {
            optionsBuilder.UseMySql(connectionString, 
                new MySqlServerVersion(new Version(8, 0, 11)),
                mySqlOptions => 
                {
                    mySqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(5),
                        errorNumbersToAdd: null);
                    mySqlOptions.CommandTimeout(120);
                });
        }

        private static void ConfigurePostgreSQL(DbContextOptionsBuilder optionsBuilder, string connectionString)
        {
            optionsBuilder.UseNpgsql(connectionString, 
                npgsqlOptions => 
                {
                    npgsqlOptions.EnableRetryOnFailure(3, TimeSpan.FromSeconds(5), null);
                    npgsqlOptions.CommandTimeout(120);
                });
        }

        private static void ConfigureOracle(DbContextOptionsBuilder optionsBuilder, string connectionString)
        {
            optionsBuilder.UseOracle(connectionString,
                oracleOptions => 
                {
                    oracleOptions.UseOracleSQLCompatibility(OracleSQLCompatibility.DatabaseVersion21);
                    oracleOptions.CommandTimeout(120);
                });
        }

        private static void ConfigureCommonOptions(DbContextOptionsBuilder optionsBuilder, bool enableDetailedLogging)
        {
            // 添加通用配置
            optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            
            if (enableDetailedLogging)
            {
                ConfigureDetailedLogging(optionsBuilder);
            }
        }

        private static void ConfigureDetailedLogging(DbContextOptionsBuilder builder)
        {
            builder.UseLoggerFactory(new LoggerFactory(new[] { new DebugLoggerProvider() }));
            builder.EnableSensitiveDataLogging();
            builder.EnableDetailedErrors();
            builder.ConfigureWarnings(
                b => b.Log(
                    (RelationalEventId.ConnectionOpened, LogLevel.Information),
                    (RelationalEventId.ConnectionClosed, LogLevel.Information),
                    (RelationalEventId.CommandExecuted, LogLevel.Information)));
        }
    }
}