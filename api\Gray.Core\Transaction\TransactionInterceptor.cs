using Castle.DynamicProxy;
using Microsoft.Extensions.Logging;
using System;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace Gray.Core.Transaction
{
    /// <summary>
    /// 事务拦截器 - 实现声明式事务管理
    /// </summary>
    public class TransactionInterceptor : IInterceptor
    {
        private readonly IAsyncTransactionContext _asyncTransactionContext;
        private readonly ILogger<TransactionInterceptor> _logger;

        public TransactionInterceptor(IAsyncTransactionContext asyncTransactionContext, ILogger<TransactionInterceptor> logger)
        {
            _asyncTransactionContext = asyncTransactionContext ?? throw new ArgumentNullException(nameof(asyncTransactionContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public void Intercept(IInvocation invocation)
        {
            var transactionalAttribute = GetTransactionalAttribute(invocation);
            
            if (transactionalAttribute == null || !transactionalAttribute.Enabled)
            {
                // 没有事务特性或事务被禁用，直接执行
                invocation.Proceed();
                return;
            }

            var methodInfo = invocation.Method;
            var isAsync = IsAsyncMethod(methodInfo);

            if (isAsync)
            {
                InterceptAsync(invocation, transactionalAttribute);
            }
            else
            {
                InterceptSync(invocation, transactionalAttribute);
            }
        }

        private void InterceptSync(IInvocation invocation, TransactionalAttribute attribute)
        {
            var methodName = $"{invocation.TargetType?.Name}.{invocation.Method.Name}";
            _logger.LogDebug($"开始同步事务拦截: {methodName}");

            try
            {
                if (ShouldCreateNewTransaction(attribute))
                {
                    // 注意：AsyncTransactionContext 只支持异步操作，这里使用 GetAwaiter().GetResult()
                    // 建议将同步方法改为异步方法
                    _asyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
                    {
                        invocation.Proceed();
                        return true; // 成功标志
                    }, attribute.IsolationLevel).GetAwaiter().GetResult();
                }
                else
                {
                    invocation.Proceed();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"同步事务执行失败: {methodName}");
                throw;
            }
        }

        private void InterceptAsync(IInvocation invocation, TransactionalAttribute attribute)
        {
            var methodName = $"{invocation.TargetType?.Name}.{invocation.Method.Name}";
            _logger.LogDebug($"开始异步事务拦截: {methodName}");

            invocation.ReturnValue = InterceptAsyncInternal(invocation, attribute, methodName);
        }

        private async Task InterceptAsyncInternal(IInvocation invocation, TransactionalAttribute attribute, string methodName)
        {
            try
            {
                if (ShouldCreateNewTransaction(attribute))
                {
                    await _asyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
                    {
                        invocation.Proceed();

                        if (invocation.ReturnValue is Task task)
                        {
                            await task;
                        }
                    }, attribute.IsolationLevel);
                }
                else
                {
                    invocation.Proceed();

                    if (invocation.ReturnValue is Task task)
                    {
                        await task;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"异步事务执行失败: {methodName}");
                throw;
            }
        }

        private async Task<T> InterceptAsyncWithResult<T>(IInvocation invocation, TransactionalAttribute attribute, string methodName)
        {
            try
            {
                if (ShouldCreateNewTransaction(attribute))
                {
                    return await _asyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
                    {
                        invocation.Proceed();

                        if (invocation.ReturnValue is Task<T> task)
                        {
                            return await task;
                        }

                        return (T)invocation.ReturnValue;
                    }, attribute.IsolationLevel);
                }
                else
                {
                    invocation.Proceed();

                    if (invocation.ReturnValue is Task<T> task)
                    {
                        return await task;
                    }

                    return (T)invocation.ReturnValue;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"异步事务执行失败: {methodName}");
                throw;
            }
        }

        private TransactionalAttribute GetTransactionalAttribute(IInvocation invocation)
        {
            // 首先检查方法级别的特性
            var methodAttribute = invocation.Method.GetCustomAttribute<TransactionalAttribute>();
            if (methodAttribute != null)
                return methodAttribute;

            // 然后检查类级别的特性
            var classAttribute = invocation.TargetType?.GetCustomAttribute<TransactionalAttribute>();
            return classAttribute;
        }

        private bool IsAsyncMethod(MethodInfo method)
        {
            return method.ReturnType == typeof(Task) || 
                   (method.ReturnType.IsGenericType && method.ReturnType.GetGenericTypeDefinition() == typeof(Task<>));
        }

        private bool ShouldCreateNewTransaction(TransactionalAttribute attribute)
        {
            switch (attribute.Propagation)
            {
                case TransactionPropagation.Required:
                    return !_asyncTransactionContext.HasActiveTransaction;

                case TransactionPropagation.RequiresNew:
                    return true;

                case TransactionPropagation.Mandatory:
                    if (!_asyncTransactionContext.HasActiveTransaction)
                        throw new InvalidOperationException("当前方法要求在事务中执行，但没有活动事务");
                    return false;
                    
                case TransactionPropagation.Supports:
                    return false; // 支持事务但不强制创建
                    
                case TransactionPropagation.NotSupported:
                case TransactionPropagation.Never:
                    if (_asyncTransactionContext.HasActiveTransaction && attribute.Propagation == TransactionPropagation.Never)
                        throw new InvalidOperationException("当前方法不支持在事务中执行");
                    return false;

                case TransactionPropagation.Nested:
                    return true; // 总是创建嵌套事务

                default:
                    return !_asyncTransactionContext.HasActiveTransaction;
            }
        }
    }
}
