/*
Tel:15662337743
 *所有业务编写全部应在Partial文件夹下Demo_GoodsService与IDemo_GoodsService中编写
 */
using Gray.DbTest.IRepositories;
using Gray.DbTest.IServices;
using Gray.Core.BaseProvider;
using Gray.Core.Extensions.AutofacManager;
using Gray.Entity.DomainModels;

namespace Gray.DbTest.Services
{
    public partial class Demo_GoodsService : ServiceBase<Demo_Goods, IDemo_GoodsRepository>
    , IDemo_GoodsService, IDependency
    {
    public Demo_GoodsService(IDemo_GoodsRepository repository)
    : base(repository)
    {
    Init(repository);
    }
    public static IDemo_GoodsService Instance
    {
      get { return AutofacContainerModule.GetService<IDemo_GoodsService>(); } }
    }
 }
