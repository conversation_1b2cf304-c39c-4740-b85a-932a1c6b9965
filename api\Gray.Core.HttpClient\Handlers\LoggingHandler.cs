using Gray.Core.GrayHttpClient.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Text;

namespace Gray.Core.GrayHttpClient.Handlers
{
    /// <summary>
    /// 日志处理器
    /// 负责记录HTTP请求和响应的详细信息
    /// </summary>
    public class LoggingHandler : DelegatingHandler
    {
        private readonly ILogger<LoggingHandler> _logger;
        private readonly LoggingOptions _loggingOptions;

        public LoggingHandler(
            ILogger<LoggingHandler> logger,
            IOptions<HttpClientOptions> options)
        {
            _logger = logger;
            _loggingOptions = options.Value.Logging;
        }

        /// <summary>
        /// 发送HTTP请求的处理
        /// </summary>
        protected override async Task<HttpResponseMessage> SendAsync(
            HttpRequestMessage request, 
            CancellationToken cancellationToken)
        {
            var requestId = Guid.NewGuid().ToString("N")[..8];
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // 记录请求信息
                if (_loggingOptions.LogRequests)
                {
                    await LogRequestAsync(request, requestId);
                }

                // 发送请求
                var response = await base.SendAsync(request, cancellationToken);

                stopwatch.Stop();

                // 记录响应信息
                if (_loggingOptions.LogResponses)
                {
                    await LogResponseAsync(response, requestId, stopwatch.ElapsedMilliseconds);
                }

                return response;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "HTTP请求异常 [RequestId: {RequestId}] {Method} {Uri} - 耗时: {ElapsedMs}ms",
                    requestId, request.Method, request.RequestUri, stopwatch.ElapsedMilliseconds);
                throw;
            }
        }

        /// <summary>
        /// 记录请求信息
        /// </summary>
        private async Task LogRequestAsync(HttpRequestMessage request, string requestId)
        {
            try
            {
                var logBuilder = new StringBuilder();
                logBuilder.AppendLine($"HTTP请求开始 [RequestId: {requestId}]");
                logBuilder.AppendLine($"方法: {request.Method}");
                logBuilder.AppendLine($"URI: {request.RequestUri}");

                // 记录请求头
                if (_loggingOptions.LogHeaders)
                {
                    logBuilder.AppendLine("请求头:");
                    foreach (var header in request.Headers)
                    {
                        var headerName = header.Key;
                        var headerValue = IsSensitiveHeader(headerName) && !_loggingOptions.LogSensitiveData
                            ? "***MASKED***"
                            : string.Join(", ", header.Value);
                        
                        logBuilder.AppendLine($"  {headerName}: {headerValue}");
                    }

                    if (request.Content?.Headers != null)
                    {
                        foreach (var header in request.Content.Headers)
                        {
                            var headerName = header.Key;
                            var headerValue = IsSensitiveHeader(headerName) && !_loggingOptions.LogSensitiveData
                                ? "***MASKED***"
                                : string.Join(", ", header.Value);
                            
                            logBuilder.AppendLine($"  {headerName}: {headerValue}");
                        }
                    }
                }

                // 记录请求体
                if (_loggingOptions.LogBody && request.Content != null)
                {
                    var content = await request.Content.ReadAsStringAsync();
                    if (!string.IsNullOrEmpty(content))
                    {
                        if (content.Length > _loggingOptions.MaxBodyLogLength)
                        {
                            content = content.Substring(0, _loggingOptions.MaxBodyLogLength) + "... (truncated)";
                        }
                        logBuilder.AppendLine($"请求体: {content}");
                    }
                }

                _logger.LogInformation(logBuilder.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "记录请求日志时发生异常 [RequestId: {RequestId}]", requestId);
            }
        }

        /// <summary>
        /// 记录响应信息
        /// </summary>
        private async Task LogResponseAsync(HttpResponseMessage response, string requestId, long elapsedMs)
        {
            try
            {
                var logBuilder = new StringBuilder();
                logBuilder.AppendLine($"HTTP响应完成 [RequestId: {requestId}]");
                logBuilder.AppendLine($"状态码: {(int)response.StatusCode} {response.StatusCode}");
                logBuilder.AppendLine($"耗时: {elapsedMs}ms");

                // 记录响应头
                if (_loggingOptions.LogHeaders)
                {
                    logBuilder.AppendLine("响应头:");
                    foreach (var header in response.Headers)
                    {
                        var headerName = header.Key;
                        var headerValue = IsSensitiveHeader(headerName) && !_loggingOptions.LogSensitiveData
                            ? "***MASKED***"
                            : string.Join(", ", header.Value);
                        
                        logBuilder.AppendLine($"  {headerName}: {headerValue}");
                    }

                    if (response.Content?.Headers != null)
                    {
                        foreach (var header in response.Content.Headers)
                        {
                            var headerName = header.Key;
                            var headerValue = IsSensitiveHeader(headerName) && !_loggingOptions.LogSensitiveData
                                ? "***MASKED***"
                                : string.Join(", ", header.Value);
                            
                            logBuilder.AppendLine($"  {headerName}: {headerValue}");
                        }
                    }
                }

                // 记录响应体
                if (_loggingOptions.LogBody && response.Content != null)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    if (!string.IsNullOrEmpty(content))
                    {
                        if (content.Length > _loggingOptions.MaxBodyLogLength)
                        {
                            content = content.Substring(0, _loggingOptions.MaxBodyLogLength) + "... (truncated)";
                        }
                        logBuilder.AppendLine($"响应体: {content}");
                    }
                }

                var logLevel = response.IsSuccessStatusCode ? LogLevel.Information : LogLevel.Warning;
                _logger.Log(logLevel, logBuilder.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "记录响应日志时发生异常 [RequestId: {RequestId}]", requestId);
            }
        }

        /// <summary>
        /// 检查是否为敏感请求头
        /// </summary>
        private bool IsSensitiveHeader(string headerName)
        {
            return _loggingOptions.SensitiveHeaders.Any(h => 
                string.Equals(h, headerName, StringComparison.OrdinalIgnoreCase));
        }
    }
}
