namespace Gray.Core.GrayHttpClient.Services
{
    /// <summary>
    /// HTTP客户端管理器接口
    /// </summary>
    public interface IHttpClientManager
    {
        /// <summary>
        /// 获取指定名称的HTTP客户端
        /// </summary>
        /// <param name="clientName">客户端名称</param>
        /// <returns>HTTP客户端</returns>
        System.Net.Http.HttpClient GetHttpClient(string clientName);

        /// <summary>
        /// 获取指定类型的Refit客户端
        /// </summary>
        /// <typeparam name="T">Refit客户端接口类型</typeparam>
        /// <returns>Refit客户端实例</returns>
        T GetRefitClient<T>() where T : class;

        /// <summary>
        /// 检查指定客户端是否可用
        /// </summary>
        /// <param name="clientName">客户端名称</param>
        /// <returns>是否可用</returns>
        Task<bool> IsClientAvailableAsync(string clientName);

        /// <summary>
        /// 获取客户端健康状态
        /// </summary>
        /// <param name="clientName">客户端名称</param>
        /// <returns>健康状态信息</returns>
        Task<ClientHealthStatus> GetClientHealthAsync(string clientName);

        /// <summary>
        /// 获取所有已配置的客户端名称
        /// </summary>
        /// <returns>客户端名称列表</returns>
        IEnumerable<string> GetConfiguredClientNames();
    }

    /// <summary>
    /// 客户端健康状态
    /// </summary>
    public class ClientHealthStatus
    {
        /// <summary>
        /// 客户端名称
        /// </summary>
        public string ClientName { get; set; } = string.Empty;

        /// <summary>
        /// 是否健康
        /// </summary>
        public bool IsHealthy { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 最后检查时间
        /// </summary>
        public DateTime LastCheckTime { get; set; }

        /// <summary>
        /// 响应时间（毫秒）
        /// </summary>
        public long ResponseTimeMs { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
}
