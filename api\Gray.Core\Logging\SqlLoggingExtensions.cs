using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;

namespace Gray.Core.Logging
{
    /// <summary>
    /// SQL日志记录扩展方法
    /// </summary>
    public static class SqlLoggingExtensions
    {
        /// <summary>
        /// 为DbContext添加SQL日志拦截器
        /// </summary>
        public static DbContextOptionsBuilder AddSqlLogging(
            this DbContextOptionsBuilder optionsBuilder,
            SqlLoggingOptions options = null)
        {
            if (options == null)
            {
                options = new SqlLoggingOptions();
            }

            if (!options.Enabled)
            {
                return optionsBuilder;
            }

            // 添加拦截器
            optionsBuilder.AddInterceptors(new SqlLoggingInterceptor(
                CreateLogger(), 
                options));

            // 配置EF Core内置日志
            if (options.UseStructuredLogging)
            {
                optionsBuilder.LogTo(
                    message => Console.WriteLine($"[EF Core] {message}"),
                    LogLevel.Information);
            }

            return optionsBuilder;
        }

        /// <summary>
        /// 为DbContext添加SQL日志拦截器（带服务提供者）
        /// </summary>
        public static DbContextOptionsBuilder AddSqlLogging(
            this DbContextOptionsBuilder optionsBuilder,
            IServiceProvider serviceProvider,
            SqlLoggingOptions options = null)
        {
            if (options == null)
            {
                options = new SqlLoggingOptions();
            }

            if (!options.Enabled)
            {
                return optionsBuilder;
            }

            // 从服务容器获取Logger
            var logger = serviceProvider.GetService<ILogger<SqlLoggingInterceptor>>();
            if (logger == null)
            {
                logger = CreateLogger();
            }

            // 添加拦截器
            optionsBuilder.AddInterceptors(new SqlLoggingInterceptor(logger, options));

            // 配置EF Core内置日志
            if (options.UseStructuredLogging)
            {
                optionsBuilder.LogTo(
                    message => logger.LogInformation("[EF Core] {Message}", message),
                    LogLevel.Information);
            }

            return optionsBuilder;
        }

        /// <summary>
        /// 注册SQL日志服务
        /// </summary>
        public static IServiceCollection AddSqlLogging(
            this IServiceCollection services,
            SqlLoggingOptions options = null)
        {
            if (options == null)
            {
                options = new SqlLoggingOptions();
            }

            // 注册配置选项
            services.AddSingleton(options);

            // 注册拦截器
            services.AddScoped<SqlLoggingInterceptor>();

            return services;
        }

        /// <summary>
        /// 注册SQL日志服务（使用配置委托）
        /// </summary>
        public static IServiceCollection AddSqlLogging(
            this IServiceCollection services,
            Action<SqlLoggingOptions> configureOptions)
        {
            var options = new SqlLoggingOptions();
            configureOptions?.Invoke(options);

            return services.AddSqlLogging(options);
        }

        /// <summary>
        /// 为特定环境配置SQL日志
        /// </summary>
        public static IServiceCollection AddSqlLoggingForEnvironment(
            this IServiceCollection services,
            string environment)
        {
            SqlLoggingOptions options = environment.ToLower() switch
            {
                "development" => SqlLoggingOptions.Development,
                "production" => SqlLoggingOptions.Production,
                "staging" => SqlLoggingOptions.Performance,
                "debug" => SqlLoggingOptions.Debug,
                _ => new SqlLoggingOptions()
            };

            return services.AddSqlLogging(options);
        }

        /// <summary>
        /// 创建默认Logger
        /// </summary>
        private static ILogger<SqlLoggingInterceptor> CreateLogger()
        {
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole();
            });

            return loggerFactory.CreateLogger<SqlLoggingInterceptor>();
        }

        /// <summary>
        /// 启用SQL日志的快捷方法
        /// </summary>
        public static DbContextOptionsBuilder EnableSqlLogging(
            this DbContextOptionsBuilder optionsBuilder,
            bool includeParameters = false,
            bool writeToFile = false)
        {
            var options = new SqlLoggingOptions
            {
                Enabled = true,
                IncludeParameters = includeParameters,
                WriteToFile = writeToFile,
                UseStructuredLogging = true
            };

            return optionsBuilder.AddSqlLogging(options);
        }

        /// <summary>
        /// 启用慢查询日志的快捷方法
        /// </summary>
        public static DbContextOptionsBuilder EnableSlowQueryLogging(
            this DbContextOptionsBuilder optionsBuilder,
            int thresholdMs = 1000,
            bool writeToFile = true)
        {
            var options = new SqlLoggingOptions
            {
                Enabled = true,
                OnlyLogSlowQueries = true,
                SlowQueryThresholdMs = thresholdMs,
                WriteToFile = writeToFile,
                IncludeCommandText = true,
                UseStructuredLogging = true
            };

            return optionsBuilder.AddSqlLogging(options);
        }

        /// <summary>
        /// 启用调试模式SQL日志
        /// </summary>
        public static DbContextOptionsBuilder EnableDebugSqlLogging(
            this DbContextOptionsBuilder optionsBuilder)
        {
            return optionsBuilder.AddSqlLogging(SqlLoggingOptions.Debug);
        }
    }
}
