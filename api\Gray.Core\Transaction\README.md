# 事务管理架构重构

## 概述

本次重构将事务管理从分散在多个层次的实现重新设计为集中式、职责明确的架构，遵循单一职责原则和关注点分离原则。

## 架构设计

### 新架构组件

1. **AsyncTransactionContext** - 专门负责异步事务管理
2. **UnitOfWork** - 简化为数据访问协调器
3. **Repository** - 纯数据访问层
4. **Service** - 业务逻辑层 + 声明式事务需求

### 职责分离

- **AsyncTransactionContext**: 统一的异步事务管理，支持多DbContext、嵌套事务、完全解耦设计
- **UnitOfWork**: DbContext管理、Repository创建、数据保存
- **Repository**: CRUD操作、查询、实体管理
- **Service**: 业务流程、通过AsyncTransactionContext声明事务需求

## 核心接口

### IAsyncTransactionContext

```csharp
public interface IAsyncTransactionContext
{
    // 异步事务管理（默认DbContext）
    Task<T> ExecuteInTransactionAsync<T>(
        Func<IUnitOfWork, Task<T>> action,
        IsolationLevel isolationLevel = IsolationLevel.ReadCommitted);

    // 异步事务管理（指定DbContext类型）
    Task<T> ExecuteInTransactionAsync<TDbContext, T>(
        Func<IUnitOfWork, Task<T>> action,
        IsolationLevel isolationLevel = IsolationLevel.ReadCommitted)
        where TDbContext : BaseDbContext;
    
    // 声明式事务执行
    T ExecuteInTransaction<T>(Func<T> operation, IsolationLevel? isolationLevel = null);
    Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation, IsolationLevel? isolationLevel = null, CancellationToken cancellationToken = default);
    
    // 事务范围管理
    ITransactionScope CreateTransactionScope(bool autoRollback = true);
    ITransactionScope CreateTransactionScopeWithNewConnection(bool autoRollback = true);
}
```

## 使用方式

### 1. 声明式事务（推荐）

#### 使用特性

```csharp
[Transactional]
public WebResponseContent CreateUser(User user)
{
    // 业务逻辑
    return new WebResponseContent(true);
}

[Transactional(IsolationLevel = IsolationLevel.Serializable)]
public async Task<WebResponseContent> UpdateUserAsync(User user)
{
    // 异步业务逻辑
    return new WebResponseContent(true);
}
```

#### 使用AsyncTransactionContext

```csharp
public class UserService : ServiceBase<User, IRepository<User>>
{
    public async Task<WebResponseContent> CreateUserAsync(User user)
    {
        return await AsyncTransactionContext.ExecuteInTransactionAsync(async (unitOfWork) =>
        {
            // 异步业务逻辑
            user.SetCreateDefaultVal();
            var userRepo = unitOfWork.GetRepository<User>();
            await userRepo.AddAsync(user);
            await unitOfWork.SaveChangesAsync();
            return new WebResponseContent(true);
        });
    }

    public async Task<WebResponseContent> CreateUserWithSpecificContextAsync(User user)
    {
        return await AsyncTransactionContext.ExecuteInTransactionAsync<SysDbContext>(async (unitOfWork) =>
        {
            // 使用指定的SysDbContext
            user.SetCreateDefaultVal();
            var userRepo = unitOfWork.GetRepository<User>();
            await userRepo.AddAsync(user);
            await unitOfWork.SaveChangesAsync();
            return new WebResponseContent(true);
        });
    }
}
```

### 2. 声明式事务

```csharp
public class UserService : ServiceBase<User, IRepository<User>>
{
    [Transactional(IsolationLevel = IsolationLevel.ReadCommitted)]
    public async Task<WebResponseContent> CreateUserAsync(User user)
    {
        // 业务逻辑，事务由拦截器自动管理（使用AsyncTransactionContext）
        user.SetCreateDefaultVal();
        await repository.AddAsync(user);
        await repository.SaveChangesAsync();
        return new WebResponseContent(true);
    }

    [Transactional(IsolationLevel = IsolationLevel.Serializable)]
    public async Task<WebResponseContent> TransferMoneyAsync(int fromUserId, int toUserId, decimal amount)
    {
        // 高隔离级别的事务操作
        var fromUser = await repository.GetByIdAsync(fromUserId);
        var toUser = await repository.GetByIdAsync(toUserId);

        fromUser.Balance -= amount;
        toUser.Balance += amount;

        await repository.UpdateAsync(fromUser);
        await repository.UpdateAsync(toUser);
        await repository.SaveChangesAsync();

        return new WebResponseContent(true);
    }
}
```

## 事务传播行为

支持多种事务传播行为：

- **Required**: 必须在事务中运行，如果当前没有事务则创建新事务
- **Mandatory**: 必须在事务中运行，如果当前没有事务则抛出异常
- **RequiresNew**: 总是创建新事务，如果当前有事务则挂起
- **Supports**: 支持事务，如果当前有事务则加入，没有则以非事务方式运行
- **NotSupported**: 不支持事务，如果当前有事务则挂起
- **Never**: 不支持事务，如果当前有事务则抛出异常
- **Nested**: 嵌套事务，在当前事务内创建保存点

## 依赖注入配置

```csharp
services.AddScoped<IUnitOfWork, UnitOfWork>();
services.AddScoped<IAsyncTransactionContext, AsyncTransactionContext>();

// 注册拦截器（如果使用AOP）
services.AddScoped<TransactionInterceptor>();
```

## 迁移指南

### 从旧架构迁移

1. **Service层**：
   - 移除直接的事务管理代码
   - 使用AsyncTransactionContext.ExecuteInTransactionAsync()替代
   - 或使用[Transactional]特性

2. **Repository层**：
   - 移除DbContextBeginTransaction方法
   - 专注于纯数据访问操作

3. **UnitOfWork**：
   - 保持现有接口，内部实现已简化
   - 事务管理方法仅供AsyncTransactionContext使用

### 兼容性

- 现有的UnitOfWork接口保持兼容
- Service和Repository的基本CRUD操作不受影响
- 只需要修改事务管理相关的代码

## 测试

提供了完整的测试套件：

- **AsyncTransactionContextTests**: 单元测试
- **TransactionInterceptorTests**: 拦截器测试
- **TransactionIntegrationTests**: 集成测试

运行测试：
```bash
dotnet test Gray.Core.Transaction.Tests
```

## 性能优化

1. **减少事务开销**: 通过声明式管理避免不必要的事务创建
2. **连接池优化**: 更好的数据库连接管理
3. **嵌套事务**: 支持高效的嵌套事务处理
4. **异步支持**: 完整的异步事务支持

## 最佳实践

1. **优先使用异步事务**: 使用AsyncTransactionContext.ExecuteInTransactionAsync()或[Transactional]特性
2. **合理设置隔离级别**: 根据业务需求选择合适的隔离级别
3. **避免长事务**: 保持事务尽可能短小
4. **异常处理**: 确保异常时事务能正确回滚
5. **测试覆盖**: 为事务相关的业务逻辑编写测试
6. **指定DbContext类型**: 在需要时使用泛型方法指定特定的DbContext类型

## 故障排除

### 常见问题

1. **事务未提交**: 检查是否正确调用Commit()或Complete()
2. **嵌套事务问题**: 确保正确理解事务传播行为
3. **死锁**: 检查事务隔离级别和锁定顺序
4. **性能问题**: 检查事务范围是否过大

### 日志和监控

TransactionService提供详细的日志记录，包括：
- 事务开始/提交/回滚
- 事务级别和计数
- 异常信息
- 性能指标
