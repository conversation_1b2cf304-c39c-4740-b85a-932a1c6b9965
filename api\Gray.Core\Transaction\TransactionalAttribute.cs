using System;
using System.Data;

namespace Gray.Core.Transaction
{
    /// <summary>
    /// 事务特性 - 用于声明式事务管理
    /// </summary>
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = false, Inherited = true)]
    public class TransactionalAttribute : Attribute
    {
        /// <summary>
        /// 事务隔离级别
        /// </summary>
        public IsolationLevel IsolationLevel { get; set; } = IsolationLevel.ReadCommitted;
        
        /// <summary>
        /// 是否启用事务
        /// </summary>
        public bool Enabled { get; set; } = true;
        
        /// <summary>
        /// 事务超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;
        
        /// <summary>
        /// 是否只读事务
        /// </summary>
        public bool ReadOnly { get; set; } = false;
        
        /// <summary>
        /// 事务传播行为
        /// </summary>
        public TransactionPropagation Propagation { get; set; } = TransactionPropagation.Required;
        
        /// <summary>
        /// 事务名称（用于日志和调试）
        /// </summary>
        public string Name { get; set; }

        public TransactionalAttribute()
        {
        }

        public TransactionalAttribute(IsolationLevel isolationLevel)
        {
            IsolationLevel = isolationLevel;
        }

        public TransactionalAttribute(TransactionPropagation propagation)
        {
            Propagation = propagation;
        }

        public TransactionalAttribute(IsolationLevel isolationLevel, TransactionPropagation propagation)
        {
            IsolationLevel = isolationLevel;
            Propagation = propagation;
        }
    }

    /// <summary>
    /// 事务传播行为
    /// </summary>
    public enum TransactionPropagation
    {
        /// <summary>
        /// 必须在事务中运行，如果当前没有事务则创建新事务
        /// </summary>
        Required,
        
        /// <summary>
        /// 必须在事务中运行，如果当前没有事务则抛出异常
        /// </summary>
        Mandatory,
        
        /// <summary>
        /// 总是创建新事务，如果当前有事务则挂起
        /// </summary>
        RequiresNew,
        
        /// <summary>
        /// 支持事务，如果当前有事务则加入，没有则以非事务方式运行
        /// </summary>
        Supports,
        
        /// <summary>
        /// 不支持事务，如果当前有事务则挂起
        /// </summary>
        NotSupported,
        
        /// <summary>
        /// 不支持事务，如果当前有事务则抛出异常
        /// </summary>
        Never,
        
        /// <summary>
        /// 嵌套事务，在当前事务内创建保存点
        /// </summary>
        Nested
    }
}
