/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "MES比对记录",TableName = "DY_MESResult",DBServer = "JL_DYDbContext")]
    public partial class DY_MESResult:JL_DYEntity
    {
        /// <summary>
       ///机台名
       /// </summary>
       [Display(Name ="机台名")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string DBMachine { get; set; }

       /// <summary>
       ///机台id
       /// </summary>
       [Display(Name ="机台id")]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid DBMachineId { get; set; }

       /// <summary>
       ///胎号id
       /// </summary>
       [Display(Name ="胎号id")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string TyreId { get; set; }

       /// <summary>
       ///胎号代码
       /// </summary>
       [Display(Name ="胎号代码")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string TyreCode { get; set; }

       /// <summary>
       ///MES结果
       /// </summary>
       [Display(Name ="MES结果")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int MESResult { get; set; }

       /// <summary>
       ///MES配方
       /// </summary>
       [Display(Name ="MES配方")]
       [MaxLength(10)]
       [Column(TypeName="nvarchar(10)")]
       [Editable(true)]
       public string DYFormula { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Key]
       [Display(Name ="MESResultId")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int MESResultId { get; set; }

       /// <summary>
       ///轮毂大小
       /// </summary>
       [Display(Name ="轮毂大小")]
       [MaxLength(222)]
       [Column(TypeName="nvarchar(222)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string RimSize { get; set; }

       /// <summary>
       ///轮胎规格
       /// </summary>
       [Display(Name ="轮胎规格")]
       [MaxLength(1)]
       [Column(TypeName="nvarchar(1)")]
       [Editable(true)]
       public string TyreSPEC { get; set; }

       
    }
}