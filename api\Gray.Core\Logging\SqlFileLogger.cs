using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Gray.Core.Extensions;

namespace Gray.Core.Logging
{
    /// <summary>
    /// SQL文件日志记录器
    /// </summary>
    public static class SqlFileLogger
    {
        private static readonly object _lockObject = new object();
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        /// <summary>
        /// 同步写入日志
        /// </summary>
        public static void WriteLog(object logData, string logPath = "Log/Sql/")
        {
            try
            {
                lock (_lockObject)
                {
                    WriteLogInternal(logData, logPath);
                }
            }
            catch (Exception ex)
            {
                // 避免日志记录失败影响主业务
                Console.WriteLine($"SQL文件日志写入失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 异步写入日志
        /// </summary>
        public static async Task WriteLogAsync(object logData, string logPath = "Log/Sql/")
        {
            try
            {
                await _semaphore.WaitAsync();
                try
                {
                    await WriteLogInternalAsync(logData, logPath);
                }
                finally
                {
                    _semaphore.Release();
                }
            }
            catch (Exception ex)
            {
                // 避免日志记录失败影响主业务
                Console.WriteLine($"SQL文件日志异步写入失败: {ex.Message}");
            }
        }

        private static void WriteLogInternal(object logData, string logPath)
        {
            // 确保日志目录存在
            var fullLogPath = logPath.MapPath();
            if (!Directory.Exists(fullLogPath))
            {
                Directory.CreateDirectory(fullLogPath);
            }

            // 生成日志文件名（按日期分文件）
            var fileName = $"sql_{DateTime.Now:yyyyMMdd}.log";
            var filePath = Path.Combine(fullLogPath, fileName);

            // 格式化日志内容
            var logContent = FormatLogContent(logData);

            // 写入文件
            File.AppendAllText(filePath, logContent + Environment.NewLine);
        }

        private static async Task WriteLogInternalAsync(object logData, string logPath)
        {
            // 确保日志目录存在
            var fullLogPath = logPath.MapPath();
            if (!Directory.Exists(fullLogPath))
            {
                Directory.CreateDirectory(fullLogPath);
            }

            // 生成日志文件名（按日期分文件）
            var fileName = $"sql_{DateTime.Now:yyyyMMdd}.log";
            var filePath = Path.Combine(fullLogPath, fileName);

            // 格式化日志内容
            var logContent = FormatLogContent(logData);

            // 异步写入文件
            await File.AppendAllTextAsync(filePath, logContent + Environment.NewLine);
        }

        private static string FormatLogContent(object logData)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var jsonData = logData.Serialize();
            
            return $"[{timestamp}] {jsonData}";
        }

        /// <summary>
        /// 清理过期日志文件
        /// </summary>
        public static void CleanupOldLogs(string logPath = "Log/Sql/", int keepDays = 30)
        {
            try
            {
                var fullLogPath = logPath.MapPath();
                if (!Directory.Exists(fullLogPath)) return;

                var cutoffDate = DateTime.Now.AddDays(-keepDays);
                var files = Directory.GetFiles(fullLogPath, "sql_*.log");

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                        Console.WriteLine($"已删除过期SQL日志文件: {fileInfo.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理SQL日志文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取日志文件大小
        /// </summary>
        public static long GetLogFileSize(string logPath = "Log/Sql/")
        {
            try
            {
                var fullLogPath = logPath.MapPath();
                if (!Directory.Exists(fullLogPath)) return 0;

                var files = Directory.GetFiles(fullLogPath, "sql_*.log");
                long totalSize = 0;

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    totalSize += fileInfo.Length;
                }

                return totalSize;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 检查是否需要轮转日志文件
        /// </summary>
        public static bool ShouldRotateLog(string logPath = "Log/Sql/", long maxSizeBytes = 100 * 1024 * 1024) // 100MB
        {
            return GetLogFileSize(logPath) > maxSizeBytes;
        }

        /// <summary>
        /// 轮转日志文件
        /// </summary>
        public static void RotateLogFiles(string logPath = "Log/Sql/", int maxFiles = 10)
        {
            try
            {
                var fullLogPath = logPath.MapPath();
                if (!Directory.Exists(fullLogPath)) return;

                var files = Directory.GetFiles(fullLogPath, "sql_*.log");
                Array.Sort(files); // 按文件名排序

                // 如果文件数量超过限制，删除最老的文件
                if (files.Length > maxFiles)
                {
                    for (int i = 0; i < files.Length - maxFiles; i++)
                    {
                        File.Delete(files[i]);
                        Console.WriteLine($"已删除旧SQL日志文件: {Path.GetFileName(files[i])}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"轮转SQL日志文件失败: {ex.Message}");
            }
        }
    }
}
