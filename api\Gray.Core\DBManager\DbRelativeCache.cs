﻿using Gray.Entity.SystemModels;
using Microsoft.Extensions.DependencyModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;

namespace Gray.Core.DBManager
{
    // public static class DbRelativeCache
    // {
    //     private static readonly Dictionary<string, Type> DbEntityTypes = new Dictionary<string, Type>();

    //     static DbRelativeCache()
    //     {
    //         InitDbEntityType();
    //     }

    //     /// <summary>
    //     /// 缓存分库model基类
    //     /// </summary>
    //     private static void InitDbEntityType()
    //     {
    //         var compilationLibrary = DependencyContext
    //              .Default
    //              .CompileLibraries
    //              .Where(x => x.Name.EndsWith(".Entity") && !x.Serviceable && x.Type != "package" && x.Type == "project");

    //         foreach (var _compilation in compilationLibrary)
    //         {
    //             foreach (var item in AssemblyLoadContext.Default
    //                 .LoadFromAssemblyName(new AssemblyName(_compilation.Name))
    //                 .GetTypes()
    //                 .Where(x => x.GetTypeInfo().BaseType != null && x.BaseType == (typeof(BaseEntity))))
    //             {
    //                 DbEntityTypes[item.Name] = item;
    //             }
    //         }
    //     }

    //     /// <summary>
    //     /// 获取数据库的链接类型。如数据库是mysql还是pgsql类型
    //     /// </summary>
    //     public static string GetDbType(string dbService)
    //     {
    //         if (string.IsNullOrEmpty(dbService))
    //         {
    //             return null;
    //         }

    //         var config = DBServerProvider.GetConnConfig(dbService);
    //         return config?.DbType;
    //     }

    //     /// <summary>
    //     /// 根据分库名称获取分库model基类
    //     /// </summary>
    //     public static Type GetDbEntityType(string dbService)
    //     {
    //         string name = dbService?.Replace("DbContext", "");
    //         return DbEntityTypes.TryGetValue($"{name}Entity", out var entityType)
    //             ? entityType
    //             : null;
    //     }

    //     /// <summary>
    //     /// 根据dbtype获取数据库链接
    //     /// </summary>
    //     public static string GetDbConnectionString(string dbContextName)
    //     {
    //         return DBServerProvider.GetConnectionString(dbContextName);
    //     }
    // }
}