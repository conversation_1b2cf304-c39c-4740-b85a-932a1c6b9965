﻿using Serilog;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;

namespace Gray.Core.Utilities
{
    /// <summary>
    /// 动态对象扩展方法类
    /// </summary>
    public static class DynamicExtensions
    {
        private static readonly ILogger _logger = Log.ForContext(typeof(DynamicExtensions));

        /// <summary>
        /// 安全获取动态对象中的属性值
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="dynamicObj">动态对象</param>
        /// <param name="propertyName">属性名称</param>
        /// <param name="defaultValue">默认值，当获取失败时返回</param>
        /// <returns>属性值或默认值</returns>
        public static T GetSafeValue<T>(this object dynamicObj, string propertyName, T defaultValue = default)
        {
            try
            {
                if (dynamicObj == null)
                {
                    _logger.Debug("尝试从空对象获取属性 {PropertyName}", propertyName);
                    return defaultValue;
                }

                // 特殊处理 ExpandoObject
                if (dynamicObj is ExpandoObject expandoObj)
                {
                    var expandoDict = expandoObj as IDictionary<string, object>;
                    if (expandoDict.TryGetValue(propertyName, out object expandoValue))
                    {
                        if (expandoValue == null)
                        {
                            _logger.Debug("ExpandoObject中的属性 {PropertyName} 值为空", propertyName);
                            return defaultValue;
                        }

                        // 尝试转换为目标类型
                        try
                        {
                            if (typeof(T) == expandoValue.GetType())
                                return (T)expandoValue;

                            // 处理数值类型转换
                            if (typeof(T) == typeof(int) || typeof(T) == typeof(double) ||
                                typeof(T) == typeof(float) || typeof(T) == typeof(decimal))
                            {
                                return (T)Convert.ChangeType(expandoValue, typeof(T));
                            }

                            // 尝试直接转换
                            return (T)expandoValue;
                        }
                        catch (Exception ex)
                        {
                            _logger.Warning(ex, "无法将ExpandoObject属性 {PropertyName} 的值 {Value} 转换为类型 {TargetType}",
                                propertyName, expandoValue, typeof(T).Name);
                            return defaultValue;
                        }
                    }
                    _logger.Debug("ExpandoObject中不存在属性 {PropertyName}", propertyName);
                    return defaultValue;
                }

                // 检查是否为IDictionary<string, object>类型
                if (dynamicObj is IDictionary<string, object> dict)
                {
                    if (dict.TryGetValue(propertyName, out object value))
                    {
                        if (value == null)
                        {
                            _logger.Debug("字典中的属性 {PropertyName} 值为空", propertyName);
                            return defaultValue;
                        }

                        // 尝试转换为目标类型
                        try
                        {
                            if (typeof(T) == value.GetType())
                                return (T)value;

                            // 处理数值类型转换
                            if (typeof(T) == typeof(int) || typeof(T) == typeof(double) ||
                                typeof(T) == typeof(float) || typeof(T) == typeof(decimal))
                            {
                                return (T)Convert.ChangeType(value, typeof(T));
                            }

                            // 尝试直接转换
                            return (T)value;
                        }
                        catch (Exception ex)
                        {
                            _logger.Warning(ex, "无法将属性 {PropertyName} 的值 {Value} 转换为类型 {TargetType}",
                                propertyName, value, typeof(T).Name);
                            return defaultValue;
                        }
                    }
                    _logger.Debug("字典中不存在属性 {PropertyName}", propertyName);
                    return defaultValue;
                }

                // 使用反射获取属性值
                var property = dynamicObj.GetType().GetProperty(propertyName);
                if (property != null)
                {
                    try
                    {
                        var value = property.GetValue(dynamicObj);
                        if (value == null)
                        {
                            _logger.Debug("通过反射获取的属性 {PropertyName} 值为空", propertyName);
                            return defaultValue;
                        }

                        // 尝试转换为目标类型
                        try
                        {
                            if (typeof(T) == value.GetType())
                                return (T)value;

                            // 处理数值类型转换
                            if (typeof(T) == typeof(int) || typeof(T) == typeof(double) ||
                                typeof(T) == typeof(float) || typeof(T) == typeof(decimal))
                            {
                                return (T)Convert.ChangeType(value, typeof(T));
                            }

                            // 尝试直接转换
                            return (T)value;
                        }
                        catch (Exception ex)
                        {
                            _logger.Warning(ex, "无法将通过反射获取的属性 {PropertyName} 的值 {Value} 转换为类型 {TargetType}",
                                propertyName, value, typeof(T).Name);
                            return defaultValue;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "通过反射获取属性 {PropertyName} 值时出错", propertyName);
                        return defaultValue;
                    }
                }

                // 尝试动态访问属性
                try
                {
                    // 使用动态调用
                    dynamic dynamicValue = dynamicObj;
                    var value = dynamicValue[propertyName];
                    if (value == null)
                    {
                        _logger.Debug("通过动态访问获取的属性 {PropertyName} 值为空", propertyName);
                        return defaultValue;
                    }

                    // 尝试转换为目标类型
                    try
                    {
                        if (typeof(T) == value.GetType())
                            return (T)value;

                        // 处理数值类型转换
                        if (typeof(T) == typeof(int) || typeof(T) == typeof(double) ||
                            typeof(T) == typeof(float) || typeof(T) == typeof(decimal))
                        {
                            return (T)Convert.ChangeType(value, typeof(T));
                        }

                        // 尝试直接转换
                        return (T)value;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "无法将通过动态访问获取的属性 {PropertyName} 的值 {Value} 转换为类型 {TargetType}",
                            propertyName, value, typeof(T).Name);
                        return defaultValue;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Debug(ex, "通过动态访问获取属性 {PropertyName} 失败，对象类型: {ObjectType}",
                        propertyName, dynamicObj.GetType().FullName);
                    return defaultValue;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "获取属性 {PropertyName} 时发生未处理的异常", propertyName);
                return defaultValue;
            }
        }

        /// <summary>
        /// 安全获取动态对象中的字符串属性值
        /// </summary>
        /// <param name="dynamicObj">动态对象</param>
        /// <param name="propertyName">属性名称</param>
        /// <param name="defaultValue">默认值，当获取失败时返回</param>
        /// <returns>字符串属性值或默认值</returns>
        public static string GetSafeString(this object dynamicObj, string propertyName, string defaultValue = "")
        {
            return GetSafeValue<string>(dynamicObj, propertyName, defaultValue);
        }

        /// <summary>
        /// 安全获取动态对象中的整数属性值
        /// </summary>
        /// <param name="dynamicObj">动态对象</param>
        /// <param name="propertyName">属性名称</param>
        /// <param name="defaultValue">默认值，当获取失败时返回</param>
        /// <returns>整数属性值或默认值</returns>
        public static int GetSafeInt(this object dynamicObj, string propertyName, int defaultValue = 0)
        {
            return GetSafeValue<int>(dynamicObj, propertyName, defaultValue);
        }

        /// <summary>
        /// 安全获取动态对象中的双精度浮点数属性值
        /// </summary>
        /// <param name="dynamicObj">动态对象</param>
        /// <param name="propertyName">属性名称</param>
        /// <param name="defaultValue">默认值，当获取失败时返回</param>
        /// <returns>双精度浮点数属性值或默认值</returns>
        public static double GetSafeDouble(this object dynamicObj, string propertyName, double defaultValue = 0)
        {
            return GetSafeValue<double>(dynamicObj, propertyName, defaultValue);
        }

        /// <summary>
        /// 安全获取动态对象中的日期时间属性值
        /// </summary>
        /// <param name="dynamicObj">动态对象</param>
        /// <param name="propertyName">属性名称</param>
        /// <param name="defaultValue">默认值，当获取失败时返回</param>
        /// <returns>日期时间属性值或默认值</returns>
        public static DateTime GetSafeDateTime(this object dynamicObj, string propertyName, DateTime? defaultValue = null)
        {
            return GetSafeValue<DateTime>(dynamicObj, propertyName, defaultValue ?? DateTime.MinValue);
        }

        /// <summary>
        /// 安全获取动态对象中的布尔属性值
        /// </summary>
        /// <param name="dynamicObj">动态对象</param>
        /// <param name="propertyName">属性名称</param>
        /// <param name="defaultValue">默认值，当获取失败时返回</param>
        /// <returns>布尔属性值或默认值</returns>
        public static bool GetSafeBool(this object dynamicObj, string propertyName, bool defaultValue = false)
        {
            return GetSafeValue<bool>(dynamicObj, propertyName, defaultValue);
        }

        /// <summary>
        /// 安全地从 List<dynamic> 中获取指定属性的值
        /// </summary>
        /// <typeparam name="T">返回值的类型</typeparam>
        /// <param name="list">动态对象列表</param>
        /// <param name="propertyName">要获取的属性名</param>
        /// <param name="defaultValue">默认值（可选）</param>
        /// <returns>属性值的列表</returns>
        public static List<T> SafeSelect<T>(this IEnumerable<object> list, string propertyName, T defaultValue = default)
        {
            return list.Select(item => GetSafeValue<T>(item, propertyName, defaultValue)).ToList();
        }

        /// <summary>
        /// 检查动态对象是否包含指定属性
        /// </summary>
        /// <param name="dynamicObj">动态对象</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>是否包含该属性</returns>
        public static bool HasProperty(this object dynamicObj, string propertyName)
        {
            try
            {
                if (dynamicObj == null)
                {
                    _logger.Debug("尝试检查空对象是否包含属性 {PropertyName}", propertyName);
                    return false;
                }

                // 特殊处理 ExpandoObject
                if (dynamicObj is ExpandoObject expandoObj)
                {
                    var expandoDict = expandoObj as IDictionary<string, object>;
                    bool result = expandoDict.ContainsKey(propertyName);
                    if (!result)
                    {
                        _logger.Debug("ExpandoObject中不存在属性 {PropertyName}", propertyName);
                    }
                    return result;
                }

                // 检查是否为IDictionary<string, object>类型
                if (dynamicObj is IDictionary<string, object> dict)
                {
                    bool result = dict.ContainsKey(propertyName);
                    if (!result)
                    {
                        _logger.Debug("字典中不存在属性 {PropertyName}", propertyName);
                    }
                    return result;
                }

                // 使用反射检查属性
                var property = dynamicObj.GetType().GetProperty(propertyName);
                if (property != null)
                    return true;

                // 尝试动态访问属性
                try
                {
                    dynamic dynamicValue = dynamicObj;
                    var value = dynamicValue[propertyName];
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.Debug(ex, "通过动态访问检查属性 {PropertyName} 失败，对象类型: {ObjectType}",
                        propertyName, dynamicObj.GetType().FullName);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "检查属性 {PropertyName} 是否存在时发生未处理的异常", propertyName);
                return false;
            }
        }

        /// <summary>
        /// 尝试获取动态对象中的属性值，并返回是否成功
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="dynamicObj">动态对象</param>
        /// <param name="propertyName">属性名称</param>
        /// <param name="value">输出参数，成功时包含属性值，失败时为默认值</param>
        /// <returns>是否成功获取属性值</returns>
        public static bool TryGetValue<T>(this object dynamicObj, string propertyName, out T value)
        {
            try
            {
                if (dynamicObj == null)
                {
                    _logger.Debug("尝试从空对象获取属性 {PropertyName}", propertyName);
                    value = default;
                    return false;
                }

                // 特殊处理 ExpandoObject
                if (dynamicObj is ExpandoObject expandoObj)
                {
                    var expandoDict = expandoObj as IDictionary<string, object>;
                    if (expandoDict.TryGetValue(propertyName, out object expandoValue))
                    {
                        if (expandoValue == null)
                        {
                            value = default;
                            return false;
                        }

                        try
                        {
                            if (typeof(T) == expandoValue.GetType())
                            {
                                value = (T)expandoValue;
                                return true;
                            }

                            // 处理数值类型转换
                            if (typeof(T) == typeof(int) || typeof(T) == typeof(double) ||
                                typeof(T) == typeof(float) || typeof(T) == typeof(decimal))
                            {
                                value = (T)Convert.ChangeType(expandoValue, typeof(T));
                                return true;
                            }

                            // 尝试直接转换
                            value = (T)expandoValue;
                            return true;
                        }
                        catch (Exception ex)
                        {
                            _logger.Warning(ex, "无法将ExpandoObject属性 {PropertyName} 的值 {Value} 转换为类型 {TargetType}",
                                propertyName, expandoValue, typeof(T).Name);
                            value = default;
                            return false;
                        }
                    }

                    _logger.Debug("ExpandoObject中不存在属性 {PropertyName}", propertyName);
                    value = default;
                    return false;
                }

                // 检查是否为IDictionary<string, object>类型
                if (dynamicObj is IDictionary<string, object> dict)
                {
                    if (dict.TryGetValue(propertyName, out object dictValue))
                    {
                        if (dictValue == null)
                        {
                            value = default;
                            return false;
                        }

                        try
                        {
                            if (typeof(T) == dictValue.GetType())
                            {
                                value = (T)dictValue;
                                return true;
                            }

                            // 处理数值类型转换
                            if (typeof(T) == typeof(int) || typeof(T) == typeof(double) ||
                                typeof(T) == typeof(float) || typeof(T) == typeof(decimal))
                            {
                                value = (T)Convert.ChangeType(dictValue, typeof(T));
                                return true;
                            }

                            // 尝试直接转换
                            value = (T)dictValue;
                            return true;
                        }
                        catch (Exception ex)
                        {
                            _logger.Warning(ex, "无法将属性 {PropertyName} 的值 {Value} 转换为类型 {TargetType}",
                                propertyName, dictValue, typeof(T).Name);
                            value = default;
                            return false;
                        }
                    }

                    _logger.Debug("字典中不存在属性 {PropertyName}", propertyName);
                    value = default;
                    return false;
                }

                // 使用反射获取属性值
                var property = dynamicObj.GetType().GetProperty(propertyName);
                if (property != null)
                {
                    try
                    {
                        var propValue = property.GetValue(dynamicObj);
                        if (propValue == null)
                        {
                            value = default;
                            return false;
                        }

                        try
                        {
                            if (typeof(T) == propValue.GetType())
                            {
                                value = (T)propValue;
                                return true;
                            }

                            // 处理数值类型转换
                            if (typeof(T) == typeof(int) || typeof(T) == typeof(double) ||
                                typeof(T) == typeof(float) || typeof(T) == typeof(decimal))
                            {
                                value = (T)Convert.ChangeType(propValue, typeof(T));
                                return true;
                            }

                            // 尝试直接转换
                            value = (T)propValue;
                            return true;
                        }
                        catch (Exception ex)
                        {
                            _logger.Warning(ex, "无法将通过反射获取的属性 {PropertyName} 的值 {Value} 转换为类型 {TargetType}",
                                propertyName, propValue, typeof(T).Name);
                            value = default;
                            return false;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "通过反射获取属性 {PropertyName} 值时出错", propertyName);
                        value = default;
                        return false;
                    }
                }

                // 尝试动态访问属性
                try
                {
                    dynamic dynamicValue = dynamicObj;
                    var dynValue = dynamicValue[propertyName];
                    if (dynValue == null)
                    {
                        value = default;
                        return false;
                    }

                    try
                    {
                        if (typeof(T) == dynValue.GetType())
                        {
                            value = (T)dynValue;
                            return true;
                        }

                        // 处理数值类型转换
                        if (typeof(T) == typeof(int) || typeof(T) == typeof(double) ||
                            typeof(T) == typeof(float) || typeof(T) == typeof(decimal))
                        {
                            value = (T)Convert.ChangeType(dynValue, typeof(T));
                            return true;
                        }

                        // 尝试直接转换
                        value = (T)dynValue;
                        return true;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "无法将通过动态访问获取的属性 {PropertyName} 的值 {Value} 转换为类型 {TargetType}",
                            propertyName, dynValue, typeof(T).Name);
                        value = default;
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Debug(ex, "通过动态访问获取属性 {PropertyName} 失败，对象类型: {ObjectType}",
                        propertyName, dynamicObj.GetType().FullName);
                    value = default;
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "尝试获取属性 {PropertyName} 时发生未处理的异常", propertyName);
                value = default;
                return false;
            }
        }
    }
}