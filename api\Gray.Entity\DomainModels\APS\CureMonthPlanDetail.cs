/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "硫化日计划预排产",TableName = "硫化日计划预排产",DBServer = "JL_APSDbContext")]
[Table("硫化日计划预排产")]
    public partial class CureMonthPlanDetail:JL_APSEntity
    {
        /// <summary>
       ///成品代码
       /// </summary>
       [Display(Name ="成品代码")]
       [MaxLength(9)]
       [Column(TypeName="char(9)")]
       [Editable(true)]
       public string 成品代码 { get; set; }

       /// <summary>
       ///产品名称
       /// </summary>
       [Display(Name ="产品名称")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string 产品名称 { get; set; }

       /// <summary>
       ///排产月份
       /// </summary>
       [Display(Name ="排产月份")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? 排产月份 { get; set; }

       /// <summary>
       ///排产月份
       /// </summary>
       [Display(Name ="排产月份")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? 排产日 { get; set; }

       /// <summary>
       ///排产数量
       /// </summary>
       [Display(Name ="排产数量")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? 排产数量 { get; set; }

       /// <summary>
       ///备注
       /// </summary>
       [Display(Name ="备注")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string 备注 { get; set; }

       /// <summary>
       ///排产年份
       /// </summary>
       [Display(Name ="排产年份")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? 排产年份 { get; set; }

       /// <summary>
       ///排产日期
       /// </summary>
       [Display(Name ="排产日期")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? 排产日期 { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Key]
       [Display(Name ="Id")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int Id { get; set; }

       
    }
}