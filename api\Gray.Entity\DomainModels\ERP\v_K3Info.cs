/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Gray.Entity.SystemModels;

namespace Gray.Entity.DomainModels
{
    [Entity(TableCnName = "外胎信息表",TableName = "v_K3Info",DBServer = "K3DataTransferDbContext")]
    public partial class v_K3Info:K3DataTransferEntity
    {
        /// <summary>
       ///物料代码
       /// </summary>
       [Key]
       [Display(Name ="物料代码")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string 物料代码 { get; set; }

       /// <summary>
       ///断面宽度
       /// </summary>
       [Display(Name ="断面宽度")]
       [Column(TypeName="numeric")]
       [Editable(true)]
       public decimal? 断面宽度 { get; set; }

       /// <summary>
       ///物料名称
       /// </summary>
       [Display(Name ="物料名称")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string 物料名称 { get; set; }

       /// <summary>
       ///轮辋尺寸
       /// </summary>
       [Display(Name ="轮辋尺寸")]
       [Column(TypeName="varchar(max)")]
       [Editable(true)]
       public string 轮辋尺寸 { get; set; }

       /// <summary>
       ///k3规格型号
       /// </summary>
       [Display(Name ="k3规格型号")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string k3规格型号 { get; set; }

       /// <summary>
       ///轮胎规格
       /// </summary>
       [Display(Name ="轮胎规格")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string 轮胎规格 { get; set; }

       /// <summary>
       ///规格尺寸
       /// </summary>
       [Display(Name ="规格尺寸")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string 规格尺寸 { get; set; }

       /// <summary>
       ///规格花纹
       /// </summary>
       [Display(Name ="规格花纹")]
       [Column(TypeName="varchar(max)")]
       [Editable(true)]
       public string 规格花纹 { get; set; }

       /// <summary>
       ///花纹
       /// </summary>
       [Display(Name ="花纹")]
       [MaxLength(10)]
       [Column(TypeName="varchar(10)")]
       [Editable(true)]
       public string 花纹 { get; set; }

       /// <summary>
       ///别名品牌
       /// </summary>
       [Display(Name ="别名品牌")]
       [MaxLength(80)]
       [Column(TypeName="varchar(80)")]
       [Editable(true)]
       public string 别名品牌 { get; set; }

       /// <summary>
       ///备用
       /// </summary>
       [Display(Name ="备用")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string 备用 { get; set; }

       /// <summary>
       ///胎重
       /// </summary>
       [Display(Name ="胎重")]
       [Column(TypeName="numeric")]
       [Editable(true)]
       public decimal? 胎重 { get; set; }

       /// <summary>
       ///sys规格
       /// </summary>
       [Display(Name ="sys规格")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string sys规格 { get; set; }

       /// <summary>
       ///sys层级
       /// </summary>
       [Display(Name ="sys层级")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string sys层级 { get; set; }

       /// <summary>
       ///sys负荷等级
       /// </summary>
       [Display(Name ="sys负荷等级")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string sys负荷等级 { get; set; }

       /// <summary>
       ///sys速度层级
       /// </summary>
       [Display(Name ="sys速度层级")]
       [MaxLength(2)]
       [Column(TypeName="varchar(2)")]
       [Editable(true)]
       public string sys速度层级 { get; set; }

       /// <summary>
       ///sys花纹
       /// </summary>
       [Display(Name ="sys花纹")]
       [MaxLength(255)]
       [Column(TypeName="varchar(255)")]
       [Editable(true)]
       public string sys花纹 { get; set; }

       /// <summary>
       ///分隔符位
       /// </summary>
       [Display(Name ="分隔符位")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? 分隔符位 { get; set; }

       /// <summary>
       ///是否删除
       /// </summary>
       [Display(Name ="是否删除")]
       [Column(TypeName="smallint")]
       [Editable(true)]
       public int? 是否删除 { get; set; }

       /// <summary>
       ///sys品牌
       /// </summary>
       [Display(Name ="sys品牌")]
       [MaxLength(7)]
       [Column(TypeName="varchar(7)")]
       [Editable(true)]
       public string sys品牌 { get; set; }

       
    }
}