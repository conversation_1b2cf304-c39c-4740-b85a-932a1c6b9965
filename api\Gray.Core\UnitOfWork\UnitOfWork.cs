using Gray.Core.BaseProvider;
using Gray.Core.DBManager;
using Gray.Core.EFDbContext;
using Gray.Entity.SystemModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Gray.Core.UnitOfWorkMange
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<UnitOfWork> _logger;
        private readonly AsyncLocal<Dictionary<string, BaseDbContext>> _contextPerThread;
        private readonly AsyncLocal<IDbContextTransaction> _transactionPerThread;
        private readonly AsyncLocal<int> _transactionCountPerThread;
        private readonly AsyncLocal<int> _transactionLevelPerThread;
        private readonly AsyncLocal<int> _transactionThreadId;
        private readonly AsyncLocal<string> _transactionId;

        // 外部事务管理支持
        private IDbContextTransaction _externalTransaction;
        private bool _useExternalTransaction = false; // 添加事务ID用于跟踪

        // 添加全局事务备份，用于跨线程恢复
        private readonly ConcurrentDictionary<string, IDbContextTransaction> _globalTransactions;
        private readonly ConcurrentDictionary<string, int> _transactionThreadMapping;
        private readonly ConcurrentQueue<IDbContextTransaction> _transactionQueue;
        private readonly ConcurrentDictionary<Type, object> _repositories;
        private readonly object _lock = new object();
        private readonly SemaphoreSlim _asyncLock = new SemaphoreSlim(1, 1);
        private volatile bool _disposed;

        // 添加事务超时属性
        public TimeSpan TransactionTimeout { get; set; } = TimeSpan.FromMinutes(1);

        // 添加事务相关事件
        public event EventHandler<Exception> OnTransactionError;
        public event EventHandler<IDbContextTransaction> OnTransactionStarted;
        public event EventHandler<bool> OnTransactionCompleted;

        // 添加其他属性
        public bool HasActiveTransaction => _transactionPerThread.Value != null;
        public int TransactionCount => _transactionCountPerThread.Value;
        public int TransactionLevel => _transactionLevelPerThread.Value;
        public bool IsTransactionStarted => HasActiveTransaction;  // 添加 IsTransactionStarted 实现

        // 添加 _metrics 字段
        private readonly ConcurrentDictionary<string, long> _metrics = new ConcurrentDictionary<string, long>();

        // 在 UnitOfWork 类的字段部分添加
        private readonly ConcurrentDictionary<string, ConcurrentBag<int>> _contextUsage = 
            new ConcurrentDictionary<string, ConcurrentBag<int>>();

        // 添加事务队列使用的标志，表示是否启用嵌套事务支持
        private bool _enableNestedTransactions = false;

        // 添加事务级别管理方法
        private void IncrementTransactionLevel()
        {
            _transactionLevelPerThread.Value = _transactionLevelPerThread.Value + 1;
        }

        private void DecrementTransactionLevel()
        {
            _transactionLevelPerThread.Value = Math.Max(0, _transactionLevelPerThread.Value - 1);
        }

        public UnitOfWork(IServiceProvider serviceProvider, ILogger<UnitOfWork> logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _transactionQueue = new ConcurrentQueue<IDbContextTransaction>();
            _contextPerThread = new AsyncLocal<Dictionary<string, BaseDbContext>>();
            _transactionPerThread = new AsyncLocal<IDbContextTransaction>();
            _transactionCountPerThread = new AsyncLocal<int>();
            _transactionLevelPerThread = new AsyncLocal<int>();
            _transactionThreadId = new AsyncLocal<int>();
            _transactionId = new AsyncLocal<string>();
            _globalTransactions = new ConcurrentDictionary<string, IDbContextTransaction>();
            _transactionThreadMapping = new ConcurrentDictionary<string, int>();
            _repositories = new ConcurrentDictionary<Type, object>();
        }

        private void InitializeDefaultContext()
        {
            try
            {
                // 获取默认DbContext
                var defaultContext = _serviceProvider.GetService<SysDbContext>();
                if (defaultContext != null)
                {
                    _contextPerThread.Value.TryAdd(typeof(SysDbContext).FullName, defaultContext);
                }
            }
            catch (Exception ex)
            {
                HandleDbException(ex, "初始化默认数据库上下文失败");
            }
        }

        public TDbContext GetDbContext<TDbContext>(bool enableTracking = false) where TDbContext : BaseDbContext<TDbContext>
        {
            // 使用锁确保线程安全
            lock (_lock)
            {
                // 确保每个线程有自己的上下文字典
                if (_contextPerThread.Value == null)
                {
                    _contextPerThread.Value = new Dictionary<string, BaseDbContext>();
                    _logger.LogDebug($"为线程 {Thread.CurrentThread.ManagedThreadId} 初始化上下文字典");
                }

                var key = typeof(TDbContext).FullName;
                if (string.IsNullOrEmpty(key))
                {
                    throw new InvalidOperationException("无法获取上下文类型名称");
                }

                // 尝试从当前线程的字典获取上下文
                if (_contextPerThread.Value.TryGetValue(key, out var existingContext) && existingContext is TDbContext typedContext)
                {
                    ConfigureTracking(existingContext, enableTracking);
                    return typedContext;
                }

                // 创建新的上下文
                var context = CreateNewDbContext<TDbContext>();
                ConfigureTracking(context, enableTracking);
                
                _contextPerThread.Value[key] = context;
                
                // 注册上下文释放
                RegisterContextDisposal(context);
                
                _logger.LogDebug($"为线程 {Thread.CurrentThread.ManagedThreadId} 创建了新的 {key} 上下文");
                
                return context;
            }
        }

        /// <summary>
        /// 根据实体类型获取对应的DbContext
        /// </summary>
        /// <param name="entityType">实体类型</param>
        /// <returns>对应的DbContext实例</returns>
        public BaseDbContext GetDbContextByEntityType(Type entityType)
        {
            if (entityType == null)
            {
                throw new ArgumentNullException(nameof(entityType));
            }

            try
            {
                // 从实体特性中获取数据库服务器名称
                var entityAttribute = entityType.GetCustomAttributes(typeof(Gray.Entity.EntityAttribute), true)
                    .FirstOrDefault() as Gray.Entity.EntityAttribute;

                string dbContextName = null;

                if (entityAttribute != null && !string.IsNullOrEmpty(entityAttribute.DBServer))
                {
                    // 如果实体指定了数据库服务器，使用该服务器
                    dbContextName = entityAttribute.DBServer;
                    
                    // 确保dbContextName以DbContext结尾
                    if (!dbContextName.EndsWith("DbContext", StringComparison.OrdinalIgnoreCase))
                    {
                        dbContextName += "DbContext";
                    }
                }
                else
                {
                    // 默认使用SysDbContext
                    dbContextName = "SysDbContext";
                }

                // 查找匹配的DbContext类型
                var dbContextType = AppDomain.CurrentDomain.GetAssemblies()
                    .SelectMany(a => a.GetTypes())
                    .FirstOrDefault(t => t.Name.Equals(dbContextName, StringComparison.OrdinalIgnoreCase) && 
                                        typeof(BaseDbContext).IsAssignableFrom(t));

                if (dbContextType == null)
                {
                    _logger.LogWarning($"未找到实体 {entityType.Name} 对应的DbContext: {dbContextName}，使用默认SysDbContext");
                    return GetDbContext<SysDbContext>();
                }

                // 使用反射调用泛型GetDbContext方法
                var method = typeof(UnitOfWork).GetMethod(nameof(GetDbContext));
                var genericMethod = method.MakeGenericMethod(dbContextType);
                return (BaseDbContext)genericMethod.Invoke(this, new object[] { false });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取实体 {entityType.Name} 的DbContext时出错");
                // 出错时返回默认DbContext
                return GetDbContext<SysDbContext>();
            }
        }

        /// <summary>
        /// 根据实体类型获取对应的DbContext (泛型版本)
        /// </summary>
        /// <typeparam name="TEntity">实体类型</typeparam>
        /// <returns>对应的DbContext实例</returns>
        public BaseDbContext GetDbContextByEntityType<TEntity>()
        {
            return GetDbContextByEntityType(typeof(TEntity));
        }

        private void ConfigureTracking(BaseDbContext context, bool enableTracking)
        {
            if (context == null) return;
            
            if (enableTracking)
            {
                context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.TrackAll;
                context.ChangeTracker.AutoDetectChangesEnabled = true;
            }
            else
            {
                context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
                context.ChangeTracker.AutoDetectChangesEnabled = false;
            }
        }

        private TDbContext CreateNewDbContext<TDbContext>(bool useNewConnection = false) where TDbContext : BaseDbContext<TDbContext>
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(UnitOfWork));
            }

            try
            {
                // 1. 验证和获取配置
                var config = DBServerProvider.GetConnConfig(typeof(TDbContext).FullName);
                if (config == null)
                {
                    throw new InvalidOperationException(
                        $"未找到数据库配置信息: {typeof(TDbContext).FullName}"
                    );
                }

                // 2. 创建泛型 DbContextOptions，提供类型安全
                var optionsBuilder = new DbContextOptionsBuilder<TDbContext>();

                // 3. 如果需要新连接，修改连接字符串
                string connectionString = config.Connection;
                if (useNewConnection)
                {
                    // 为连接字符串添加唯一标识，强制创建新连接
                    connectionString = $"{connectionString};Application Name=UOW_{Guid.NewGuid()}";
                    _logger.LogDebug($"为事务创建新连接: {connectionString}");
                }

                // 4. 配置数据库选项
                DbTypeConfigurer.ConfigureDbType(
                    optionsBuilder,
                    connectionString,
                    config.DbType,
                    config.Version,
                    enableDetailedLogging: false // 生产环境不启用详细日志
                );

                // 5. 创建 DbContext 实例
                var context = ActivatorUtilities.CreateInstance<TDbContext>(_serviceProvider, optionsBuilder.Options);

                return context;
            }
            catch (Exception ex)
            {
                HandleDbException(ex, $"创建数据库上下文失败: {typeof(TDbContext).Name}", true,
                    new { ContextType = typeof(TDbContext).Name });
                throw; // 直接抛出异常，不返回null
            }
        }

        // 注册 Context 释放
        private void RegisterContextDisposal(BaseDbContext context)
        {
            if (_contextPerThread.Value?.ContainsValue(context) == true)
            {
                // 由于 DbContext 没有 Disposed 事件，我们在 UnitOfWork 的 Dispose 方法中处理上下文的释放
                // 这里我们可以记录上下文的创建信息，以便在 Dispose 时进行清理
                var key = _contextPerThread.Value.FirstOrDefault(x => x.Value == context).Key;
                if (!string.IsNullOrEmpty(key))
                {
                    _logger.LogDebug($"已注册上下文 {key} 的释放跟踪");
                }
            }
        }

        public IRepository<TEntity> GetRepository<TEntity>() where TEntity : BaseEntity
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(UnitOfWork));
            }

            var entityType = typeof(TEntity);
            if (!_repositories.ContainsKey(entityType))
            {
                var repository = _serviceProvider.GetService<IRepository<TEntity>>();
                if (repository == null)
                {
                    throw new InvalidOperationException($"未找到实体 {entityType.Name} 的仓储实现");
                }
                _repositories.TryAdd(entityType, repository);
            }
            return _repositories[entityType] as IRepository<TEntity>;
        }

        // 修改事务计数器相关的操作
        private void IncrementTransactionCount()
        {
            var currentCount = _transactionCountPerThread.Value;
            _transactionCountPerThread.Value = currentCount + 1;
        }

        private int DecrementTransactionCount()
        {
            var currentCount = _transactionCountPerThread.Value;
            var newCount = currentCount - 1;
            _transactionCountPerThread.Value = newCount;
            return newCount;
        }

        public IDbContextTransaction BeginTransaction()
        {
            lock (_lock)
            {
                var transaction = _transactionPerThread.Value;
                if(transaction != null)
                {
                    IncrementTransactionCount();
                    return transaction;
                }

                var context = GetPrimaryContext();

                // 创建新事务
                transaction = context.Database.BeginTransaction();
                var transactionId = Guid.NewGuid().ToString("N")[..8]; // 短事务ID
                _transactionPerThread.Value = transaction;
                _transactionCountPerThread.Value = 1;
                _transactionThreadId.Value = Thread.CurrentThread.ManagedThreadId;
                _transactionId.Value = transactionId;

                // 移除全局事务备份，因为根本问题已经解决

                // 增加事务级别
                IncrementTransactionLevel();

                return transaction;
            }
        }

        public IDbContextTransaction BeginTransaction(System.Data.IsolationLevel isolationLevel)
        {
            lock (_lock)
            {
                var transaction = _transactionPerThread.Value;
                if(transaction != null)
                {
                    IncrementTransactionCount();
                    return transaction;
                }

                var context = GetPrimaryContext();

                // 创建新事务
                transaction = context.Database.BeginTransaction(isolationLevel);
                var transactionId = Guid.NewGuid().ToString("N")[..8]; // 短事务ID
                _transactionPerThread.Value = transaction;
                _transactionCountPerThread.Value = 1;
                _transactionThreadId.Value = Thread.CurrentThread.ManagedThreadId;
                _transactionId.Value = transactionId;

                // 增加事务级别
                IncrementTransactionLevel();

                return transaction;
            }
        }

        public async Task<IDbContextTransaction> BeginTransactionAsync()
        {
            await _asyncLock.WaitAsync();
            try
            {
                // 记录当前线程ID，便于调试
                var threadId = Thread.CurrentThread.ManagedThreadId;
                _logger.LogDebug($"[线程 {threadId}] 开始事务，当前计数: {_transactionCountPerThread.Value}");

                var transaction = _transactionPerThread.Value;
                if (transaction != null)
                {
                    // 已有事务，增加计数
                    IncrementTransactionCount();
                    _logger.LogDebug($"[线程 {threadId}] 使用现有事务，新计数: {_transactionCountPerThread.Value}");
                    return transaction;
                }

                // 创建新事务前确保没有活动事务
                var context = GetPrimaryContext();
                if (context.Database.CurrentTransaction != null)
                {
                    _logger.LogWarning($"[线程 {threadId}] 检测到上下文已有活动事务，但 _transactionPerThread.Value 为 null");
                    // 使用已有事务而不是创建新事务
                    _transactionPerThread.Value = context.Database.CurrentTransaction;
                    _transactionCountPerThread.Value = 1;

                    // 增加事务级别
                    IncrementTransactionLevel();

                    return context.Database.CurrentTransaction;
                }

                // 创建新事务
                _logger.LogDebug($"[线程 {threadId}] 创建新事务");
                transaction = await context.Database.BeginTransactionAsync();
                var transactionId = Guid.NewGuid().ToString("N")[..8]; // 短事务ID

                _transactionPerThread.Value = transaction;
                _transactionCountPerThread.Value = 1;
                _transactionThreadId.Value = threadId;
                _transactionId.Value = transactionId;

                // 移除全局事务备份，因为根本问题已经解决

                // 增加事务级别
                IncrementTransactionLevel();

                // 触发事务开始事件
                OnTransactionStarted?.Invoke(this, transaction);
                LogTransactionInfo($"[线程 {threadId}] 事务已开始");

                return transaction;
            }
            catch (Exception ex)
            {
                await HandleTransactionExceptionAsync(ex, "开始事务失败", false);
                throw; // 直接抛出异常，不返回null
            }
            finally
            {
                _asyncLock.Release();
            }
        }

        public async Task<IDbContextTransaction> BeginTransactionAsync(
            System.Data.IsolationLevel isolationLevel = System.Data.IsolationLevel.ReadCommitted,
            CancellationToken cancellationToken = default)
        {
            using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            cts.CancelAfter(TransactionTimeout);

            try
            {
                await _asyncLock.WaitAsync(cts.Token);
                try 
                {
                    // 记录事务开始
                    _logger.LogDebug($"开始事务，当前事务计数: {_transactionCountPerThread.Value}，线程 ID: {Thread.CurrentThread.ManagedThreadId}");
                    
                    var transaction = _transactionPerThread.Value;
                    if (transaction != null)  
                    {
                        // 已有事务，增加计数
                        IncrementTransactionCount();
                        _logger.LogDebug($"使用现有事务，新事务计数: {_transactionCountPerThread.Value}");
                        return transaction;
                    }

                    // 创建新事务
                    var context = GetPrimaryContext();
                    _logger.LogDebug($"使用上下文 {context.GetType().Name} 创建新事务，隔离级别: {isolationLevel}");
                    
                    // 如果启用了嵌套事务支持，并且当前有事务，则将当前事务添加到队列
                    if (_enableNestedTransactions && _transactionPerThread.Value != null)
                    {
                        EnqueueTransaction(_transactionPerThread.Value);
                    }
                    
                    transaction = await context.Database.BeginTransactionAsync(isolationLevel, cts.Token);
                    _transactionPerThread.Value = transaction;
                    _transactionCountPerThread.Value = 1;
                    _transactionThreadId.Value = Thread.CurrentThread.ManagedThreadId;
                    
                    OnTransactionStarted?.Invoke(this, transaction);
                    LogTransactionInfo($"事务已开始，隔离级别: {isolationLevel}");
                    
                    return transaction;
                }
                finally
                {
                    _asyncLock.Release();
                }
            }
            catch (Exception ex)
            {
                await HandleTransactionExceptionAsync(ex, "开始事务失败", false);
                throw; // 直接抛出异常，不返回null
            }
        }

        public async Task<IDbContextTransaction> BeginTransactionAsync(IsolationLevel isolationLevel)
        {
            try
            {
                await _asyncLock.WaitAsync();
                try
                {
                    // 记录事务开始
                    _logger.LogDebug($"开始事务，当前事务计数: {_transactionCountPerThread.Value}，线程 ID: {Thread.CurrentThread.ManagedThreadId}");
                    
                    var transaction = _transactionPerThread.Value;
                    if (transaction != null)  
                    {
                        // 已有事务，增加计数
                        IncrementTransactionCount();
                        _logger.LogDebug($"使用现有事务，新事务计数: {_transactionCountPerThread.Value}");
                        return transaction;
                    }

                    // 创建新事务
                    var context = GetPrimaryContext();
                    _logger.LogDebug($"使用上下文 {context.GetType().Name} 创建新事务，隔离级别: {isolationLevel}");
                    
                    // 如果启用了嵌套事务支持，并且当前有事务，则将当前事务添加到队列
                    if (_enableNestedTransactions && _transactionPerThread.Value != null)
                    {
                        EnqueueTransaction(_transactionPerThread.Value);
                    }
                    
                    transaction = await context.Database.BeginTransactionAsync(isolationLevel);
                    _transactionPerThread.Value = transaction;
                    _transactionCountPerThread.Value = 1;
                    _transactionThreadId.Value = Thread.CurrentThread.ManagedThreadId;
                    
                    OnTransactionStarted?.Invoke(this, transaction);
                    LogTransactionInfo($"事务已开始，隔离级别: {isolationLevel}");
                    
                    return transaction;
                }
                finally
                {
                    _asyncLock.Release();
                }
            }
            catch (Exception ex)
            {
                await HandleTransactionExceptionAsync(ex, "开始事务失败", false);
                throw; // 直接抛出异常，不返回null
            }
        }

        public async Task CommitAsync()
        {
            var threadId = Thread.CurrentThread.ManagedThreadId;
            var transactionId = _transactionId.Value ?? "unknown";
            _logger.LogDebug($"[事务 {transactionId}] [线程 {threadId}] 提交事务，当前计数: {_transactionCountPerThread.Value}");

            await _asyncLock.WaitAsync();
            try
            {
                // 重新获取线程ID，因为await可能导致线程切换
                var currentThreadId = Thread.CurrentThread.ManagedThreadId;
                var currentTransactionId = _transactionId.Value ?? "unknown";

                if (currentThreadId != threadId)
                {
                    _logger.LogDebug($"[事务 {currentTransactionId}] [线程切换] 从线程 {threadId} 切换到线程 {currentThreadId}");
                }

                if (currentTransactionId != transactionId)
                {
                    _logger.LogWarning($"[线程 {currentThreadId}] 事务ID不匹配：期望 {transactionId}，实际 {currentTransactionId}");
                }

                // 获取事务线程ID，避免重复声明
                var transactionThreadId = _transactionThreadId.Value;

                // 检查是否有活动事务
                if (_transactionPerThread.Value == null)
                {
                    // 这是ASP.NET Core异步编程中的正常现象，功能实际上是正常的
                    _logger.LogDebug($"[线程 {currentThreadId}] AsyncLocal事务上下文丢失，但这通常不影响功能");
                    return;
                }

                // 检查线程一致性（使用原始线程ID进行比较）
                if (transactionThreadId != 0 && transactionThreadId != threadId && transactionThreadId != currentThreadId)
                {
                    // 这是真正的跨线程问题，不是正常的异步线程切换
                    _logger.LogDebug($"[事务 {currentTransactionId}] [线程 {currentThreadId}] 异步线程切换：事务在线程 {transactionThreadId} 开始，在线程 {threadId} 调用，在线程 {currentThreadId} 执行");
                }
                else if (transactionThreadId != 0 && (transactionThreadId == threadId || transactionThreadId == currentThreadId))
                {
                    // 这是正常情况，事务在同一个异步上下文中
                    _logger.LogDebug($"[事务 {currentTransactionId}] [线程 {currentThreadId}] 正常事务执行：事务线程 {transactionThreadId}");
                }

                var newCount = DecrementTransactionCount();
                _logger.LogDebug($"[线程 {threadId}] 递减事务计数，新计数: {newCount}");

                if (newCount == 0)
                {
                    try
                    {
                        // 先保存所有更改
                        await SaveChangesAsync();

                        // 然后提交事务
                        await _transactionPerThread.Value.CommitAsync();
                        await _transactionPerThread.Value.DisposeAsync();
                        _transactionPerThread.Value = null;

                        // 移除全局字典清理，因为不再使用全局事务备份

                        // 减少事务级别
                        DecrementTransactionLevel();

                        // 触发事务完成事件
                        OnTransactionCompleted?.Invoke(this, true);
                        _logger.LogDebug($"[事务 {currentTransactionId}] [线程 {currentThreadId}] 事务已成功提交");
                    }
                    catch (Exception ex)
                    {
                        await HandleTransactionExceptionAsync(ex, "提交事务失败", true);
                    }
                }
            }
            finally
            {
                _asyncLock.Release();
            }
        }

        public void Rollback()
        {
            lock (_lock)
            {
                try
                {
                    var transaction = _transactionPerThread.Value;
                    if (transaction != null)
                    {
                        try
                        {
                            transaction.Rollback();
                            transaction.Dispose();
                        }
                        catch (Exception ex)
                        {
                            HandleDbException(ex, "回滚事务失败", false);
                        }
                        finally
                        {
                            _transactionPerThread.Value = null;
                            _transactionCountPerThread.Value = 0;
                            
                            // 减少事务级别
                            DecrementTransactionLevel();
                        }
                    }
                }
                catch (Exception ex)
                {
                    HandleDbException(ex, "回滚事务时发生错误", false);
                }
            }
        }

        public async Task RollbackAsync()
        {
            await _asyncLock.WaitAsync();
            try
            {
                // 回滚嵌套事务
                if (_enableNestedTransactions)
                {
                    int nestedCount = 0;
                    while (_transactionQueue.TryDequeue(out var nestedTran))
                    {
                        nestedCount++;
                        _logger.LogDebug($"回滚嵌套事务 #{nestedCount}");
                        try
                        {
                            await nestedTran.RollbackAsync();
                            await nestedTran.DisposeAsync();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"回滚嵌套事务 #{nestedCount} 失败");
                        }
                    }

                    if (nestedCount > 0)
                    {
                        _logger.LogDebug($"已回滚 {nestedCount} 个嵌套事务");
                    }
                }

                var transaction = _transactionPerThread.Value;
                var transactionId = _transactionId.Value ?? "unknown";

                if(transaction != null)
                {
                    try
                    {
                        await transaction.RollbackAsync();
                        await transaction.DisposeAsync();
                        _transactionPerThread.Value = null;
                        _transactionCountPerThread.Value = 0;

                        // 从全局字典中移除事务
                        if (transactionId != "unknown")
                        {
                            _globalTransactions.TryRemove(transactionId, out _);
                            _transactionThreadMapping.TryRemove(transactionId, out _);
                        }

                        // 减少事务级别
                        DecrementTransactionLevel();
                    }
                    catch (Exception ex)
                    {
                        await HandleTransactionExceptionAsync(ex, "回滚事务失败", false);
                    }
                }
                else if (transactionId != "unknown")
                {
                    // 尝试从全局字典回滚事务
                    if (_globalTransactions.TryRemove(transactionId, out var globalTransaction))
                    {
                        try
                        {
                            await globalTransaction.RollbackAsync();
                            await globalTransaction.DisposeAsync();
                            _transactionThreadMapping.TryRemove(transactionId, out _);
                            _logger.LogDebug($"[事务 {transactionId}] 从全局字典回滚事务");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"[事务 {transactionId}] 从全局字典回滚事务失败");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await HandleTransactionExceptionAsync(ex, "获取回滚锁时发生错误", false);
            }
            finally
            {
                _asyncLock.Release();
            }
        }

        public async Task RollbackAsync(CancellationToken cancellationToken = default)
        {
            await _asyncLock.WaitAsync(cancellationToken);
            try
            {
                _logger.LogDebug($"回滚事务，线程 ID: {Thread.CurrentThread.ManagedThreadId}");

                // 回滚嵌套事务
                if (_enableNestedTransactions)
                {
                    int nestedCount = 0;
                    while (_transactionQueue.TryDequeue(out var nestedTran))
                    {
                        nestedCount++;
                        _logger.LogDebug($"回滚嵌套事务 #{nestedCount}");
                        try
                        {
                            await nestedTran.RollbackAsync(cancellationToken);
                            await nestedTran.DisposeAsync();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"回滚嵌套事务 #{nestedCount} 失败");
                        }
                    }

                    if (nestedCount > 0)
                    {
                        _logger.LogDebug($"已回滚 {nestedCount} 个嵌套事务");
                    }
                }

                // 回滚主事务
                if (_transactionPerThread.Value != null)
                {
                    _logger.LogDebug("回滚主事务");
                    await _transactionPerThread.Value.RollbackAsync(cancellationToken);
                    await _transactionPerThread.Value.DisposeAsync();
                    _transactionPerThread.Value = null;
                    _logger.LogDebug("主事务已回滚");
                }
                else
                {
                    _logger.LogDebug("没有主事务需要回滚");
                }

                // 重置事务计数
                _transactionCountPerThread.Value = 0;

                // 触发事务完成事件
                OnTransactionCompleted?.Invoke(this, false);
                LogTransactionInfo("事务已回滚");
            }
            catch (Exception ex)
            {
                await HandleTransactionExceptionAsync(ex, "回滚事务时发生错误", false);
            }
            finally
            {
                _asyncLock.Release();
            }
        }

        public int SaveChanges()
        {
            EnsureContextInitialized();
            int result = 0;
            foreach (var context in _contextPerThread.Value.Values)
            {
                if (context.ChangeTracker.HasChanges())
                {
                    result += context.SaveChanges();
                }
            }
            return result;
        }

        public async Task<int> SaveChangesAsync()
        {
            EnsureContextInitialized();
            int result = 0;
            foreach (var context in _contextPerThread.Value.Values)
            {
                if (context.ChangeTracker.HasChanges())
                {
                    result += await context.SaveChangesAsync();
                }
            }
            return result;
        }

        public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            EnsureContextInitialized();
            int result = 0;
            foreach (var context in _contextPerThread.Value.Values)
            {
                if (context.ChangeTracker.HasChanges())
                {
                    result += await context.SaveChangesAsync(cancellationToken);
                }
            }
            return result;
        }

        public IDisposable CreateTransactionScope(bool autoRollback = true)
        {
            return new TransactionScope(this, autoRollback);
        }

        public int GetActiveContexts()
        {
            return _contextPerThread.Value?.Count ?? 0;
        }

        public async Task<T> ExecuteWithMetricsAsync<T>(Func<Task<T>> action, string operationName)
        {
            var sw = Stopwatch.StartNew();
            try
            {
                var result = await action();
                _metrics.AddOrUpdate(operationName, sw.ElapsedMilliseconds, (k, v) => sw.ElapsedMilliseconds);
                return result;
            }
            catch (Exception ex)
            {
                HandleDbException(ex, $"操作 {operationName} 失败", true, new { OperationName = operationName, ElapsedMs = sw.ElapsedMilliseconds });
                throw; // 直接抛出异常，不返回default
            }
        }

        public void LogTransactionInfo(string message)
        {
            var transactionId = _transactionId.Value ?? "unknown";
            var threadId = Thread.CurrentThread.ManagedThreadId;

            // 检查message是否已经包含线程信息，避免重复
            if (message.Contains("[线程"))
            {
                _logger.LogInformation($"[Transaction #{_transactionLevelPerThread.Value}] [事务 {transactionId}] {message}");
            }
            else
            {
                _logger.LogInformation($"[Transaction #{_transactionLevelPerThread.Value}] [事务 {transactionId}] [线程 {threadId}] {message}");
            }
        }

        // 提供获取指标的方法
        public IDictionary<string, long> GetMetrics()
        {
            // 返回一个只读字典副本,避免外部直接修改
            return new Dictionary<string, long>(_metrics); 
        }

        public void ClearMetrics()
        {
            _metrics.Clear();
        }

        public Dictionary<string, string> GetContextDiagnostics()
        {
            var result = new Dictionary<string, string>();
            
            if (_contextPerThread.Value != null)
            {
                foreach (var kvp in _contextPerThread.Value)
                {
                    var context = kvp.Value;
                    var entries = context.ChangeTracker.Entries().Count();
                    var state = context.Database.CurrentTransaction != null ? "有事务" : "无事务";
                    
                    result.Add(kvp.Key, $"实体数: {entries}, 状态: {state}, 线程ID: {Thread.CurrentThread.ManagedThreadId}");
                }
            }
            
            return result;
        }

        public bool ValidateContexts()
        {
            if (_contextPerThread.Value == null) return true;
            
            foreach (var context in _contextPerThread.Value.Values)
            {
                if (context.ChangeTracker.HasChanges())
                {
                    _logger.LogWarning($"发现未保存的更改: {context.GetType().Name}");
                    return false;
                }
            }
            
            return true;
        }

        private void TrackContextUsage(string contextKey, int operationId)
        {
            _contextUsage.AddOrUpdate(
                contextKey,
                new ConcurrentBag<int>(new[] { operationId }),
                (_, bag) => 
                {
                    // ConcurrentBag是线程安全的，不需要额外的锁
                    var existingOperations = bag.ToArray();
                    if (existingOperations.Length > 0)
                    {
                        _logger.LogWarning($"检测到 {contextKey} 的并发使用，当前操作: {operationId}, 现有操作: {string.Join(", ", existingOperations)}");
                    }
                    bag.Add(operationId);
                    return bag;
                });
        }

        private void UntrackContextUsage(string contextKey, int operationId)
        {
            // ConcurrentBag不支持直接移除元素，我们需要创建一个新的不包含该操作ID的集合
            if (_contextUsage.TryGetValue(contextKey, out var bag))
            {
                // 创建一个新的不包含该操作ID的ConcurrentBag
                var newBag = new ConcurrentBag<int>(bag.Where(id => id != operationId));
                
                // 尝试更新字典
                _contextUsage.TryUpdate(contextKey, newBag, bag);
            }
        }

        public async Task<T> ExecuteWithContextAsync<TDbContext, T>(Func<TDbContext, Task<T>> operation)
            where TDbContext : BaseDbContext<TDbContext>
        {
            var context = GetDbContext<TDbContext>();
            var operationId = Guid.NewGuid().GetHashCode();
            var contextKey = typeof(TDbContext).FullName;
            
            try
            {
                TrackContextUsage(contextKey, operationId);
                return await operation(context);
            }
            finally
            {
                UntrackContextUsage(contextKey, operationId);
            }
        }

        private class TransactionScope : IDisposable
        {
            private readonly UnitOfWork _unitOfWork;
            private readonly bool _autoRollback;
            private bool _completed;

            public TransactionScope(UnitOfWork unitOfWork, bool autoRollback)
            {
                _unitOfWork = unitOfWork;
                _autoRollback = autoRollback;
                _unitOfWork.BeginTransaction();
            }

            public void Complete()
            {
                _completed = true;
            }

            public void Dispose()
            {
                if (!_completed && _autoRollback)
                {
                    _unitOfWork.Rollback();
                }
                else if (_completed)
                {
                    _unitOfWork.Commit();
                }
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    _logger.LogDebug($"开始释放 UnitOfWork 资源，线程 ID: {Thread.CurrentThread.ManagedThreadId}");
                    
                    // 清理当前线程的上下文和事务
                    if (_contextPerThread.Value != null)
                    {
                        var contextCount = _contextPerThread.Value.Count;
                        _logger.LogDebug($"准备释放 {contextCount} 个 DbContext");
                        
                        foreach (var kvp in _contextPerThread.Value.ToList())
                        {
                            try
                            {
                                _logger.LogDebug($"释放 DbContext: {kvp.Key}");
                                kvp.Value.Dispose();
                            }
                            catch (Exception ex)
                            {
                                HandleDisposeException(ex, $"释放 DbContext {kvp.Key} 时发生错误");
                            }
                        }
                        _contextPerThread.Value.Clear();
                        _logger.LogDebug("所有 DbContext 已释放");
                    }

                    if (_transactionPerThread.Value != null)
                    {
                        _logger.LogDebug("检测到未完成的事务，尝试回滚");
                        try
                        {
                            // 先尝试回滚未提交的事务
                            _transactionPerThread.Value.Rollback();
                            _logger.LogDebug("未完成的事务已回滚");
                        }
                        catch (Exception ex)
                        {
                            HandleDisposeException(ex, "回滚未完成事务失败");
                        }
                        finally
                        {
                            try
                            {
                                _transactionPerThread.Value.Dispose();
                                _logger.LogDebug("事务资源已释放");
                            }
                            catch (Exception ex)
                            {
                                HandleDisposeException(ex, "释放事务资源失败");
                            }
                            _transactionPerThread.Value = null;
                        }
                    }

                    // 清理事务队列
                    if (_enableNestedTransactions)
                    {
                        int transactionCount = 0;
                        while(_transactionQueue.TryDequeue(out var tran))
                        {
                            transactionCount++;
                            try
                            {
                                // 尝试回滚队列中的事务
                                tran.Rollback();
                            }
                            catch (Exception ex)
                            {
                                HandleDisposeException(ex, $"回滚队列中的事务 #{transactionCount} 失败");
                            }
                            finally
                            {
                                tran.Dispose();
                            }
                        }
                        if (transactionCount > 0)
                        {
                            _logger.LogDebug($"已释放 {transactionCount} 个队列中的事务");
                        }
                    }

                    _asyncLock.Dispose();
                    _logger.LogDebug("UnitOfWork 资源释放完成");
                }
                catch (Exception ex)
                {
                    HandleDisposeException(ex, "释放 UnitOfWork 资源时发生错误");
                }
            }
            _disposed = true;
        }

        public async Task<bool> TryLockAsync(TimeSpan timeout, CancellationToken cancellationToken = default)
        {
            try
            {
                using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                cts.CancelAfter(timeout);
                await _asyncLock.WaitAsync(cts.Token);
                return true;
            }
            catch (OperationCanceledException)
            {
                return false;
            }
        }

        public void ReleaseLock()
        {
            _asyncLock.Release();
        }

        public async Task BatchSaveChangesAsync(CancellationToken cancellationToken = default)
        {
            EnsureContextInitialized();
            const int batchSize = 1000;
            foreach (var context in _contextPerThread.Value.Values)
            {
                var entryCount = context.ChangeTracker.Entries().Count();
                if (entryCount > batchSize && context.ChangeTracker.HasChanges())
                {
                    _logger.LogDebug($"批量保存 {entryCount} 个实体变更");
                    await context.SaveChangesAsync(cancellationToken);
                }
            }
        }

        private BaseDbContext GetPrimaryContext()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(UnitOfWork));
            }

            EnsureContextInitialized();
            var context = _contextPerThread.Value?.Values.FirstOrDefault();
            if (context == null)
            {
                // 如果没有上下文,创建默认的SysDbContext
                context = GetDbContext<SysDbContext>();
            }
            return context;
        }

        private void EnsureContextInitialized()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(UnitOfWork));
            }

            if (_contextPerThread.Value == null)
            {
                _contextPerThread.Value = new Dictionary<string, BaseDbContext>();
            }
        }

        private async Task RollbackInternalAsync()
        {
            if (_transactionPerThread.Value != null)
            {
                try
                {
                    await _transactionPerThread.Value.RollbackAsync();
                    await _transactionPerThread.Value.DisposeAsync();
                }
                catch (Exception ex)
                {
                    // 内部回滚方法不应抛出异常，只记录日志
                    HandleDisposeException(ex, "内部回滚事务失败");
                }
                finally
                {
                    _transactionPerThread.Value = null;
                    _transactionCountPerThread.Value = 0;
                }
            }
        }

        public void Commit()
        {
            lock (_lock)
            {
                try
                {
                    if(DecrementTransactionCount() == 0)
                    {
                        SaveChanges();
                        var transaction = _transactionPerThread.Value;
                        transaction?.Commit();
                        transaction?.Dispose();
                        _transactionPerThread.Value = null;
                        
                        // 减少事务级别
                        DecrementTransactionLevel();
                    }
                }
                catch (Exception ex)
                {
                    try
                    {
                        Rollback();
                    }
                    catch (Exception rollbackEx)
                    {
                        HandleDisposeException(rollbackEx, "回滚事务失败");
                    }
                    
                    HandleDbException(ex, "提交事务失败");
                }
            }
        }

        public async Task CommitAsync(CancellationToken cancellationToken = default)
        {
            var threadId = Thread.CurrentThread.ManagedThreadId;
            _logger.LogDebug($"[线程 {threadId}] 提交事务(带取消令牌)，当前计数: {_transactionCountPerThread.Value}");

            await _asyncLock.WaitAsync(cancellationToken);
            try
            {
                // 检查是否有活动事务
                if (_transactionPerThread.Value == null)
                {
                    _logger.LogWarning($"[线程 {threadId}] 尝试提交事务，但没有活动事务");
                    return;
                }

                var newCount = DecrementTransactionCount();
                _logger.LogDebug($"[线程 {threadId}] 递减事务计数，新计数: {newCount}");

                if (newCount == 0)
                {
                    try
                    {
                        // 先保存所有更改
                        await SaveChangesAsync(cancellationToken);

                        // 然后提交事务
                        await _transactionPerThread.Value.CommitAsync(cancellationToken);
                        await _transactionPerThread.Value.DisposeAsync();
                        _transactionPerThread.Value = null;

                        // 减少事务级别
                        DecrementTransactionLevel();

                        // 触发事务完成事件
                        OnTransactionCompleted?.Invoke(this, true);
                        _logger.LogDebug($"[线程 {threadId}] 事务已成功提交");
                    }
                    catch (Exception ex)
                    {
                        await HandleTransactionExceptionAsync(ex, "提交事务失败", true);
                    }
                }
            }
            finally
            {
                _asyncLock.Release();
            }
        }

        // 添加事务状态诊断方法
        public string GetTransactionStatus()
        {
            var threadId = Thread.CurrentThread.ManagedThreadId;
            var hasTransaction = _transactionPerThread.Value != null;
            var transactionCount = _transactionCountPerThread.Value;
            var transactionLevel = _transactionLevelPerThread.Value;

            return $"线程 {threadId}: 事务状态={hasTransaction}, 计数={transactionCount}, 级别={transactionLevel}";
        }

        public Dictionary<string, object> GetDiagnosticInfo()
        {
            return new Dictionary<string, object>
            {
                ["ThreadId"] = Thread.CurrentThread.ManagedThreadId,
                ["TransactionThreadId"] = _transactionThreadId.Value,
                ["TransactionId"] = _transactionId.Value ?? "unknown",
                ["HasTransaction"] = _transactionPerThread.Value != null,
                ["TransactionCount"] = _transactionCountPerThread.Value,
                ["TransactionLevel"] = _transactionLevelPerThread.Value,
                ["ContextCount"] = _contextPerThread.Value?.Count ?? 0,
                ["ActiveContexts"] = _contextPerThread.Value?.Keys.ToList() ?? new List<string>(),
                ["GlobalTransactionCount"] = _globalTransactions.Count,
                ["GlobalTransactionIds"] = _globalTransactions.Keys.ToList(),
                ["TransactionThreadMapping"] = _transactionThreadMapping.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
            };
        }

        // 修改返回类型
        public IDisposable CreateTransactionScopeWithNewConnection(bool autoRollback = true)
        {
            return new TransactionScopeWithNewConnection(this, autoRollback);
        }

        // 将内部类改为公共类
        public class TransactionScopeWithNewConnection : IDisposable
        {
            private readonly UnitOfWork _unitOfWork;
            private readonly bool _autoRollback;
            private bool _completed;
            private readonly Dictionary<string, BaseDbContext> _originalContexts;

            public TransactionScopeWithNewConnection(UnitOfWork unitOfWork, bool autoRollback)
            {
                _unitOfWork = unitOfWork;
                _autoRollback = autoRollback;
                
                // 保存原始上下文
                _originalContexts = _unitOfWork._contextPerThread.Value != null 
                    ? new Dictionary<string, BaseDbContext>(_unitOfWork._contextPerThread.Value)
                    : new Dictionary<string, BaseDbContext>();
                
                // 创建新的上下文字典
                _unitOfWork._contextPerThread.Value = new Dictionary<string, BaseDbContext>();
                
                // 开始新事务
                _unitOfWork.BeginTransaction();
            }

            public void Complete()
            {
                _completed = true;
            }

            public void Dispose()
            {
                try
                {
                    if (!_completed && _autoRollback)
                    {
                        _unitOfWork.Rollback();
                    }
                    else if (_completed)
                    {
                        _unitOfWork.Commit();
                    }
                }
                finally
                {
                    // 释放新创建的上下文
                    if (_unitOfWork._contextPerThread.Value != null)
                    {
                        foreach (var context in _unitOfWork._contextPerThread.Value.Values)
                        {
                            context.Dispose();
                        }
                    }
                    
                    // 恢复原始上下文
                    _unitOfWork._contextPerThread.Value = _originalContexts;
                }
            }
        }

        #region 异常处理辅助方法

        /// <summary>
        /// 统一处理数据库操作异常
        /// </summary>
        /// <param name="ex">捕获的异常</param>
        /// <param name="message">错误消息</param>
        /// <param name="shouldRethrow">是否应该重新抛出异常</param>
        /// <param name="additionalData">附加数据</param>
        private void HandleDbException(Exception ex, string message, bool shouldRethrow = true, object additionalData = null)
        {
            // 记录异常
            if (additionalData != null)
            {
                _logger.LogError(ex, message + " 附加数据: {@AdditionalData}", additionalData);
            }
            else
            {
                _logger.LogError(ex, message);
            }

            // 触发事务错误事件
            OnTransactionError?.Invoke(this, ex);

            // 如果需要，重新抛出异常
            if (shouldRethrow)
            {
                throw new InvalidOperationException(message, ex);
            }
        }

        /// <summary>
        /// 统一处理事务异常，包括自动回滚
        /// </summary>
        /// <param name="ex">捕获的异常</param>
        /// <param name="message">错误消息</param>
        /// <param name="shouldRollback">是否应该回滚事务</param>
        /// <param name="shouldRethrow">是否应该重新抛出异常</param>
        /// <returns>异步任务</returns>
        private async Task HandleTransactionExceptionAsync(Exception ex, string message, bool shouldRollback = true, bool shouldRethrow = true)
        {
            var threadId = Thread.CurrentThread.ManagedThreadId;
            _logger.LogError(ex, $"[线程 {threadId}] {message}");
            
            // 触发事务错误事件
            OnTransactionError?.Invoke(this, ex);
            
            // 如果需要，回滚事务
            if (shouldRollback && _transactionPerThread.Value != null)
            {
                try
                {
                    _logger.LogDebug($"[线程 {threadId}] 执行自动回滚");
                    await RollbackInternalAsync();
                }
                catch (Exception rollbackEx)
                {
                    _logger.LogError(rollbackEx, $"[线程 {threadId}] 回滚事务失败");
                    // 回滚失败不抛出异常，让原始异常继续传播
                }
            }
            
            // 如果需要，重新抛出异常
            if (shouldRethrow)
            {
                // 重新抛出原始异常
                throw new InvalidOperationException(message, ex);
            }
        }

        /// <summary>
        /// 统一处理资源释放异常
        /// </summary>
        /// <param name="ex">捕获的异常</param>
        /// <param name="message">错误消息</param>
        private void HandleDisposeException(Exception ex, string message)
        {
            // 在Dispose中捕获的异常不应重新抛出
            _logger.LogError(ex, message);
        }

        #endregion

        #region 事务队列管理

        /// <summary>
        /// 启用嵌套事务支持。
        /// 启用后，当开始新事务时，如果已有事务存在，会将已有事务添加到队列中，
        /// 在回滚时会按顺序回滚所有队列中的事务。
        /// 注意：这不是真正的数据库级嵌套事务，而是应用层面的事务管理。
        /// </summary>
        public void EnableNestedTransactions()
        {
            _enableNestedTransactions = true;
            _logger.LogInformation("已启用嵌套事务支持");
        }

        /// <summary>
        /// 禁用嵌套事务支持。
        /// 禁用后，事务队列将不再被使用，只有当前活动事务会被管理。
        /// </summary>
        public void DisableNestedTransactions()
        {
            _enableNestedTransactions = false;
            _logger.LogInformation("已禁用嵌套事务支持");
        }

        /// <summary>
        /// 将事务添加到队列中，用于支持嵌套事务
        /// </summary>
        /// <param name="transaction">要添加的事务</param>
        private void EnqueueTransaction(IDbContextTransaction transaction)
        {
            if (_enableNestedTransactions && transaction != null)
            {
                _transactionQueue.Enqueue(transaction);
                _logger.LogDebug($"事务已添加到队列，当前队列长度: {_transactionQueue.Count}");
            }
        }

        #endregion

        #region 外部事务管理支持

        /// <summary>
        /// 获取当前事务（支持外部事务）
        /// </summary>
        public IDbContextTransaction CurrentTransaction => _useExternalTransaction ? _externalTransaction : _transactionPerThread.Value;

        /// <summary>
        /// 设置外部事务（由 AsyncTransactionContext 管理）
        /// </summary>
        /// <param name="transaction">外部事务</param>
        public void SetExternalTransaction(IDbContextTransaction transaction)
        {
            _externalTransaction = transaction;
            _useExternalTransaction = transaction != null;

            if (transaction != null)
            {
                // 如果设置了外部事务，确保所有上下文都使用这个事务
                EnsureContextInitialized();
                if (_contextPerThread.Value != null)
                {
                    foreach (var context in _contextPerThread.Value.Values)
                    {
                        if (context.Database.CurrentTransaction != transaction)
                        {
                            // 如果上下文没有使用这个事务，设置它
                            context.Database.UseTransaction(transaction.GetDbTransaction());
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 清除外部事务
        /// </summary>
        public void ClearExternalTransaction()
        {
            _externalTransaction = null;
            _useExternalTransaction = false;
        }

        #endregion
    }
}

