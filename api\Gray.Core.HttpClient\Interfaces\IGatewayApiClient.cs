using Refit;

namespace Gray.Core.GrayHttpClient.Interfaces
{
    /// <summary>
    /// 网关 API 客户端接口
    /// 用于与 Gray.Core.Gateway 通信
    /// </summary>
    public interface IGatewayApiClient
    {
        /// <summary>
        /// 获取网关健康状态
        /// </summary>
        /// <returns>健康状态</returns>
        [Get("/health")]
        Task<ApiResponse<GatewayHealthStatus>> GetHealthStatusAsync();

        /// <summary>
        /// 获取所有路由信息
        /// </summary>
        /// <returns>路由信息列表</returns>
        [Get("/api/Routes")]
        Task<ApiResponse<List<RouteInfo>>> GetRoutesAsync();

        /// <summary>
        /// 获取指定路由信息
        /// </summary>
        /// <param name="routeId">路由ID</param>
        /// <returns>路由信息</returns>
        [Get("/api/Routes/{routeId}")]
        Task<ApiResponse<RouteInfo>> GetRouteAsync(string routeId);

        /// <summary>
        /// 创建新路由
        /// </summary>
        /// <param name="routeInfo">路由信息</param>
        /// <returns>创建结果</returns>
        [Post("/api/Routes")]
        Task<ApiResponse<RouteInfo>> CreateRouteAsync([Body] RouteInfo routeInfo);

        /// <summary>
        /// 更新路由信息
        /// </summary>
        /// <param name="routeId">路由ID</param>
        /// <param name="routeInfo">路由信息</param>
        /// <returns>更新结果</returns>
        [Put("/api/Routes/{routeId}")]
        Task<ApiResponse<RouteInfo>> UpdateRouteAsync(string routeId, [Body] RouteInfo routeInfo);

        /// <summary>
        /// 删除路由
        /// </summary>
        /// <param name="routeId">路由ID</param>
        /// <returns>删除结果</returns>
        [Delete("/api/Routes/{routeId}")]
        Task<ApiResponse<bool>> DeleteRouteAsync(string routeId);

        /// <summary>
        /// 获取服务发现信息
        /// </summary>
        /// <returns>服务信息列表</returns>
        [Get("/api/Services")]
        Task<ApiResponse<List<ServiceInfo>>> GetServicesAsync();

        /// <summary>
        /// 注册服务
        /// </summary>
        /// <param name="serviceInfo">服务信息</param>
        /// <returns>注册结果</returns>
        [Post("/api/Services")]
        Task<ApiResponse<ServiceInfo>> RegisterServiceAsync([Body] ServiceInfo serviceInfo);

        /// <summary>
        /// 注销服务
        /// </summary>
        /// <param name="serviceId">服务ID</param>
        /// <returns>注销结果</returns>
        [Delete("/api/Services/{serviceId}")]
        Task<ApiResponse<bool>> UnregisterServiceAsync(string serviceId);

        /// <summary>
        /// 获取负载均衡配置
        /// </summary>
        /// <returns>负载均衡配置</returns>
        [Get("/api/LoadBalancer")]
        Task<ApiResponse<LoadBalancerConfig>> GetLoadBalancerConfigAsync();

        /// <summary>
        /// 更新负载均衡配置
        /// </summary>
        /// <param name="config">负载均衡配置</param>
        /// <returns>更新结果</returns>
        [Put("/api/LoadBalancer")]
        Task<ApiResponse<LoadBalancerConfig>> UpdateLoadBalancerConfigAsync([Body] LoadBalancerConfig config);
    }

    /// <summary>
    /// 网关健康状态模型
    /// </summary>
    public class GatewayHealthStatus
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime CheckTime { get; set; }
        public Dictionary<string, object> Details { get; set; } = new();
    }

    /// <summary>
    /// 路由信息模型
    /// </summary>
    public class RouteInfo
    {
        public string RouteId { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public string ServiceName { get; set; } = string.Empty;
        public string DownstreamPathTemplate { get; set; } = string.Empty;
        public string DownstreamScheme { get; set; } = string.Empty;
        public List<string> DownstreamHostAndPorts { get; set; } = new();
        public List<string> UpstreamHttpMethod { get; set; } = new();
        public string UpstreamPathTemplate { get; set; } = string.Empty;
        public int Priority { get; set; }
        public bool IsEnabled { get; set; } = true;
        public Dictionary<string, string> Headers { get; set; } = new();
        public RouteLoadBalancerOptions LoadBalancerOptions { get; set; } = new();
    }

    /// <summary>
    /// 服务信息模型
    /// </summary>
    public class ServiceInfo
    {
        public string ServiceId { get; set; } = string.Empty;
        public string ServiceName { get; set; } = string.Empty;
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; }
        public string Scheme { get; set; } = "http";
        public bool IsHealthy { get; set; } = true;
        public DateTime LastHeartbeat { get; set; }
        public Dictionary<string, string> Tags { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 负载均衡配置模型
    /// </summary>
    public class LoadBalancerConfig
    {
        public string Type { get; set; } = "RoundRobin"; // RoundRobin, LeastConnection, Random
        public int HealthCheckInterval { get; set; } = 30;
        public int HealthCheckTimeout { get; set; } = 5;
        public string HealthCheckPath { get; set; } = "/health";
        public Dictionary<string, object> Options { get; set; } = new();
    }

    /// <summary>
    /// 路由负载均衡选项
    /// </summary>
    public class RouteLoadBalancerOptions
    {
        public string Type { get; set; } = "RoundRobin";
        public string Key { get; set; } = string.Empty;
        public int Expiry { get; set; }
    }
} 