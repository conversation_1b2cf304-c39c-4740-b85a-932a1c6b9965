﻿<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8" />
	<title>WebSocketDemoPlugin</title>
	<style>
		table {
			border: 0
		}

		.commslog-data {
			font-family: <PERSON>solas, Courier New, Courier, monospace;
		}

		.commslog-server {
			background-color: red;
			color: white
		}

		.commslog-client {
			background-color: green;
			color: white
		}
	</style>
</head>
<body>
	<h1>WebSocket Sample Application</h1>
	<p id="stateLabel">Ready to connect...</p>
	<div>
		<label for="connectionUrl">WebSocket Server URL:</label>
		<input id="connectionUrl" />
		<button id="connectButton" type="submit">Connect</button>
	</div>
	<p></p>
	<div>
		<label for="sendMessage">Message to send:</label>
		<input id="sendMessage" disabled />
		<button id="sendButton" type="submit" disabled>Send</button>
		<button id="closeButton" disabled>Close Socket</button>
	</div>

	<h2>Communication Log</h2>
	<table style="width: 800px">
		<thead>
			<tr>
				<td style="width: 100px">From</td>
				<td style="width: 100px">To</td>
				<td>Data</td>
			</tr>
		</thead>
		<tbody id="commsLog">
		</tbody>
	</table>

	<!-- 注意:路径, 前置 /plugins/WebSocketDemoPlugin/ -->
	<script src="/plugins/WebSocketDemoPlugin/js/main.js"></script>
</body>
</html>