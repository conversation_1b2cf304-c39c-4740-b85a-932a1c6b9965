using System;

namespace Gray.Core.Monitoring
{
    public class SqlMonitoringOptions
    {
        // 性能监控阈值配置
        public int SlowQueryThresholdMs { get; set; } = 1000;
        public int ExtremelySlowQueryThresholdMs { get; set; } = 3000;
        
        // 缓存配置
        public int QueryPlanCacheMinutes { get; set; } = 30;
        public int QueryPlanCacheMaxItems { get; set; } = 1000;
        
        // 监控配置
        public bool EnablePerformanceMonitoring { get; set; } = true;
        public bool EnableQueryPlanMonitoring { get; set; } = true;
        public bool EnableErrorTracking { get; set; } = true;
    }
}
